<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Order;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Actions\GetPlatformUsageSummary;
use App\Modules\Gpo\Models\GpoAccount;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $this->gpoAccount = GpoAccount::factory()->create(['name' => 'Test GPO Account']);

    // Create clinic accounts under this GPO
    $this->clinicAccount1 = ClinicAccount::factory()->create([
        'gpo_account_id' => $this->gpoAccount->id,
    ]);

    $this->clinicAccount2 = ClinicAccount::factory()->create([
        'gpo_account_id' => $this->gpoAccount->id,
    ]);

    $this->clinicAccount3 = ClinicAccount::factory()->create([
        'gpo_account_id' => $this->gpoAccount->id,
    ]);

    // Create clinics under these accounts
    $this->clinic1 = Clinic::factory()->create([
        'clinic_account_id' => $this->clinicAccount1->id,
    ]);

    $this->clinic2 = Clinic::factory()->create([
        'clinic_account_id' => $this->clinicAccount2->id,
    ]);

    $this->clinic3 = Clinic::factory()->create([
        'clinic_account_id' => $this->clinicAccount3->id,
    ]);

    // Create a separate GPO account for isolation testing
    $this->otherGpoAccount = GpoAccount::factory()->create(['name' => 'Other GPO Account']);
    $this->otherClinicAccount = ClinicAccount::factory()->create([
        'gpo_account_id' => $this->otherGpoAccount->id,
    ]);
    $this->otherClinic = Clinic::factory()->create([
        'clinic_account_id' => $this->otherClinicAccount->id,
    ]);
});

describe('GetPlatformUsageSummary', function () {
    it('returns correct counts when no clinics have orders', function () {
        $action = new GetPlatformUsageSummary();
        $result = $action->handle($this->gpoAccount->id, Carbon::now()->subDays(15), Carbon::now());

        expect($result['gpo_clinics'])->toBe(3);
        expect($result['active_clinics'])->toBe(0);
        expect($result['active_clinics_percentage'])->toBe(0.0);
    });

    it('returns correct counts when all clinics have recent orders', function () {
        // Create recent orders for all clinics
        Order::factory()->create([
            'clinic_id' => $this->clinic1->id,
            'created_at' => Carbon::now()->subDays(5),
        ]);

        Order::factory()->create([
            'clinic_id' => $this->clinic2->id,
            'created_at' => Carbon::now()->subDays(10),
        ]);

        Order::factory()->create([
            'clinic_id' => $this->clinic3->id,
            'created_at' => Carbon::now()->subDays(15),
        ]);

        $action = new GetPlatformUsageSummary();
        $result = $action->handle($this->gpoAccount->id, Carbon::now()->subDays(15), Carbon::now());

        expect($result['gpo_clinics'])->toBe(3);
        expect($result['active_clinics'])->toBe(3);
        expect($result['active_clinics_percentage'])->toBe(100.0);
    });

    it('returns correct counts when some clinics have recent orders', function () {
        // Create recent orders for only 2 clinics
        Order::factory()->create([
            'clinic_id' => $this->clinic1->id,
            'created_at' => Carbon::now()->subDays(5),
        ]);

        Order::factory()->create([
            'clinic_id' => $this->clinic2->id,
            'created_at' => Carbon::now()->subDays(25),
        ]);

        // Clinic 3 has no orders

        $action = new GetPlatformUsageSummary();
        $result = $action->handle($this->gpoAccount->id, Carbon::now()->subDays(15), Carbon::now());

        expect($result['gpo_clinics'])->toBe(3);
        expect($result['active_clinics'])->toBe(2);
        expect($result['active_clinics_percentage'])->toBe(66.67);
    });

    it('excludes orders older than the threshold', function () {
        // Create old orders (older than 30 days)
        Order::factory()->create([
            'clinic_id' => $this->clinic1->id,
            'created_at' => Carbon::now()->subDays(40),
        ]);

        Order::factory()->create([
            'clinic_id' => $this->clinic2->id,
            'created_at' => Carbon::now()->subDays(50),
        ]);

        // Create one recent order
        Order::factory()->create([
            'clinic_id' => $this->clinic3->id,
            'created_at' => Carbon::now()->subDays(10),
        ]);

        $action = new GetPlatformUsageSummary();
        $result = $action->handle($this->gpoAccount->id, Carbon::now()->subDays(15), Carbon::now());

        expect($result['gpo_clinics'])->toBe(3);
        expect($result['active_clinics'])->toBe(1);
        expect($result['active_clinics_percentage'])->toBe(33.33);
    });

    it('respects custom active days threshold', function () {
        // Create orders at various dates
        Order::factory()->create([
            'clinic_id' => $this->clinic1->id,
            'created_at' => Carbon::now()->subDays(5), // Within 7 days
        ]);

        Order::factory()->create([
            'clinic_id' => $this->clinic2->id,
            'created_at' => Carbon::now()->subDays(10), // Outside 7 days
        ]);

        Order::factory()->create([
            'clinic_id' => $this->clinic3->id,
            'created_at' => Carbon::now()->subDays(15), // Outside 7 days
        ]);

        $action = new GetPlatformUsageSummary();
        $result = $action->handle($this->gpoAccount->id, Carbon::now()->subDays(15), Carbon::now(), 7); // 7-day threshold

        expect($result['gpo_clinics'])->toBe(3);
        expect($result['active_clinics'])->toBe(1);
        expect($result['active_clinics_percentage'])->toBe(33.33);
    });

    it('isolates data between different GPO accounts', function () {
        // Create orders for both GPO accounts
        Order::factory()->create([
            'clinic_id' => $this->clinic1->id,
            'created_at' => Carbon::now()->subDays(5),
        ]);

        Order::factory()->create([
            'clinic_id' => $this->otherClinic->id,
            'created_at' => Carbon::now()->subDays(5),
        ]);

        $action = new GetPlatformUsageSummary();
        $result = $action->handle($this->gpoAccount->id, Carbon::now()->subDays(15), Carbon::now());

        // Should only count clinics from the specified GPO
        expect($result['gpo_clinics'])->toBe(3);
        expect($result['active_clinics'])->toBe(1);
        expect($result['active_clinics_percentage'])->toBe(33.33);
    });

    it('handles edge case with zero clinics', function () {
        // Create a GPO with no clinic accounts
        $emptyGpoAccount = GpoAccount::factory()->create(['name' => 'Empty GPO Account']);

        $action = new GetPlatformUsageSummary();
        $result = $action->handle($emptyGpoAccount->id, Carbon::now()->subDays(15), Carbon::now());

        expect($result['gpo_clinics'])->toBe(0);
        expect($result['active_clinics'])->toBe(0);
        expect($result['active_clinics_percentage'])->toBe(0.0);
    });

    it('handles clinics with multiple recent orders correctly', function () {
        // Create multiple orders for the same clinic
        Order::factory()->create([
            'clinic_id' => $this->clinic1->id,
            'created_at' => Carbon::now()->subDays(5),
        ]);

        Order::factory()->create([
            'clinic_id' => $this->clinic1->id,
            'created_at' => Carbon::now()->subDays(10),
        ]);

        Order::factory()->create([
            'clinic_id' => $this->clinic1->id,
            'created_at' => Carbon::now()->subDays(15),
        ]);

        $action = new GetPlatformUsageSummary();
        $result = $action->handle($this->gpoAccount->id, Carbon::now()->subDays(15), Carbon::now());

        // Clinic should only be counted once even with multiple orders
        expect($result['gpo_clinics'])->toBe(3);
        expect($result['active_clinics'])->toBe(1);
        expect($result['active_clinics_percentage'])->toBe(33.33);
    });
});
