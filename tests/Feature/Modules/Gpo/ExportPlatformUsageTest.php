<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Actions\ExportPlatformUsageCsv;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Services\PlatformUsageCsvExportService;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $this->gpoAccount = GpoAccount::factory()->create(['name' => 'Test GPO Account']);

    $this->clinicAccount1 = ClinicAccount::factory()->create([
        'gpo_account_id' => $this->gpoAccount->id,
    ]);

    $this->clinic1 = Clinic::factory()->create([
        'clinic_account_id' => $this->clinicAccount1->id,
        'name' => 'Test Clinic 1',
        'created_at' => Carbon::parse('2025-01-10'),
    ]);

    $this->user1 = User::factory()->create([
        'account_id' => $this->clinicAccount1->id,
        'name' => 'Test User 1',
        'email' => '<EMAIL>',
    ]);

    $this->clinic1->users()->attach($this->user1);

    $this->vendor1 = Vendor::factory()->create(['name' => 'Test Vendor 1']);
    $this->productOffer1 = ProductOffer::factory()->create(['vendor_id' => $this->vendor1->id]);

    $this->gpoAccount->recommendedVendors()->attach([
        $this->vendor1->id => ['order' => 1],
    ]);
});

describe('ExportPlatformUsageCsv Action', function () {
    it('exports CSV with correct headers and content type', function () {
        $action = new ExportPlatformUsageCsv(new PlatformUsageCsvExportService());

        $startDate = Carbon::parse('2025-01-01');
        $endDate = Carbon::parse('2025-01-31');

        $response = $action->handle($this->gpoAccount->id, $startDate, $endDate);

        expect($response)->toBeInstanceOf(Symfony\Component\HttpFoundation\StreamedResponse::class);
        expect($response->headers->get('Content-Type'))->toBe('text/csv; charset=UTF-8');

        $contentDisposition = $response->headers->get('Content-Disposition');
        expect($contentDisposition)->toContain('platform_usage_2025-01-01_to_2025-01-31_');
    });
});

describe('PlatformUsageCsvExportService', function () {
    it('generates CSV with correct headers', function () {
        $clinics = collect([$this->clinic1]);
        $service = new PlatformUsageCsvExportService();

        $startDate = Carbon::parse('2025-01-01');
        $endDate = Carbon::parse('2025-01-31');

        $response = $service->exportToCsv($clinics, $startDate, $endDate, 30);

        ob_start();
        $response->sendContent();
        $csvContent = ob_get_clean();

        $lines = explode("\n", mb_trim($csvContent));
        $headers = str_getcsv($lines[0]);

        expect($headers)->toBe([
            'Clinic Name',
            'Unique Users',
            'Last Active Date',
            'Average Order Total ($)',
            'Avg # All Vendors',
            'Avg # Preferred Vendors',
            'Average Session Time (min)',
            'Status',
        ]);
    });

    it('handles clinics with no orders correctly', function () {
        $clinics = collect([$this->clinic1]);
        $service = new PlatformUsageCsvExportService();

        $startDate = Carbon::parse('2025-01-01');
        $endDate = Carbon::parse('2025-01-31');

        $response = $service->exportToCsv($clinics, $startDate, $endDate, 30);

        ob_start();
        $response->sendContent();
        $csvContent = ob_get_clean();

        $lines = explode("\n", mb_trim($csvContent));
        $clinicData = str_getcsv($lines[1]);

        expect($clinicData[0])->toBe('Test Clinic 1');
        expect($clinicData[1])->toBe('1'); // Unique Users
        expect($clinicData[2])->toBe('Never'); // Last Active Date
        expect($clinicData[3])->toBe('0.00'); // Average Order Total
        expect($clinicData[7])->toBe('Inactive'); // Status
    });

    it('calculates metrics correctly with orders', function () {
        $order1 = Order::factory()->create([
            'clinic_id' => $this->clinic1->id,
            'user_id' => $this->user1->id,
            'created_at' => Carbon::now()->subDays(5),
        ]);

        OrderItem::factory()->create([
            'order_id' => $order1->id,
            'product_offer_id' => $this->productOffer1->id,
            'quantity' => 1,
            'price' => 10000,
        ]);

        $clinics = collect([$this->clinic1]);
        $service = new PlatformUsageCsvExportService();

        $startDate = Carbon::now()->subDays(10);
        $endDate = Carbon::now();

        $response = $service->exportToCsv($clinics, $startDate, $endDate, 30);

        ob_start();
        $response->sendContent();
        $csvContent = ob_get_clean();

        $lines = explode("\n", mb_trim($csvContent));
        $clinicData = str_getcsv($lines[1]);

        expect($clinicData[0])->toBe('Test Clinic 1');
        expect($clinicData[1])->toBe('1'); // Unique Users
        expect($clinicData[2])->toBe($order1->created_at->format('Y-m-d')); // Last Active Date
        expect($clinicData[3])->toBe('100.00'); // Average Order Total
        expect($clinicData[7])->toBe('Active'); // Status (recent order)
    });
});
