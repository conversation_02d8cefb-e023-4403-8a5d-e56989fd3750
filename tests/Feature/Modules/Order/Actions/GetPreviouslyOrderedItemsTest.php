<?php

declare(strict_types=1);

use App\Enums\OrderItemStatus;
use App\Enums\ProductStockStatus;
use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Order\Actions\GetPreviouslyOrderedItems;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class GetPreviouslyOrderedItemsTest extends TestCase
{
    use RefreshDatabase;

    protected Clinic $clinic;

    protected Vendor $connectedVendor;

    protected Vendor $disconnectedVendor;

    protected Product $connectedProduct;

    protected Product $disconnectedProduct;

    protected ProductOffer $connectedProductOffer;

    protected ProductOffer $disconnectedProductOffer;

    protected GetPreviouslyOrderedItems $action;

    protected function setUp(): void
    {
        parent::setUp();

        $this->clinic = Clinic::factory()->create();

        // Create vendors
        $this->connectedVendor = Vendor::factory()->create(['name' => 'Connected Vendor']);
        $this->disconnectedVendor = Vendor::factory()->create(['name' => 'Disconnected Vendor']);

        // Create products
        $this->connectedProduct = Product::factory()->create(['name' => 'Connected Product']);
        $this->disconnectedProduct = Product::factory()->create(['name' => 'Disconnected Product']);

        // Create product offers
        $this->connectedProductOffer = ProductOffer::factory()->create([
            'product_id' => $this->connectedProduct->id,
            'vendor_id' => $this->connectedVendor->id,
            'stock_status' => ProductStockStatus::InStock,
            'increments' => 1,
        ]);

        $this->disconnectedProductOffer = ProductOffer::factory()->create([
            'product_id' => $this->disconnectedProduct->id,
            'vendor_id' => $this->disconnectedVendor->id,
            'stock_status' => ProductStockStatus::InStock,
            'increments' => 1,
        ]);

        // Create integration connection for connected vendor
        IntegrationConnection::factory()->create([
            'clinic_id' => $this->clinic->id,
            'vendor_id' => $this->connectedVendor->id,
            'status' => IntegrationConnectionStatus::Connected,
        ]);

        // Create integration connection for disconnected vendor (but with disconnected status)
        IntegrationConnection::factory()->create([
            'clinic_id' => $this->clinic->id,
            'vendor_id' => $this->disconnectedVendor->id,
            'status' => IntegrationConnectionStatus::Disconnected,
        ]);

        $this->action = new GetPreviouslyOrderedItems();
    }

    public function test_returns_only_items_from_connected_vendors(): void
    {
        // Create orders with both connected and disconnected vendor products
        $connectedOrder = Order::factory()->for($this->clinic)->create();
        $disconnectedOrder = Order::factory()->for($this->clinic)->create();

        // Add items to orders
        OrderItem::factory()->create([
            'order_id' => $connectedOrder->id,
            'product_offer_id' => $this->connectedProductOffer->id,
            'quantity' => 2,
            'price' => 1500,
            'status' => OrderItemStatus::Delivered,
        ]);

        OrderItem::factory()->create([
            'order_id' => $disconnectedOrder->id,
            'product_offer_id' => $this->disconnectedProductOffer->id,
            'quantity' => 1,
            'price' => 2000,
            'status' => OrderItemStatus::Delivered,
        ]);

        $result = $this->action->handle($this->clinic->id, 'Product', 3);

        // Should only return items from connected vendor
        $this->assertCount(1, $result);
        $this->assertEquals('Connected Product', $result->first()->product_name);
        $this->assertEquals('Connected Vendor', $result->first()->vendor_name);
        $this->assertEquals($this->connectedProductOffer->id, $result->first()->product_offer_id);
    }
}
