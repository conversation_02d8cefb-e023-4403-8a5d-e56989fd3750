<?php

declare(strict_types=1);

use App\Modules\Integration\Enums\IntegrationEventStatus;
use App\Modules\Integration\Models\IntegrationEvent;
use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Services\Vendor\Exceptions\ApiAuthenticationException;
use App\Modules\Order\Services\Vendor\Exceptions\ApiException;
use App\Modules\Order\Services\Vendor\Mwi\MwiHttpClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;

uses(RefreshDatabase::class);

describe('MwiHttpClient Integration Events', function () {
    beforeEach(function () {
        $this->session = IntegrationSession::factory()->create();
        $this->subject = (object) ['id' => (string) Str::uuid()];
    });

    it('logs integration events with correct data', function () {
        $client = new MwiHttpClient('invalid_username', 'invalid_password');

        // This should fail due to invalid credentials but still log events
        try {
            $client->getAccountId(
                $this->session,
                'test_get_account_id',
                $this->subject
            );
        } catch (ApiAuthenticationException|ApiException $e) {
            // Expected to fail - this is what we want
        }

        // Check that integration events were logged
        $events = IntegrationEvent::where('integration_session_id', $this->session->id)->get();
        expect($events)->toHaveCount(2); // One initiated, one error

        $initiatedEvent = $events->where('action', 'test_get_account_id_initiated')->first();
        expect($initiatedEvent)->not->toBeNull()
            ->and($initiatedEvent->status)->toBe(IntegrationEventStatus::Success->value)
            ->and($initiatedEvent->metadata)->toHaveKey('endpoint')
            ->and($initiatedEvent->metadata)->toHaveKey('method')
            ->and($initiatedEvent->metadata)->toHaveKey('body');

        $errorEvent = $events->where('action', 'test_get_account_id')->first();
        expect($errorEvent)->not->toBeNull()
            ->and($errorEvent->status)->toBe(IntegrationEventStatus::Error->value);
    });
});
