<?php

declare(strict_types=1);

use App\Modules\Integration\Enums\IntegrationEventStatus;
use App\Modules\Integration\Models\IntegrationEvent;
use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Services\Vendor\Http\BaseHttpClient;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

// Create a concrete implementation for testing
final class TestHttpClient extends BaseHttpClient
{
    public function __construct()
    {
        parent::__construct();
        $this->configureClient();
    }

    protected function configureClient(): void
    {
        $this->client = Http::withOptions([
            'base_uri' => 'https://test.example.com',
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
        ]);
    }
}

describe('BaseHttpClient Integration Events', function () {
    beforeEach(function () {
        $this->session = IntegrationSession::factory()->create();
        $this->subject = (object) ['id' => (string) Str::uuid()];
        $this->client = new TestHttpClient();
    });

    it('logs integration events with correct data for POST requests', function () {
        try {
            $this->client->post(
                '/test',
                ['data' => 'test'],
                $this->session,
                'test_post',
                $this->subject
            );
        } catch (Exception $e) {
            // Expected to fail due to invalid host
        }

        $events = IntegrationEvent::where('integration_session_id', $this->session->id)->get();
        expect($events)->toHaveCount(2); // One initiated, one error

        $initiatedEvent = $events->where('action', 'test_post_initiated')->first();
        expect($initiatedEvent)->not->toBeNull()
            ->and($initiatedEvent->status)->toBe(IntegrationEventStatus::Success->value)
            ->and($initiatedEvent->metadata['endpoint'])->toBe('/test')
            ->and($initiatedEvent->metadata['method'])->toBe('POST')
            ->and($initiatedEvent->metadata['body'])->toBe(['data' => 'test']);
    });
});
