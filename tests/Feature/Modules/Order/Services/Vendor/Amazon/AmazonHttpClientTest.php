<?php

declare(strict_types=1);

use App\Modules\Integration\Enums\IntegrationEventStatus;
use App\Modules\Integration\Models\IntegrationEvent;
use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Services\Vendor\Amazon\AmazonHttpClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

uses(RefreshDatabase::class);

describe('AmazonHttpClient Integration Events', function () {
    beforeEach(function () {
        $this->session = IntegrationSession::factory()->create();
        $this->subject = (object) ['id' => (string) Str::uuid()];
    });

    it('logs integration events with correct data', function () {
        // Mock the HTTP response to avoid authentication issues
        Http::fake([
            'https://api.amazon.com/auth/O2/token' => Http::response([
                'access_token' => 'fake_token',
                'token_type' => 'bearer',
                'expires_in' => 3600,
            ], 200),
            'https://api.amazon.com/*' => Http::response(['error' => 'test'], 400),
        ]);

        $client = new AmazonHttpClient('fake_refresh_token');

        try {
            $client->placeOrder(
                ['test' => 'data'],
                $this->session,
                'test_place_order',
                $this->subject
            );
        } catch (Exception $e) {
            // Expected to fail due to mocked 400 response
        }

        // Check that integration events were logged
        $events = IntegrationEvent::where('integration_session_id', $this->session->id)->get();
        expect($events)->toHaveCount(2); // One initiated, one error

        $initiatedEvent = $events->where('action', 'test_place_order_initiated')->first();
        expect($initiatedEvent)->not->toBeNull()
            ->and($initiatedEvent->status)->toBe(IntegrationEventStatus::Success->value)
            ->and($initiatedEvent->metadata)->toHaveKey('endpoint')
            ->and($initiatedEvent->metadata)->toHaveKey('method')
            ->and($initiatedEvent->metadata)->toHaveKey('body');

        $errorEvent = $events->where('action', 'test_place_order')->first();
        expect($errorEvent)->not->toBeNull()
            ->and($errorEvent->status)->toBe(IntegrationEventStatus::Error->value);
    });
});
