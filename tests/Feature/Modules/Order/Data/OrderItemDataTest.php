<?php

declare(strict_types=1);

use App\Enums\OrderItemStatus;
use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Order\Data\OrderItemData;

beforeEach(function () {
    $this->vendor = Vendor::factory()->create();
    $this->product = Product::factory()->create();
    $this->order = Order::factory()->create();
});

test('OrderItemData includes offers when product has active offers', function () {
    // Create a clinic and connect it to the vendor
    $clinic = Clinic::factory()->create();
    $this->order->update(['clinic_id' => $clinic->id]);

    // Create vendor connection
    IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $this->vendor->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    // Create an active product offer
    $productOffer = ProductOffer::factory()->create([
        'product_id' => $this->product->id,
        'vendor_id' => $this->vendor->id,
        'deactivated_at' => null,
    ]);

    $orderItem = OrderItem::factory()->create([
        'order_id' => $this->order->id,
        'product_offer_id' => $productOffer->id,
        'status' => OrderItemStatus::Pending,
    ]);

    $orderItemData = OrderItemData::fromModel($orderItem);

    expect($orderItemData->product->offers)->toHaveCount(1);
    expect($orderItemData->product->offers[0]['id'])->toBe($productOffer->id);
    expect($orderItemData->product->offers[0])->toHaveKey('unitOfMeasure');
});

test('OrderItemData includes empty offers when product has no active offers', function () {
    // Create a clinic and connect it to the vendor
    $clinic = Clinic::factory()->create();
    $this->order->update(['clinic_id' => $clinic->id]);

    // Create vendor connection
    IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $this->vendor->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    // Create a deactivated product offer
    $productOffer = ProductOffer::factory()->create([
        'product_id' => $this->product->id,
        'vendor_id' => $this->vendor->id,
        'deactivated_at' => now(),
    ]);

    $orderItem = OrderItem::factory()->create([
        'order_id' => $this->order->id,
        'product_offer_id' => $productOffer->id,
        'status' => OrderItemStatus::Pending,
    ]);

    // Load the order item with the same relationship loading as the controller
    $orderItem->load([
        'product',
        'product.productOffers' => function ($query) {
            $query->active();
        },
    ]);

    $orderItemData = OrderItemData::fromModel($orderItem);

    expect($orderItemData->product->offers)->toBeEmpty();
});

test('OrderItemData includes empty offers when product has no offers at all', function () {
    // Create a clinic
    $clinic = Clinic::factory()->create();
    $this->order->update(['clinic_id' => $clinic->id]);

    // Create a product with no offers
    $productWithNoOffers = Product::factory()->create();

    $orderItem = OrderItem::factory()->create([
        'order_id' => $this->order->id,
        'product_offer_id' => ProductOffer::factory()->create()->id,
        'status' => OrderItemStatus::Pending,
    ]);

    // Manually set the product relationship to our product with no offers
    $orderItem->setRelation('product', $productWithNoOffers);

    $orderItemData = OrderItemData::fromModel($orderItem);

    expect($orderItemData->product->offers)->toBeEmpty();
});

test('OrderItemData includes offers regardless of vendor connection status', function () {
    $clinic = Clinic::factory()->create();

    // Create a vendor that is not connected to the clinic
    $unconnectedVendor = Vendor::factory()->create();

    // Create an active product offer from unconnected vendor
    $productOffer = ProductOffer::factory()->create([
        'product_id' => $this->product->id,
        'vendor_id' => $unconnectedVendor->id,
        'deactivated_at' => null,
    ]);

    $orderItem = OrderItem::factory()->create([
        'order_id' => $this->order->id,
        'product_offer_id' => $productOffer->id,
        'status' => OrderItemStatus::Pending,
    ]);

    // Set the order's clinic to our test clinic
    $this->order->update(['clinic_id' => $clinic->id]);

    // Test that offers are included regardless of vendor connection status
    $orderItemData = OrderItemData::fromModel($orderItem);
    expect($orderItemData->product->offers)->toHaveCount(1);
    expect($orderItemData->product->offers[0]['id'])->toBe($productOffer->id);
    expect($orderItemData->product->offers[0])->toHaveKey('unitOfMeasure');
});

test('OrderItemData includes unit of measure in offers', function () {
    // Create a clinic and connect it to the vendor
    $clinic = Clinic::factory()->create();
    $this->order->update(['clinic_id' => $clinic->id]);

    // Create vendor connection
    IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $this->vendor->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    // Create an active product offer with specific unit of measure
    $productOffer = ProductOffer::factory()->create([
        'product_id' => $this->product->id,
        'vendor_id' => $this->vendor->id,
        'deactivated_at' => null,
        'unit_of_measure' => 'Each',
    ]);

    $orderItem = OrderItem::factory()->create([
        'order_id' => $this->order->id,
        'product_offer_id' => $productOffer->id,
        'status' => OrderItemStatus::Pending,
    ]);

    $orderItemData = OrderItemData::fromModel($orderItem);

    expect($orderItemData->product->offers)->toHaveCount(1);
    expect($orderItemData->product->offers[0]['id'])->toBe($productOffer->id);
    expect($orderItemData->product->offers[0]['unitOfMeasure'])->toBe('Each');
});
