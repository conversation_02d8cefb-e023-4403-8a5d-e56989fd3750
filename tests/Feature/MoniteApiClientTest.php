<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Services\MoniteTokenManager;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

final class MoniteApiClientTest extends TestCase
{
    public function test_monite_api_client_can_be_resolved(): void
    {
        // Mock the configuration
        Config::set('monite.environment', 'sandbox');
        Config::set('monite.api_version', '2023-09-01');
        Config::set('monite.credentials.client_id', 'test-client-id');
        Config::set('monite.credentials.client_secret', 'test-client-secret');
        Config::set('monite.environments.sandbox.api_base_url', 'https://api.sandbox.monite.com');

        $client = app(MoniteApiClientInterface::class);

        $this->assertInstanceOf(MoniteApiClientInterface::class, $client);
    }

    public function test_monite_token_manager_can_be_resolved(): void
    {
        // Mock the configuration
        Config::set('monite.environment', 'sandbox');
        Config::set('monite.api_version', '2023-09-01');
        Config::set('monite.credentials.client_id', 'test-client-id');
        Config::set('monite.credentials.client_secret', 'test-client-secret');
        Config::set('monite.environments.sandbox.api_base_url', 'https://api.sandbox.monite.com');

        $tokenManager = app(MoniteTokenManager::class);

        $this->assertInstanceOf(MoniteTokenManager::class, $tokenManager);
    }
}
