<?php

declare(strict_types=1);

namespace App\Data\Transformers;

use Brick\Math\BigNumber;
use Brick\Money\Money;
use InvalidArgumentException;
use Spatie\LaravelData\Support\DataProperty;
use Spatie\LaravelData\Support\Transformation\TransformationContext;
use Spatie\LaravelData\Transformers\Transformer;

final class MoneyTransformer implements Transformer
{
    public function transform(DataProperty $property, mixed $value, TransformationContext $context): BigNumber
    {
        if (is_null($value)) {
            return Money::ofMinor(0, 'USD')->getAmount();
        }

        if ($value instanceof Money) {
            return $value->getAmount();
        }

        throw new InvalidArgumentException('The value must be an instance of \Brick\Money\Money.');
    }
}
