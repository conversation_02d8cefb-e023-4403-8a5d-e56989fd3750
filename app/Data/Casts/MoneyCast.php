<?php

declare(strict_types=1);

namespace App\Data\Casts;

use Brick\Money\Money;
use Spatie\LaravelData\Casts\Cast;
use Spatie\LaravelData\Support\Creation\CreationContext;
use Spatie\LaravelData\Support\DataProperty;

final class MoneyCast implements Cast
{
    public function cast(DataProperty $property, mixed $value, array $properties, CreationContext $context): Money
    {
        if (is_null($value)) {
            return Money::ofMinor(0, 'USD');
        }

        return Money::ofMinor($value, 'USD');
    }
}
