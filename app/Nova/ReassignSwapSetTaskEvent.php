<?php

declare(strict_types=1);

namespace App\Nova;

use Illuminate\Support\Str;
use <PERSON><PERSON>\Nova\Fields\Badge;
use <PERSON>vel\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

final class ReassignSwapSetTaskEvent extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\ReassignSwapSetTaskEvent>
     */
    public static $model = \App\Models\ReassignSwapSetTaskEvent::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'type',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<int, \Laravel\Nova\Fields\Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Badge::make('Type', 'type')
                ->map([
                    'no_new_parent_product_id' => 'warning',
                    'invalid_child_product_id' => 'warning',
                    'invalid_new_parent_product_id' => 'warning',
                    'product_offer_not_found' => 'warning',
                    'target_product_not_found' => 'warning',
                    'conflict' => 'danger',
                    'noop' => 'success',
                    'offer_reassigned' => 'success',
                    'failed_to_reassign_offer' => 'danger',
                ])
                ->label(fn ($value) => Str::title($value))
                ->readonly(),

            Code::make('Meta', 'meta')
                ->json()
                ->readonly(),
        ];
    }
}
