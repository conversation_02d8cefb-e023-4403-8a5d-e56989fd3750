<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Enums\OrderItemStatus;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;
use Stringable;

final class UpdateOrderItemStatus extends Action
{
    use InteractsWithQueue;
    use Queueable;

    /**
     * The displayable name of the action.
     *
     * @var Stringable|string
     */
    public $name = 'Update status';

    /**
     * Perform the action on the given models.
     *
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        \App\Models\OrderItem::whereKey($models->pluck('id'))->update([
            'updated_at' => now(),
            'status' => $fields['status'],
        ]);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array<int, \Laravel\Nova\Fields\Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Select::make('Status', 'status')
                ->default(OrderItemStatus::Delivered->value)
                ->options(OrderItemStatus::options())
                ->displayUsingLabels(),
        ];
    }
}
