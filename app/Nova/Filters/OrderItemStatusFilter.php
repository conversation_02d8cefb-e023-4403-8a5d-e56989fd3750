<?php

declare(strict_types=1);

namespace App\Nova\Filters;

use App\Enums\OrderItemStatus;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class OrderItemStatusFilter extends Filter
{
    /** @var string */
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('status', $value);
    }

    public function options(NovaRequest $request)
    {
        return array_flip(OrderItemStatus::options());
    }

    public function name()
    {
        return 'Status';
    }
}
