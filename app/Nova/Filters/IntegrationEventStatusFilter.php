<?php

declare(strict_types=1);

namespace App\Nova\Filters;

use App\Modules\Integration\Enums\IntegrationEventStatus;
use Laravel\Nova\Filters\Filter;
use Laravel\Nova\Http\Requests\NovaRequest;

final class IntegrationEventStatusFilter extends Filter
{
    /** @var string */
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('status', $value);
    }

    public function options(NovaRequest $request)
    {
        return [
            'Success' => IntegrationEventStatus::Success->value,
            'Error' => IntegrationEventStatus::Error->value,
        ];
    }

    public function name()
    {
        return 'Status';
    }
}
