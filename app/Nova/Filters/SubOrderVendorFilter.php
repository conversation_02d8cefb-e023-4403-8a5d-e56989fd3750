<?php

declare(strict_types=1);

namespace App\Nova\Filters;

use App\Models\Vendor;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

final class SubOrderVendorFilter extends Filter
{
    /** @var string */
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('vendor_id', $value);
    }

    public function options(NovaRequest $request)
    {
        return Vendor::query()
            ->orderBy('name')
            ->pluck('id', 'name')
            ->toArray();
    }

    public function name()
    {
        return 'Vendor';
    }
}
