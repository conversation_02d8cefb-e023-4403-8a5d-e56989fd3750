<?php

declare(strict_types=1);

namespace App\Actions;

use App\Models\User;

final class CreateUser
{
    /**
     * Handle the user creation process.
     */
    public function handle(array $data): User
    {
        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => bcrypt($data['password']),
        ]);

        return $user;
    }
}
