<?php

declare(strict_types=1);

namespace App\Actions\Clinics;

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Events\ConnectingIntegration;
use App\Modules\Integration\Models\IntegrationConnection;

final class ConnectToVendor
{
    /**
     * Connect the clinic to a vendor.
     */
    public function handle(Clinic $clinic, array $data): void
    {
        /** @var Vendor $vendor */
        $vendor = Vendor::findOrFail($data['vendor_id']);

        IntegrationConnection::query()
            ->updateOrCreate([
                'clinic_id' => $clinic->id,
                'vendor_id' => $vendor->id,
            ], [
                'credentials' => $data['credentials'],
                'status' => $vendor->can(IntegrationPoint::SyncProductCatalog) ? IntegrationConnectionStatus::Connecting : IntegrationConnectionStatus::Connected,
            ]);

        ConnectingIntegration::dispatch($clinic->id, $vendor->id);
    }
}
