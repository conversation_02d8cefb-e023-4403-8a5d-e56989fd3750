<?php

declare(strict_types=1);

namespace App\Actions;

use App\Enums\AddressType;
use App\Models\Clinic;
use App\Modules\Integration\Enums\IntegrationConnectionRole;
use Illuminate\Support\Facades\DB;

final class UpdateClinic
{
    /**
     * Handle updating an clinic.
     */
    public function handle(Clinic $clinic, array $data): Clinic
    {
        return DB::transaction(function () use ($clinic, $data) {
            $this->updateIntegrationConnection($clinic, $data);
            $this->updateClinicDetails($clinic, $data);
            $this->updateClinicAddress($clinic, $data['address'] ?? []);

            return $clinic->fresh();
        });
    }

    /**
     * Update the clinic's details.
     */
    private function updateClinicDetails(Clinic $clinic, array $data): void
    {
        unset($data['primary_distributor_id'], $data['secondary_distributor_id'], $data['preferred_manufacturer_ids']);
        $clinic->update(array_diff_key($data, ['address' => null]));
    }

    /**
     * Update or create the clinic's address.
     */
    private function updateClinicAddress(Clinic $clinic, array $data): void
    {
        if (! empty($data)) {
            $clinic->billingAddress()->updateOrCreate(['type' => AddressType::Billing], $data);
            $clinic->shippingAddress()->updateOrCreate(['type' => AddressType::Shipping], $data);
        }
    }

    private function updateIntegrationConnection(Clinic $clinic, array $data): void
    {
        $this->updateDistributorRole($clinic, $data, 'primary_distributor_id', IntegrationConnectionRole::PrimaryDistributor);
        $this->updateDistributorRole($clinic, $data, 'secondary_distributor_id', IntegrationConnectionRole::SecondaryDistributor);
        $this->updatePreferredManufacturers($clinic, $data);
    }

    private function updateDistributorRole(Clinic $clinic, array $data, string $key, IntegrationConnectionRole $role): void
    {
        if (! array_key_exists($key, $data)) {
            return;
        }

        $clinic->integrationConnections()
            ->where('role', $role)
            ->update(['role' => null]);

        $clinic->integrationConnections()
            ->where('vendor_id', $data[$key])
            ->update(['role' => $data[$key] ? $role : null]);
    }

    private function updatePreferredManufacturers(Clinic $clinic, array $data): void
    {
        if (! array_key_exists('preferred_manufacturer_ids', $data)) {
            return;
        }

        $clinic->integrationConnections()
            ->where('role', IntegrationConnectionRole::PreferredManufacturer)
            ->update(['role' => null]);

        if (! empty($data['preferred_manufacturer_ids'])) {
            $clinic->integrationConnections()
                ->whereIn('vendor_id', $data['preferred_manufacturer_ids'])
                ->update(['role' => IntegrationConnectionRole::PreferredManufacturer]);
        }
    }
}
