<?php

declare(strict_types=1);

namespace App\Actions\Products;

use App\Models\Clinic;
use App\Modules\Product\Queries\ProductSearchQueryBuilder;
use Illuminate\Pagination\LengthAwarePaginator;

final class SearchProducts
{
    public function __construct(
        private readonly SearchEngine $action
    ) {}

    public function handle(
        Clinic $clinic,
        string $searchTerm,
        array $filter = []
    ): LengthAwarePaginator {

        [$products, $productIds] = $this->action->handle(
            $clinic,
            $searchTerm,
            $filter
        ) ?? [collect(), []];

        if (! $products || $productIds === []) {
            return new LengthAwarePaginator([], 0, 15);
        }

        return (new ProductSearchQueryBuilder($products, $productIds))->jsonPaginate();
    }
}
