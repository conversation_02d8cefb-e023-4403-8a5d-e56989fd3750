<?php

declare(strict_types=1);

namespace App\Actions\Products;

use App\Models\Clinic;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\RebateEstimate;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;

final class GetRebateEstimateByProductOffer
{
    public function handle(string $clinicId, string $productOfferId): ?RebateEstimate
    {
        $clinic = Clinic::with(['account'])->find($clinicId);

        if (! $clinic || ! $clinic->account) {
            return null;
        }

        $gpoId = $clinic->account->gpo_account_id;

        if (is_null($gpoId)) {
            return null;
        }

        $today = Carbon::today();

        return Cache::flexible(
            "estimate-rebate:{$clinic->id}:{$gpoId}:{$productOfferId}",
            [30, 60],
            fn () => RebateEstimate::query()
                ->with(['promotion', 'promotion.vendor'])
                ->where('clinic_id', $clinic->id)
                ->whereHas('promotion', function (Builder $query) use ($productOfferId, $gpoId, $today) {
                    $query
                        ->whereHas('productOffers', function (Builder $query) use ($productOfferId) {
                            $query->where('product_offer_id', $productOfferId);
                        })
                        ->where('promotionable_type', GpoAccount::class)
                        ->where('promotionable_id', $gpoId)
                        ->where('type', PromotionType::Rebate)
                        ->where('status', PromotionStatus::Active)
                        ->where('started_at', '<=', $today)
                        ->where('ended_at', '>=', $today);
                })->first()
        );
    }
}
