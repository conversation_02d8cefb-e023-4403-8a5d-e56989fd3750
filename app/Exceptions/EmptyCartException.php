<?php

declare(strict_types=1);

namespace App\Exceptions;

use Illuminate\Validation\ValidationException;

final class EmptyCartException extends ValidationException
{
    /**
     * Create a new exception instance.
     */
    public function __construct()
    {
        $exception = ValidationException::withMessages(['cart' => [__('The cart is empty.')]]);

        parent::__construct($exception->validator, $exception->response, $exception->errorBag);
    }
}
