<?php

declare(strict_types=1);

namespace App\Exceptions;

use RuntimeException;
use Throwable;

final class VendorServiceError extends RuntimeException
{
    /**
     * Create a new vendor service exception.
     */
    public function __construct(
        string $message,
        public readonly array $context = [],
        ?Throwable $previous = null
    ) {
        parent::__construct($message, 0, $previous);
    }
}
