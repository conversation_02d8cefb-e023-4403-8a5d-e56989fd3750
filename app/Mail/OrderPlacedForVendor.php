<?php

declare(strict_types=1);

namespace App\Mail;

use App\Models\OrderItem;
use App\Models\SubOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

final class OrderPlacedForVendor extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(public SubOrder $suborder)
    {
        //
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $order = $this->suborder->order;
        $orderNumber = $order->order_number;
        $orderTotal = '$'.number_format($this->suborder->total_price / 100, 2);
        $orderItemsCount = $this->suborder->items->count();

        return new Envelope(
            subject: "Highfive Order {$orderNumber}: {$orderTotal} - {$orderItemsCount} items",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $order = $this->suborder->order;

        return new Content(
            markdown: 'mail.order-placed-for-vendor',
            with: [
                'customerName' => $order->clinic->name,
                'orderNumber' => $order->order_number,
                'orderDate' => $order->created_at->format('M d, Y'),
                'shippingAddress' => "{$order->shippingAddress->street}\\\r\n{$order->shippingAddress->city}\\\r\n{$order->shippingAddress->state}\\\r\n{$order->shippingAddress->postal_code}",
                'orderTotal' => '$'.number_format($this->suborder->total_price / 100, 2),
                'orderItemsCount' => $this->suborder->items->count(),
                'orderItems' => $this->suborder->items->map(fn (OrderItem $item) => [
                    'name' => $item->productOffer->name,
                    'vendorSku' => $item->productOffer->vendor_sku,
                    'price' => '$'.number_format($item->price / 100, 2),
                    'quantity' => $item->quantity,
                    'totalPrice' => '$'.number_format($item->total_price / 100, 2),
                ]),
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
