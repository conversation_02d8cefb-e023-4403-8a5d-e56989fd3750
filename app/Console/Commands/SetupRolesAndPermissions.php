<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Modules\Account\Models\Permission;
use App\Modules\Account\Models\Role;
use App\Modules\User\Enums\UserPermission;
use App\Modules\User\Enums\UserRole;
use Illuminate\Console\Command;

final class SetupRolesAndPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:setup-roles-and-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to set up user roles and permissions for the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userRoles = UserRole::cases();
        foreach ($userRoles as $rawRole) {
            Role::updateOrCreate(['name' => $rawRole->value], ['name' => $rawRole->value]);
        }

        $userPermissions = UserPermission::cases();
        foreach ($userPermissions as $rawPermission) {
            Permission::updateOrCreate(['name' => $rawPermission->value], ['name' => $rawPermission->value]);
        }

        $this->info('Roles and permissions have been set up successfully.');
    }
}
