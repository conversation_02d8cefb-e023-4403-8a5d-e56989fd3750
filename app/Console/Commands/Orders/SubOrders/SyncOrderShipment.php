<?php

declare(strict_types=1);

namespace App\Console\Commands\Orders\SubOrders;

use App\Enums\OrderItemStatus;
use App\Models\SubOrder;
use App\Models\Vendor;
use App\Modules\Order\Jobs\Vendor\SyncShipmentFromVendor;
use Illuminate\Console\Command;

final class SyncOrderShipment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vendor:sync-order-shipment {--statuses=} {--days=30 : Number of days in the past to process orders}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync vendor order shipment status';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $statuses = $this->option('statuses');
        if ($statuses) {
            $statuses = explode(',', $statuses);
        } else {
            $statuses = OrderItemStatus::transient();
        }

        $days = (int) $this->option('days');
        $fromDate = now()->subDays($days);

        $vendors = Vendor::query()
            ->whereIn('key', ['AMZN', 'MWIX'])
            ->isEnabled()
            ->get();

        if ($vendors->isEmpty()) {
            $this->error('No enabled vendors found');

            return;
        }

        foreach ($vendors as $vendor) {
            $this->info("Processing vendor: {$vendor->key}");

            $suborders = SubOrder::query()
                ->select('sub_orders.*')
                ->where('vendor_id', $vendor->id)
                ->join('orders', 'sub_orders.order_id', '=', 'orders.id')
                ->join('order_items', 'orders.id', '=', 'order_items.order_id')
                ->whereIn('order_items.status', $statuses)
                ->where('sub_orders.created_at', '>=', $fromDate)
                ->hasValidClinicConnection($vendor->id)
                ->distinct()
                ->get();

            $this->info("Found {$suborders->count()} suborders to sync");

            $suborders->each(fn (SubOrder $suborder) => SyncShipmentFromVendor::dispatch($suborder));
        }
    }
}
