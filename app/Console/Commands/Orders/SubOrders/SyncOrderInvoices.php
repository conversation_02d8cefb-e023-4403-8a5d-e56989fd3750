<?php

declare(strict_types=1);

namespace App\Console\Commands\Orders\SubOrders;

use App\Models\SubOrder;
use App\Models\Vendor;
use App\Modules\Order\Jobs\Vendor\SyncInvoiceFromVendor;
use Illuminate\Console\Command;

final class SyncOrderInvoices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vendor:sync-invoices {--days=30 : Number of days in the past to process orders}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync vendor invoices for suborders that don\'t have invoices';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $vendors = Vendor::query()
            ->whereIn('key', ['MWIX'])
            ->isEnabled()
            ->get();

        if ($vendors->isEmpty()) {
            $this->error('No enabled MWI vendor found');

            return;
        }

        $days = (int) $this->option('days');
        $fromDate = now()->subDays($days);

        foreach ($vendors as $vendor) {
            $this->info("Processing vendor: {$vendor->key}");

            $suborders = $vendor->suborders()
                ->whereHas('externalOrders', function ($query) {
                    $query->whereNull('invoice_file_path');
                })
                ->where('created_at', '>=', $fromDate)
                ->hasValidClinicConnection($vendor->id)
                ->distinct()
                ->get();

            $this->info("Found {$suborders->count()} suborders without invoices");

            $suborders->each(fn (SubOrder $suborder) => SyncInvoiceFromVendor::dispatch($suborder));
        }
    }
}
