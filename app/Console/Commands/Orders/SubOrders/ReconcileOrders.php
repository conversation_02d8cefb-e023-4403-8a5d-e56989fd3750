<?php

declare(strict_types=1);

namespace App\Console\Commands\Orders\SubOrders;

use App\Models\SubOrder;
use App\Models\Vendor;
use App\Modules\Order\Jobs\Vendor\ReconcileOrderFromVendor;
use Illuminate\Console\Command;

final class ReconcileOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vendor:reconcile-orders {--days=30 : Number of days in the past to process orders}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reconcile orders that haven\'t been synced yet';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $vendors = Vendor::query()
            ->whereIn('key', ['AMZN', 'MWIX'])
            ->isEnabled()
            ->get();

        if ($vendors->isEmpty()) {
            $this->error('No enabled vendors found');

            return;
        }

        $days = (int) $this->option('days');
        $fromDate = now()->subDays($days);

        foreach ($vendors as $vendor) {
            $this->info("Processing vendor: {$vendor->key}");

            $suborders = $vendor->suborders()
                ->whereNull('reconciled_at')
                ->where('created_at', '>=', $fromDate)
                ->hasValidClinicConnection($vendor->id)
                ->get();

            $this->info("Found {$suborders->count()} suborders to reconcile");

            $suborders->each(fn (SubOrder $suborder) => ReconcileOrderFromVendor::dispatch($suborder));
        }
    }
}
