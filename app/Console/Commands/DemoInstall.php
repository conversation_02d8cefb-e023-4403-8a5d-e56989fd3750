<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Enums\AddressType;
use App\Models\Address;
use App\Models\Clinic;
use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Console\Command;
use Throwable;

final class DemoInstall extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'demo:install';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Install demo data for testing and development';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Installing demo data...');

        $productOffers = collect();

        for ($i = 0; $i < 15; $i++) {
            $product = Product::factory()->create([
                'name' => '[TEST] Product',
            ]);

            $productOffer = ProductOffer::factory()
                ->for(Vendor::whereIsEnabled(true)->inRandomOrder()->first())
                ->create(['name' => '[TEST] Product Offer '.$i]);

            try {
                $productOffer->update(['product_id' => $product->id]);
            } catch (Throwable $th) {
                //
            }

            $product->attributes()->createMany([
                ['name' => 'Dosage', 'value' => ['Tablet', 'Capsule', 'Liquid', 'Injection'][array_rand(['Tablet', 'Capsule', 'Liquid', 'Injection'])]],
                ['name' => 'Package Size', 'value' => ['30 count', '60 count', '100ml', '250ml'][array_rand(['30 count', '60 count', '100ml', '250ml'])]],
                ['name' => 'Active Ingredient', 'value' => ['Amoxicillin', 'Meloxicam', 'Carprofen'][array_rand(['Amoxicillin', 'Meloxicam', 'Carprofen'])]],
                ['name' => 'Strength', 'value' => ['100mg', '200mg', '500mg'][array_rand(['100mg', '200mg', '500mg'])]],
                ['name' => 'Flavor', 'value' => ['Cherry', 'Grape', 'Orange', 'Watermelon'][array_rand(['Cherry', 'Grape', 'Orange', 'Watermelon'])]],
            ]);

            $productOffers->push($productOffer);
        }

        $accountA = ClinicAccount::factory()->create([
            'name' => '[TEST] Clinic Account A',
        ]);
        $accountA->details()->create([
            'ein' => '12-3456789',
            'phone_number' => '******-567-8900',
        ]);
        // $accountA->address()->create(Address::factory()->make()->toArray());

        $accountB = ClinicAccount::factory()->create([
            'name' => '[TEST] Clinic Account B',
        ]);
        $accountB->details()->create([
            'ein' => '12-3456789',
            'phone_number' => '******-567-8900',
        ]);
        // $accountB->address()->create(Address::factory()->make()->toArray());

        User::factory()->create([
            'account_id' => $accountA->id,
            'name' => '[TEST] Michael Scott',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ])->assignRole(ClinicAccountRole::Admin);

        $clinicA1 = Clinic::factory()->create([
            'clinic_account_id' => $accountA->id,
            'name' => '[TEST] Clinic A1',
        ]);
        $clinicA1->billingAddress()->create(Address::factory(['type' => AddressType::Billing])->make()->toArray());
        $clinicA1->shippingAddress()->create(Address::factory(['type' => AddressType::Shipping])->make()->toArray());
        $productOffers->each(fn (ProductOffer $product) => $clinicA1->productOffers()->attach($product, ['price' => (int) round($product->price * 0.9)]));

        $clinicB1 = Clinic::factory()->create([
            'clinic_account_id' => $accountB->id,
            'name' => '[TEST] Clinic B1',
        ]);
        $clinicB1->billingAddress()->create(Address::factory(['type' => AddressType::Billing])->make()->toArray());
        $clinicB1->shippingAddress()->create(Address::factory(['type' => AddressType::Shipping])->make()->toArray());
        $productOffers->each(fn (ProductOffer $product) => $clinicB1->productOffers()->attach($product, ['price' => (int) round($product->price * 0.5)]));

        $manager = User::factory()->create([
            'name' => '[TEST] Dwight Schrute',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'account_id' => $accountA->id,
        ]);

        $manager->assignRole(ClinicAccountRole::Manager);

        $clinicA2 = Clinic::factory()->create([
            'clinic_account_id' => $accountA->id,
            'name' => '[TEST] Clinic A2',
        ]);
        $clinicA2->users()->attach($manager);
        $productOffers->each(fn (ProductOffer $product) => $clinicA2->productOffers()->attach($product, ['price' => (int) round($product->price * 0.9)]));

        $clinicB2 = Clinic::factory()->create([
            'clinic_account_id' => $accountB->id,
            'name' => '[TEST] Clinic B2',
        ]);
        $clinicB2->users()->attach($manager);
        $productOffers->each(fn (ProductOffer $product) => $clinicB2->productOffers()->attach($product, ['price' => (int) round($product->price * 0.5)]));

        $this->info('Demo data installed successfully!');
    }
}
