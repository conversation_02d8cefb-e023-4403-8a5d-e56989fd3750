<?php

declare(strict_types=1);

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use SensitiveParameter;

final class ManagerAddedToClinic extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public string $clinic,
        #[SensitiveParameter]
        public string $token,
    ) {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject("Welcome to {$this->clinic}! Set Up Your Manager Account")
            ->markdown('mail.manager-added-to-clinic', [
                'manager' => $notifiable,
                'clinic' => $this->clinic,
                'setupUrl' => $this->getSetupUrl($notifiable),
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    /**
     * Get the setup URL for the manager.
     */
    protected function getSetupUrl(object $notifiable): string
    {
        return config('app.frontend_url')."/password-reset/$this->token?email={$notifiable->getEmailForPasswordReset()}";
    }
}
