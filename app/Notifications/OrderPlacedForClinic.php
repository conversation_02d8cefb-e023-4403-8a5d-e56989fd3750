<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

final class OrderPlacedForClinic extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public Order $order)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $orderNumber = $this->order->order_number;
        $orderTotal = '$'.number_format($this->order->total_price / 100, 2);
        $orderItemsCount = $this->order->items->count();

        return (new MailMessage)
            ->subject("Highfive Order Confirmation {$orderNumber}: {$orderTotal} - {$orderItemsCount} items")
            ->bcc('<EMAIL>', 'HighFive Vet')
            ->markdown('mail.order-placed-for-clinic', [
                'customerName' => $this->order->clinic->name,
                'orderNumber' => $orderNumber,
                'orderDate' => $this->order->created_at->format('M d, Y'),
                'shippingAddress' => "{$this->order->shippingAddress->street}\\\r\n{$this->order->shippingAddress->city}\\\r\n{$this->order->shippingAddress->state}\\\r\n{$this->order->shippingAddress->postal_code}",
                'orderTotal' => $orderTotal,
                'orderItemsCount' => $orderItemsCount,
                'orderUrl' => config('app.frontend_url')."/order-history/{$this->order->id}",
                'orderItems' => $this->order->items->map(fn ($item) => [
                    'name' => $item->productOffer->name,
                    'vendorSku' => $item->productOffer->vendor_sku,
                    'vendorName' => $item->productOffer->vendor->name,
                    'price' => '$'.number_format($item->price / 100, 2),
                    'quantity' => $item->quantity,
                    'totalPrice' => '$'.number_format($item->total_price / 100, 2),
                ]),
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
