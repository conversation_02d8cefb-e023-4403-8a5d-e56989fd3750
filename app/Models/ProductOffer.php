<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\ProductStockStatus;
use App\Models\QueryBuilders\ProductOfferQuery;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

final class ProductOffer extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $table = 'product_offers';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array<int, string>
     */
    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'stock_status' => ProductStockStatus::class,
        'keywords' => 'array',
        'deactivated_at' => 'datetime',
        'last_synced_at' => 'datetime',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'price' => 0,
        'increments' => 1,
    ];

    /**
     * Boot the model.
     */
    public static function boot(): void
    {
        parent::boot();
    }

    /**
     * Create a new Eloquent query builder for the model.
     */
    public function newEloquentBuilder($query): ProductOfferQuery
    {
        return new ProductOfferQuery($query);
    }

    /**
     * Retrieve the model for a bound value.
     */
    public function resolveRouteBinding($value, $field = null): ?static
    {
        return $this->whereHas('vendor', fn (Builder $query) => $query->isEnabled())->findOrFail($value);
    }

    /**
     * Get the vendor that owns the product.
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the clinic prices that belong to the product.
     */
    public function clinics(): BelongsToMany
    {
        return $this->belongsToMany(Clinic::class, 'clinic_product_offer', 'product_offer_id', 'clinic_id')
            ->withPivot('price');
    }

    /**
     * Get the orders that contain this product.
     */
    public function orders(): BelongsToMany
    {
        return $this->belongsToMany(Order::class, 'order_items', 'product_offer_id', 'order_id');
    }

    /**
     * Get the order items that belong to the product.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class, 'product_offer_id');
    }

    /**
     * Get the cart items that belong to the product.
     */
    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class, 'product_offer_id');
    }

    /**
     * Get the product that owns the offer.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function gpos(): BelongsToMany
    {
        return $this->belongsToMany(
            GpoAccount::class,
            'gpo_recommended_products',
            'product_offer_id',
            'gpo_account_id',
        )->withTimestamps();
    }

    /**
     * Get the promotions associated with this product offer.
     */
    public function promotions(): BelongsToMany
    {
        return $this->belongsToMany(Promotion::class, 'promotion_product_offers', 'product_offer_id', 'promotion_id');
    }

    /**
     * Get the saved items for this product offer.
     */
    public function savedItems(): HasMany
    {
        return $this->hasMany(SavedItem::class);
    }

    /**
     * Whether this product is marked as a favorite by the current clinic.
     * This is a nullable clinic-specific attribute that:
     * - Returns true if favorited_at timestamp exists in the pivot table
     * - Returns false if favorited_at is null
     * - Returns false if the clinic relationship/pivot is not loaded
     * The favorited_at field is set when a clinic marks a product as favorite.
     */
    protected function isFavorite(): Attribute
    {
        return Attribute::make(get: fn (): bool => isset($this->clinic_favorited_at) ? ! empty($this->clinic_favorited_at) : false);
    }

    /**
     * The timestamp of when this product was last ordered by the current clinic.
     * This is a nullable clinic-specific attribute that:
     * - Returns Carbon instance if last_ordered_at exists
     * - Returns null if last_ordered_at is null
     * - Returns null if the order history is not loaded
     * The last_ordered_at is determined from the most recent order for this product by the clinic.
     */
    protected function lastOrderedAt(): Attribute
    {
        return Attribute::make(get: fn (): ?Carbon => isset($this->clinic_last_ordered_at) ? Carbon::parse($this->clinic_last_ordered_at) : null);
    }

    /**
     * The total quantity of this product that has been ordered by the current clinic.
     * This is a nullable clinic-specific attribute that:
     * - Returns integer value if clinic_last_ordered_quantity exists
     * - Returns null if clinic_last_ordered_quantity is null
     * - Returns null if the order history is not loaded
     * The clinic_last_ordered_quantity is determined from the sum of quantities ordered by the clinic.
     */
    protected function lastOrderedQuantity(): Attribute
    {
        return Attribute::make(get: fn (): ?int => $this->clinic_last_ordered_quantity);
    }
}
