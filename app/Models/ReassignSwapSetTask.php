<?php

declare(strict_types=1);

namespace App\Models;

use App\Observers\ReassignSwapSetTaskObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

#[ObservedBy(ReassignSwapSetTaskObserver::class)]
final class ReassignSwapSetTask extends Model
{
    use HasUuids;

    protected $guarded = [];

    public function events(): HasMany
    {
        return $this->hasMany(ReassignSwapSetTaskEvent::class);
    }

    protected function casts(): array
    {
        return [
            'total_rows_count' => 'integer',
            'processsed_rows_count' => 'integer',
            'skipped_or_failed_rows_count' => 'integer',
            'started_at' => 'datetime',
            'finished_at' => 'datetime',
        ];
    }
}
