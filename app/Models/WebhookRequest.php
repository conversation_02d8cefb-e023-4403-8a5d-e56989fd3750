<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\WebhookRequestStatus;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class WebhookRequest extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'vendor_id',
        'method',
        'slug',
        'headers',
        'payload',
        'status',
        'error_message',
    ];

    protected $casts = [
        'headers' => 'array',
        'payload' => 'array',
        'status' => WebhookRequestStatus::class,
    ];

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }
}
