<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\OrderItemStatus;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationEvent;
use App\Modules\Order\Models\ExternalOrder;
use App\Traits\Statusable;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;

final class SubOrder extends Model
{
    use HasFactory, HasUuids, SoftDeletes, Statusable;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array<int, string>
     */
    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'reconciled_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'total_price',
        'status',
    ];

    /**
     * Get the order that owns the suborder.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function shipments(): HasMany
    {
        return $this->hasMany(Shipment::class);
    }

    public function externalOrders(): HasMany
    {
        return $this->hasMany(ExternalOrder::class);
    }

    /**
     * Get the vendor that owns the suborder.
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the order items for the suborder.
     */
    public function items(): HasManyThrough
    {
        return $this->hasManyThrough(
            OrderItem::class,
            Order::class,
            'id',
            'order_id',
            'order_id',
            'id'
        )->whereHas('productOffer', function ($query) {
            $query->where('vendor_id', $this->vendor_id);
        });
    }

    /**
     * Get if the SubOrder has error.
     */
    public function scopeHasError(Builder $query): Builder
    {
        return $query->whereNotNull('error_message');
    }

    /**
     * Get all integration events related to this suborder.
     */
    public function integrationEvents()
    {
        return $this->morphMany(IntegrationEvent::class, 'subject');
    }

    /**
     * Scope to filter suborders whose order's clinic has a valid (connected) integration connection for the given vendor.
     */
    public function scopeHasValidClinicConnection(Builder $query, $vendorId): Builder
    {
        return $query->whereHas('order.clinic.integrationConnections', function ($q) use ($vendorId) {
            $q->where('vendor_id', $vendorId)
                ->where('status', IntegrationConnectionStatus::Connected);
        });
    }

    /**
     * The total price of the order.
     */
    protected function totalPrice(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->items->sum(fn (OrderItem $item) => $item->total_price)
        );
    }

    protected function totalTaxFee(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->items->sum(fn (OrderItem $item) => $item->tax_fee)
        );
    }

    /**
     * The status of the order.
     */
    protected function status(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->determineOrderStatus()
        );
    }

    /**
     * Determine the status of the order based on the items statuses.
     */
    private function determineOrderStatus(): OrderItemStatus
    {
        $statusCounts = $this->items()
            ->selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        return $this->generateStatus($statusCounts);
    }
}
