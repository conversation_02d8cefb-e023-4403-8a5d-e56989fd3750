<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class ReassignSwapSetTaskEvent extends Model
{
    use HasUuids;

    protected $guarded = [];

    public function task(): BelongsTo
    {
        return $this->belongsTo(ReassignSwapSetTask::class);
    }

    protected function casts(): array
    {
        return [
            'meta' => 'array',
        ];
    }
}
