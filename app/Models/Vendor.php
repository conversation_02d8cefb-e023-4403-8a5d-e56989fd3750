<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\ExpenseCategory;
use App\Enums\VendorAuthenticationKind;
use App\Enums\VendorType;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\AsEnumCollection;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property-read \Illuminate\Support\Collection<int, IntegrationPoint> $integration_points
 */
final class Vendor extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'name',
        'type',
        'expense_category',
        'purchase_order_email',
        'new_account_email',
        'phone_number',
        'credentials_schema',
        'is_enabled',
        'authentication_kind',
        'authentication_configuration',
    ];

    /**
     * Get the account receivable contacts for the vendor.
     */
    public function accountReceivableContacts(): HasMany
    {
        return $this->hasMany(VendorAccountReceivableContact::class);
    }

    /**
     * Get the shipping terms for the vendor.
     */
    public function shippingTerms(): HasOne
    {
        return $this->hasOne(VendorShippingTerms::class);
    }

    /**
     * Get the integration connections for the vendor.
     */
    public function integrationConnections(): HasMany
    {
        return $this->hasMany(IntegrationConnection::class);
    }

    /**
     * Get the clinics connected to this vendor through integration connections.
     */
    public function clinics(): HasManyThrough
    {
        return $this->hasManyThrough(
            Clinic::class,
            IntegrationConnection::class,
            'vendor_id',
            'id',
            'id',
            'clinic_id'
        );
    }

    /**
     * Get the products for the vendor.
     */
    public function productOffers(): HasMany
    {
        return $this->hasMany(ProductOffer::class, 'vendor_id', 'id');
    }

    /**
     * Get the suborders for the vendor.
     */
    public function suborders(): HasMany
    {
        return $this->hasMany(SubOrder::class);
    }

    /**
     * Calculate the shipping fee based on the order total and shipping terms.
     *
     * Applies free shipping if the order meets the free shipping threshold or if the
     * shipping rate is set to zero. Otherwise, returns the standard shipping rate.
     *
     * @return array<int, int>
     */
    public function calculateShippingFee(int $subtotal): array
    {
        $shippingTerms = $this->shippingTerms;

        if (is_null($shippingTerms)) {
            return [0, 0];
        }

        $freeShippingThreshold = $shippingTerms->free_shipping_threshold;
        $shippingRate = $shippingTerms->shipping_rate;

        if ($freeShippingThreshold === 0 && $shippingRate > 0) {
            return [0, $shippingRate];
        }

        if ($freeShippingThreshold > 0 && $subtotal >= $freeShippingThreshold) {
            return [0, 0];
        }

        return [$freeShippingThreshold - $subtotal, $shippingRate];
    }

    public function scopeIsEnabled(Builder $query): Builder
    {
        return $query->where('is_enabled', true);
    }

    public function can(IntegrationPoint $point): bool
    {
        return $this->integration_points->contains($point);
    }

    protected function casts(): array
    {
        return [
            'type' => VendorType::class,
            'expense_category' => ExpenseCategory::class,
            'credentials_schema' => 'array',
            'is_enabled' => 'boolean',
            'authentication_kind' => VendorAuthenticationKind::class,
            'authentication_configuration' => 'array',
            'integration_points' => AsEnumCollection::of(IntegrationPoint::class),
        ];
    }

    protected function imageUrl(): Attribute
    {
        return Attribute::get(fn () => $this->image_path ? asset("storage/$this->image_path") : null);
    }
}
