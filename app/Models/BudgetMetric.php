<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\ExpenseCategory;
use App\Enums\TimePeriod;

final class BudgetMetric
{
    /**
     * Create a new budget metric instance.
     */
    public function __construct(
        public readonly ExpenseCategory $category,
        public readonly TimePeriod $period,
        public readonly int $spent,
        public readonly float $spentPercentage,
        public readonly int $target,
        public readonly float $targetPercentage,
    ) {
        //
    }
}
