<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use InvalidArgumentException;

final class Cart extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'total_items',
        'total_price',
        'prices_have_changed',
    ];

    /**
     * Get the clinic that owns the cart.
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get the cart items that belong to the clinic.
     */
    public function items(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Add an item to the cart.
     */
    public function addItem(ProductOffer $product, int $quantity, ?User $user = null, ?string $notes = null): void
    {
        if ($quantity <= 0) {
            return;
        }

        // Ensure we have a valid price - never allow null
        $price = $product->clinics->first()?->pivot?->price ?? $product->price ?? 0;

        if ($price <= 0) {
            throw new InvalidArgumentException("Product offer {$product->id} has no valid price");
        }

        $item = $this->items()->firstOrNew([
            'product_offer_id' => $product->id,
        ], [
            'user_id' => $user?->id,
            'price' => $price,
            'notes' => $notes,
        ]);

        $item->quantity = $item->wasRecentlyCreated ? $item->quantity + $quantity : $quantity;

        // Update notes if provided, even for existing items
        if ($notes !== null) {
            $item->notes = $notes;
        }

        $item->save();
    }

    /**
     * Update the quantity of an item in the cart.
     */
    public function updateItem(ProductOffer $product, array $data): void
    {
        $item = $this->items()->where('product_offer_id', $product->id)->first();

        if (is_null($item)) {
            $this->addItem($product, $data['quantity']);

            return;
        }

        if (array_key_exists('quantity', $data) && $data['quantity'] <= 0) {
            $this->removeItem($item);

            return;
        }

        $item->update($data);
    }

    /**
     * Remove an item from the cart.
     */
    public function removeItem(CartItem $item): void
    {
        $item->delete();
    }

    /**
     * Clean the cart.
     */
    public function clean(): void
    {
        $this->items()->delete();
    }

    /**
     * The sum of the quantity of all items in the cart.
     */
    protected function itemsCount(): Attribute
    {
        return Attribute::make(get: fn () => $this->items->sum('quantity'));
    }

    /**
     * The total number of items in the cart (alias for itemsCount).
     */
    protected function totalItems(): Attribute
    {
        return Attribute::make(get: fn () => $this->items->sum('quantity'));
    }

    /**
     * The total number of unique items in the cart.
     */
    protected function uniqueItemsCount(): Attribute
    {
        return Attribute::make(get: fn () => $this->items->count());
    }

    /**
     * The sum of the subtotal of all items in the cart.
     */
    protected function subtotal(): Attribute
    {
        return Attribute::make(get: fn () => $this->items->sum(fn (CartItem $item) => $item->subtotal));
    }

    /**
     * The total price of all items in the cart (alias for subtotal).
     */
    protected function totalPrice(): Attribute
    {
        return Attribute::make(get: fn () => $this->items->sum(fn (CartItem $item) => $item->subtotal));
    }

    /**
     * Whether the prices have changed.
     */
    protected function pricesHaveChanged(): Attribute
    {
        return Attribute::make(get: fn () => $this->items->some(fn ($item) => $item->price_has_changed));
    }
}
