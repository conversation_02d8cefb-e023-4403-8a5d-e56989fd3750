<?php

declare(strict_types=1);

namespace App\Models;

use App\Modules\Promotion\Models\Promotion;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class OrderPromotion extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = [];

    protected $casts = [
        'triggering_items' => 'array',
        'applied_rules' => 'array',
        'applied_benefits' => 'array',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function promotion(): BelongsTo
    {
        return $this->belongsTo(Promotion::class);
    }
}
