<?php

declare(strict_types=1);

namespace App\Models\QueryBuilders;

use App\Models\Clinic;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

final class ProductOfferQuery extends Builder
{
    public function stale(int $days): self
    {
        return $this
            ->active()
            ->where('last_synced_at', '<', Carbon::now()->subDays($days));
    }

    public function active(): self
    {
        return $this->whereNull('deactivated_at');
    }

    /**
     * Eager load the clinic relationship for a specific clinic ID
     */
    public function withClinic(string $clinicId): self
    {
        return $this->with([
            'clinics' => fn (BelongsToMany $builder) => $builder->whereClinicId($clinicId),
        ]);
    }

    /**
     * Eager load the most recent order item for a specific clinic
     */
    public function withLastClinicOrderItem(string $clinicId): self
    {
        return $this->with([
            'orderItems' => fn (HasMany $builder) => $builder
                ->whereHas('order', fn (Builder $query) => $query->whereClinicId($clinicId))
                ->latest('created_at')
                ->limit(1),
        ]);
    }

    /**
     * Eager load cart items associated with a specific clinic
     */
    public function withClinicCartItem(string $clinicId): self
    {
        return $this->with([
            'cartItems' => fn (HasMany $builder) => $builder->whereHas('cart', fn (Builder $query) => $query->whereClinicId($clinicId)),
        ]);
    }

    /**
     * Filter products by vendor ID.
     */
    public function whereVendorIdIn(array $vendorIds): self
    {
        return $this->whereIn('vendor_id', $vendorIds);
    }

    public function withExistsGpo($gpoId)
    {
        return $this->withExists(['gpos as is_recommended' => fn ($query) => $query->where('gpo_accounts.id', $gpoId)]);
    }

    public function withVendorConnected(array $clinicIds): self
    {
        return $this->whereHas('vendor', function ($query) use ($clinicIds) {
            $query->whereHas('integrationConnections', function ($query) use ($clinicIds) {
                $query->whereIn('status', ['connected', 'connecting'])->whereIn('clinic_id', $clinicIds);
            });
        });
    }

    public function withActiveOffers(): self
    {
        return $this->whereNull('deactivated_at');
    }

    public function withPriceForFrontend(Clinic $clinic): self
    {
        return $this->where(function ($query) use ($clinic) {
            $query->where(function ($subQuery) use ($clinic) {
                $subQuery->whereHas('clinics', function ($clinicQuery) use ($clinic) {
                    $clinicQuery->where('clinic_id', $clinic->id)
                        ->whereNotNull('clinic_product_offer.price');
                });
            })->orWhere(function ($subQuery) use ($clinic) {
                $subQuery->whereNotNull('price')
                    ->where(function ($clinicCheck) use ($clinic) {
                        $clinicCheck->whereDoesntHave('clinics', function ($q) use ($clinic) {
                            $q->where('clinic_id', $clinic->id);
                        })->orWhereHas('clinics', function ($q) use ($clinic) {
                            $q->where('clinic_id', $clinic->id)
                                ->whereNull('clinic_product_offer.price');
                        });
                    });
            });
        });
    }
}
