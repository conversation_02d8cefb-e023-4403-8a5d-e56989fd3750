<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Exceptions\NoVendorConnection;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Order\Events\Vendor\VendorConnectionDisconnected;
use App\Modules\Order\Services\Vendor\Exceptions\ApiAuthenticationException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Support\Facades\Log;
use Throwable;

abstract class BaseVendorJob implements ShouldQueue
{
    use Queueable;

    // Vendors that use clinic-specific credentials
    protected const CLINIC_SPECIFIC_VENDORS = ['AMZN', 'MWIX', 'GERVET'];

    // Vendors that use generic credentials
    protected const GENERIC_VENDORS = ['PATN'];

    public int $tries = 4; // 1 initial + 3 retries

    public int $backoff = 60 * 15; // 15 minutes in seconds

    public function __construct(protected string $clinicId, protected string $vendorId) {}

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        return [
            new WithoutOverlapping($this->getOverlapKey()),
        ];
    }

    public function failed(?Throwable $th): void
    {
        Log::error('Job failed: '.$th->getMessage(), [
            'job' => static::class,
            'exception' => get_class($th),
            'clinic_id' => $this->clinicId,
            'vendor_id' => $this->vendorId,
            'error' => $th->getMessage(),
            'trace' => $th->getTraceAsString(),
        ]);

        if ($th instanceof ApiAuthenticationException) {
            $vendorConnection = $this->getVendorConnection();
            VendorConnectionDisconnected::dispatch($vendorConnection, $th->getMessage());
        }
    }

    /**
     * Get the overlap prevention key based on vendor credential type
     */
    protected function getOverlapKey(): string
    {
        // For vendors with clinic-specific credentials, include clinic_id
        if (in_array($this->vendorId, self::CLINIC_SPECIFIC_VENDORS)) {
            return "vendor-job-{$this->vendorId}-{$this->clinicId}";
        }

        // For vendors with generic credentials, use only vendor_id
        return "vendor-job-{$this->vendorId}";
    }

    /**
     * Fetch the IntegrationConnection relationship.
     */
    protected function getVendorConnection(): IntegrationConnection
    {
        $vendorConnection = IntegrationConnection::whereClinicId($this->clinicId)
            ->whereVendorId($this->vendorId)
            ->first();

        if (! $vendorConnection) {
            throw new NoVendorConnection();
        }

        return $vendorConnection;
    }
}
