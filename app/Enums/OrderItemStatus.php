<?php

declare(strict_types=1);

namespace App\Enums;

enum OrderItemStatus: string
{
    use EnumMethods;

    /**
     * The transient statuses; they can transition to other statuses.
     */
    case Pending = 'PENDING';
    case PlacementFailed = 'PLACEMENT_FAILED';
    case Accepted = 'ACCEPTED';
    case Backordered = 'BACKORDERED';
    case Shipped = 'SHIPPED';

    /**
     * The terminal statuses; they cannot transition to other statuses.
     */
    case Rejected = 'REJECTED';
    case Returned = 'RETURNED';
    case Delivered = 'DELIVERED';
    case Cancelled = 'CANCELLED';

    /**
     * The composite statuses; these are result of a composite operation.
     */
    case Processing = 'PROCESSING';
    case PartiallyShipped = 'PARTIALLY_SHIPPED';
    case PartiallyDelivered = 'PARTIALLY_DELIVERED';

    /**
     * Get all non-terminal and non-initial (transient) statuses that should be synced.
     *
     * @return array<self>
     */
    public static function transient(): array
    {
        return [
            self::Pending,
            self::PlacementFailed,
            self::Accepted,
            self::Backordered,
            self::Shipped,
        ];
    }

    /**
     * Get all terminal statuses (that should not be synced).
     *
     * @return array<self>
     */
    public static function terminal(): array
    {
        return [
            self::Rejected,
            self::Returned,
            self::Delivered,
            self::Cancelled,
        ];
    }
}
