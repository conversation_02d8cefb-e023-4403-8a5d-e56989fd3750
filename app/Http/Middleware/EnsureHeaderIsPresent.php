<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

final class EnsureHeaderIsPresent
{
    public function handle(Request $request, Closure $next, string ...$headers)
    {
        foreach ($headers as $header) {
            if ($request->header($header) && $request->header($header) !== '') {
                continue;
            }

            abort(Response::HTTP_BAD_REQUEST, "The {$header} header is required.");
        }

        return $next($request);
    }
}
