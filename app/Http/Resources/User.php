<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Modules\Account\Data\ClinicAccountData;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class User extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $manager = app('impersonate');

        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'role' => 'ADMINISTRATOR',
            'account' => $this->whenLoaded('account', fn () => ClinicAccountData::from($this->account)),
            'account_id' => $this->whenLoaded('account', fn () => $this->account->id),
            'system_user' => $this->when($this->is_system_user, fn () => true),
            'is_impersonating' => $this->when($manager->isImpersonating(), fn () => true),

            /**
             * @deprecated
             */
            'clinic_id' => $this->whenLoaded('account', fn () => $this->account->clinics->first()?->id),
        ];
    }
}
