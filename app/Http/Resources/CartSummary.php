<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Brick\Money\Money;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class CartSummary extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $totalShippingFee = $this->calculateVendorsShippingFee();

        return [

            'subtotal' => Money::ofMinor($this->subtotal, 'USD')->getAmount(),

            'total' => Money::ofMinor($this->subtotal + $totalShippingFee, 'USD')->getAmount(),

            'itemsCount' => $this->items->sum('quantity'),

            'uniqueItemsCount' => $this->items->count(),
        ];
    }

    private function calculateVendorsShippingFee(): int
    {
        return $this->items
            ->groupBy('vendor.id')->map(fn (Collection $items) => [
                'vendor' => $items->first()->vendor,
                'items' => $items,
            ])
            ->sum(function (array $vendor) {
                [, $shippingFee] = $vendor['vendor']->calculateShippingFee($vendor['items']->sum('subtotal'));

                return $shippingFee;
            });
    }
}
