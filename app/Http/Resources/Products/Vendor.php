<?php

declare(strict_types=1);

namespace App\Http\Resources\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class Vendor extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /**
             * The vendor's unique identifier.
             */
            'id' => $this->id,

            /**
             * The vendor's display name.
             */
            'name' => $this->name,

            /**
             * The URL to the vendor's logo/image.
             */
            'imageUrl' => asset("storage/{$this->image_path}"),
        ];
    }
}
