<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Modules\Gpo\Data\GpoAccountData;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class GpoUser extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'account' => $this->whenLoaded('account', fn () => GpoAccountData::fromModel($this->account)),
            'account_id' => $this->whenLoaded('account', fn () => $this->account->id),
        ];
    }
}
