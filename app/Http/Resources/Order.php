<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\OrderItem;
use App\Models\SubOrder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class Order extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_id' => $this->id,
            'order_number' => "$this->order_number",
            'order_date' => $this->created_at->toIso8601String(),
            'total_items' => $this->items->sum('quantity'),
            'order_total' => round($this->total_price / 100, 2),
            'number_of_vendors' => $this->suborders->count(),
            'status' => $this->status,
            'vendors' => $this->suborders->map(fn (SubOrder $suborder) => [
                'vendor' => new Vendor($suborder->vendor),
                'total_items' => $suborder->items->sum('quantity'),
                'order_total' => round($suborder->total_price / 100, 2),
            ]),
            'products' => $this->items->map(fn (OrderItem $item) => [
                ...(new ProductOffer($item->productOffer->load('clinics')))->toArray($request),
                'count' => $item->quantity,
                'price' => round($item->price / 100, 2),
                'total' => round($item->total_price / 100, 2),
                'tax_fee' => round($item->tax_fee / 100, 2),
                'status' => $item->status,
            ]),
            'shipments' => $this->shipments->map(fn ($shipment) => [
                'id' => $shipment->id,
                'tracking_number' => $shipment->tracking_number,
                'carrier' => $shipment->carrier,
            ]),
            'promotions' => $this->promotions->map(fn ($promotion) => [
                'id' => $promotion->id,
                'name' => $promotion->promotion->name,
                'type' => $promotion->promotion->type->value,
                'vendor' => new Vendor($promotion->promotion->vendor),
                'triggering_items' => $promotion->triggering_items,
                'applied_rules' => $promotion->applied_rules,
                'applied_benefits' => $promotion->applied_benefits,
            ]),
        ];
    }
}
