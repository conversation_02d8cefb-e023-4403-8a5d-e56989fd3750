<?php

declare(strict_types=1);

namespace App\Http\Resources\Cart;

use Brick\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class LightweightCart extends JsonResource
{
    /**
     * Transform the resource into a array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'subtotal' => Money::ofMinor($this->subtotal, 'USD')->getAmount(),
            'itemsCount' => $this->items->sum('quantity'),
            'uniqueItemsCount' => $this->items->count(),
        ];
    }
}
