<?php

declare(strict_types=1);

namespace App\Http\Resources\Cart;

use Brick\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class BudgetMetric extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var \App\Enums\ExpenseCategory */
            'category' => $this->category,
            'spent' => Money::ofMinor(ceil($this->spent / 100) * 100, 'USD')->getAmount(),
            'spentPercentage' => number_format($this->spentPercentage),
            'target' => Money::ofMinor(ceil($this->target / 100) * 100, 'USD')->getAmount(),
            'targetPercentage' => number_format($this->targetPercentage),
        ];
    }
}
