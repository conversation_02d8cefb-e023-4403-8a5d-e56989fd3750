<?php

declare(strict_types=1);

namespace App\Http\Resources\Cart;

use App\Http\Resources\Cart\Vendor as CartVendorResource;
use App\Models\BudgetSummary;
use Brick\Money\Money;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class Cart extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $vendors = $this->items->groupBy('vendor.id')->map(fn (Collection $items) => [
            'vendor' => $items->first()->vendor,
            'items' => $items,
        ]);

        $totalShippingFee = $vendors->sum(function (array $vendor) {
            [, $shippingFee] = $vendor['vendor']->calculateShippingFee($vendor['items']->sum('subtotal'));

            return $shippingFee;
        });

        return [
            'budget' => new Budget(new BudgetSummary($this->clinic, $this->resource)),

            'subtotal' => Money::ofMinor($this->subtotal, 'USD')->getAmount(),

            'total' => Money::ofMinor($this->subtotal + $totalShippingFee, 'USD')->getAmount(),

            'itemsCount' => $this->items->sum('quantity'),

            'uniqueItemsCount' => $this->items->count(),

            'vendors' => CartVendorResource::collection($vendors->sortBy('vendor.name')->values()),
        ];
    }
}
