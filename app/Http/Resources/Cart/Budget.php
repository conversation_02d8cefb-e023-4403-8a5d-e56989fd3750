<?php

declare(strict_types=1);

namespace App\Http\Resources\Cart;

use App\Enums\TimePeriod;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class Budget extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var \App\Enums\ClinicBudgetType */
            'type' => $this->type,
            'weekToDate' => BudgetMetric::collection($this->metrics->where('period', TimePeriod::WeekToDate)),
            'monthToDate' => BudgetMetric::collection($this->metrics->where('period', TimePeriod::MonthToDate)),
        ];
    }
}
