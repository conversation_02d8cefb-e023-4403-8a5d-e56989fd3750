<?php

declare(strict_types=1);

namespace App\Http\Resources\Cart;

use Brick\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;

final class Vendor extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var \App\Models\Vendor */
        $vendor = $this['vendor'];

        /** @var Collection<int, \App\Models\CartItem> */
        $items = $this['items'];

        $subtotal = $items->sum('total_price');

        [$amountToFreeShipping, $shippingFee] = $vendor->calculateShippingFee($subtotal);

        return [
            /**
             * The vendor's unique identifier.
             */
            'id' => $vendor->id,

            /**
             * The vendor's display name.
             */
            'name' => $vendor->name,

            /**
             * The URL to the vendor's logo/image.
             */
            'imageUrl' => asset("storage/{$vendor->image_path}"),

            /**
             * The subtotal of all items in the vendor's cart.
             */
            'subtotal' => Money::ofMinor($subtotal, 'USD')->getAmount(),

            /**
             * The total of all items in the vendor's cart (subtotal + shipping fee).
             */
            'total' => Money::ofMinor($subtotal + $shippingFee, 'USD')->getAmount(),

            /**
             * The shipping fee for the vendor's cart.
             */
            'shippingFee' => Money::ofMinor($shippingFee, 'USD')->getAmount(),

            /**
             * The amount needed to reach free shipping.
             */
            'amountToFreeShipping' => Money::ofMinor($amountToFreeShipping, 'USD')->getAmount(),

            /**
             * The cutoff time for the vendor's cart.
             */
            'cutoffTime' => $vendor->shippingTerms?->cutoff_time,

            /**
             * The items in the vendor's cart.
             */
            'items' => CartItem::collection($items->sortBy('id')),
        ];
    }
}
