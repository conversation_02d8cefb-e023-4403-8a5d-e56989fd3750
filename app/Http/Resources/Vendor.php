<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Modules\CatalogSync\Models\CatalogSyncTask;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class Vendor extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $connection = $this->integrationConnections()?->where('clinic_id', $request->clinicId())?->first();
        $lastCatalogSync = CatalogSyncTask::query()
            ->where('integration_connection_id', $connection?->id)
            ?->latest()
            ?->first();

        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
            'image_url' => asset("storage/{$this->image_path}"),
            'status' => $connection?->status ?? IntegrationConnectionStatus::Disconnected,
            'alert' => $connection?->alert,
            'authentication_kind' => $this->authentication_kind,
            'authentication_configuration' => $this->authentication_configuration,
            'lastProductCatalogSync' => $lastCatalogSync ? new CatalogSync($lastCatalogSync) : null,
            'integrationPoints' => $this->integration_points,
        ];
    }
}
