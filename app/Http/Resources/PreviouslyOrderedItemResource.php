<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Modules\Order\Data\PreviouslyOrderedItem;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class PreviouslyOrderedItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var PreviouslyOrderedItem $this->resource */
        return [
            'product_offer_id' => $this->resource->product_offer_id,
            'product_id' => $this->resource->product_id,
            'product_name' => $this->resource->product_name,
            'vendor_id' => $this->resource->vendor_id,
            'vendor_name' => $this->resource->vendor_name,
            'last_ordered_at' => $this->resource->last_ordered_at->toIso8601String(),
            'quantity' => $this->resource->quantity,
            'price' => $this->resource->price,
            'image_url' => $this->resource->image_url,
            'order_count' => $this->resource->order_count,
            'stock_status' => $this->resource->stock_status->value,
            'increments' => $this->resource->increments,
        ];
    }
}
