<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Enums\ClinicOnboardingStatus;
use App\Modules\Integration\Enums\IntegrationConnectionRole;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class Clinic extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'business_tax_id' => $this->business_tax_id,
            'phone_number' => $this->phone_number,
            'shipping_address' => Address::make($this->shippingAddress),
            'billing_address' => Address::make(
                $this->is_billing_same_as_shipping_address
                    ? $this->shippingAddress
                    : $this->billingAddress,
            ),
            'same_address' => $this->is_billing_same_as_shipping_address,
            'budget_settings' => $this->whenLoaded('budgetSettings', fn () => ClinicBudgetSettings::make($this->budgetSettings)),
            /** @var bool */
            'has_any_vendor_connected' => $this->whenLoaded(
                'vendors',
                fn () => $this->vendors()->whereIn('status', [IntegrationConnectionStatus::Connecting, IntegrationConnectionStatus::Connected])->exists(),
            ),
            'onboarding_status' => $this->resolveClinicOnboardingStatus(),
            'species_focus' => $this->species_focus,
            'practice_types' => $this->practice_types,
            'fulltime_dvm_count' => $this->fulltime_dvm_count,
            'exam_rooms_count' => $this->exam_rooms_count,
            'primary_shopping_preference' => $this->primary_shopping_preference,
            'secondary_shopping_preferences' => $this->secondary_shopping_preferences,

            'primary_distributor' => VendorMinified::make($this->integrationConnections()
                ->where('role', IntegrationConnectionRole::PrimaryDistributor->value)
                ->first()?->vendor),

            'secondary_distributor' => VendorMinified::make($this->integrationConnections()
                ->where('role', IntegrationConnectionRole::SecondaryDistributor->value)
                ->first()?->vendor),

            'preferred_manufacturers' => VendorMinified::collection($this->integrationConnections()
                ->where('role', IntegrationConnectionRole::PreferredManufacturer->value)->get()->pluck('vendor')),
        ];
    }

    /**
     * Resolve the onboarding step for the clinic.
     */
    private function resolveClinicOnboardingStatus(): ClinicOnboardingStatus
    {
        return match (true) {
            (
                $this->business_tax_id === null
                || $this->phone_number === null
                || $this->billingAddress === null
                || $this->shippingAddress === null
            ) => ClinicOnboardingStatus::Registered,
            default => ClinicOnboardingStatus::Done,
        };
    }
}
