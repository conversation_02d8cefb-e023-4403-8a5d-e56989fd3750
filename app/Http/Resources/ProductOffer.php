<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Http\Resources\Concerns\FormatsPrice;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class ProductOffer extends JsonResource
{
    use FormatsPrice;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'vendor' => new Vendor($this->vendor),
            'vendor_sku' => $this->vendor_sku,
            'image_url' => $this->image_url,
            /** @var bool|null */
            'is_favorite' => $this->whenLoaded('clinics', function () {
                $clinic = $this->clinics->first();

                return $clinic && $clinic->pivot && ! is_null($clinic->pivot->favorited_at);
            }, false),
            'increments' => $this->increments,
            'size' => $this->size,
            'unit_of_measure' => $this->unit_of_measure,
            'stock_status' => $this->stock_status,
            'raw_category_1' => $this->raw_category_1,
            'raw_category_2' => $this->raw_category_2,
            'raw_category_3' => $this->raw_category_3,
            'raw_category_4' => $this->raw_category_4,
        ];
    }
}
