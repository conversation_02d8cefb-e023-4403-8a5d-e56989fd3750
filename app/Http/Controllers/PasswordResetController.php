<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Actions\SendPasswordResetLink;
use App\Http\Requests\PasswordResetRequest;
use Illuminate\Http\JsonResponse;

final class PasswordResetController extends Controller
{
    /**
     * Handle an incoming password reset request.
     */
    public function store(
        PasswordResetRequest $request,
        SendPasswordResetLink $action
    ): JsonResponse {
        $status = $action->handle($request->validated());

        return response()->json(['status' => __($status)], JsonResponse::HTTP_CREATED);
    }
}
