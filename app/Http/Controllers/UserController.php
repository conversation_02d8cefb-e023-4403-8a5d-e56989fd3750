<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Actions\CreateUser;
use App\Http\Requests\CreateUserRequest;
use App\Http\Resources\User as UserResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

final class UserController extends Controller
{
    /**
     * Create a new user and return their details.
     */
    public function store(
        CreateUserRequest $request,
        CreateUser $action
    ): JsonResponse {
        $user = $action->handle($request->validated());

        $user->load(['account', 'clinics']);

        return UserResource::make($user)
            ->response()
            ->setStatusCode(Response::HTTP_CREATED);
    }

    /**
     * Retrieve the authenticated user's details with their associated accounts.
     */
    public function show(Request $request): JsonResponse
    {
        $user = $request->user();

        $user->load(['account', 'clinics']);

        return UserResource::make($user)
            ->response();
    }
}
