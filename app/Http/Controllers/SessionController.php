<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\Auth\LoginRequest;
use App\Http\Resources\GpoUser;
use App\Http\Resources\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

final class SessionController extends Controller
{
    /**
     * Log in a user by validating their credentials and creating a new session.
     */
    public function createUserSession(LoginRequest $request): JsonResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        return User::make($request->user()->load(['account', 'clinics']))
            ->response()
            ->setStatusCode(JsonResponse::HTTP_CREATED);
    }

    /**
     * Log out the currently authenticated user by destroying their session.
     */
    public function destroyUserSession(Request $request): JsonResponse
    {
        Auth::guard()->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return response()->json([], JsonResponse::HTTP_NO_CONTENT);
    }

    public function createGpoSession(LoginRequest $request): JsonResponse
    {
        $request->authenticate('gpo');

        $request->session()->regenerate();

        return GpoUser::make(Auth::guard('gpo')->user()->load(['account']))
            ->response()
            ->setStatusCode(JsonResponse::HTTP_CREATED);
    }

    /**
     * Log out the currently authenticated user by destroying their session.
     */
    public function destroyGpoSession(Request $request): JsonResponse
    {
        Auth::guard('gpo')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return response()->json([], JsonResponse::HTTP_NO_CONTENT);
    }
}
