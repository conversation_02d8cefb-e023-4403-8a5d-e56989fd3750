<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics\Settings;

use App\Actions\UpdateClinicSettings;
use App\Enums\ClinicSettingsType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Clinics\Settings\UpdateClinicControlledDrugSettingsRequest;
use App\Models\Clinic;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;

final class ClinicControlledDrugsSettingsController extends Controller
{
    use AuthorizesRequests;

    /**
     * Get the given clinics controlled drugs settings.
     */
    public function show(Clinic $clinic): JsonResponse
    {
        $this->authorize('view', $clinic);
        $settings = $clinic->controlledDrugsSettings()->firstOrNew([], [
            'value' => [
                'controlledSubstancePurchases' => [
                    'controlledSubstancePct' => 0,
                    'nonControlledPrescriptionsPct' => 0,
                    'nonPrescriptionsPct' => 0,
                ],
                'orderingPattern' => null,
                'otherSuppliers' => null,
                'topControlledSubstances' => [],
                'intendedControlledSubstances' => [],
                'isRegisteredWithCsos' => null,
                'lastDeaInspectionDate' => null,
                'administerMedicationsOnSite' => null,
                'takeBackControlledSubstances' => null,
                'reasonTakingBackControlledSubstances' => null,
                'areLicensesCurrent' => null,
                'registrantIssues' => [],
                'inventoryManager' => null,
                'everCutOffFromPurchasing' => null,
                'maintainControlledSubstancesLog' => null,
                'reasonNotMaintainingLog' => null,
                'hasSecurityPolicies' => null,
                'reasonNoSecurityPolicies' => null,
                'areEmployeesTrained' => null,
                'reasonEmployeesNotTrained' => null,
                'hasOtherDeaBusinesses' => null,
                'reasonNotDeaPermit' => null,
            ],
        ]);

        return response()
            ->json($settings?->value)
            ->setStatusCode(JsonResponse::HTTP_OK);
    }

    /**
     * Update clinics controlled drugs settings.
     */
    public function update(
        UpdateClinicControlledDrugSettingsRequest $request,
        UpdateClinicSettings $action,
        Clinic $clinic
    ): JsonResponse {
        $action->handle($clinic, ClinicSettingsType::ControlledDrugs->value, $request->validated());
        $clinic->load(['controlledDrugsSettings']);

        return response()
            ->json($clinic->controlledDrugsSettings?->value)
            ->setStatusCode(JsonResponse::HTTP_OK);
    }
}
