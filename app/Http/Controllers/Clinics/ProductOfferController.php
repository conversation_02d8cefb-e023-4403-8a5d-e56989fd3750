<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Http\Controllers\Controller;
use App\Http\Resources\Cart\ProductOffer as ProductOfferResource;
use App\Models\Clinic;
use App\Models\ProductOffer;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;

final class ProductOfferController extends Controller
{
    use AuthorizesRequests;

    /**
     * Get the given product.
     */
    public function show(Clinic $clinic, ProductOffer $productOffer): JsonResponse
    {
        $this->authorize('view', $clinic);

        $productOffer = ProductOffer::with('vendor')
            ->withVendorConnected([$clinic->id])
            ->withClinic($clinic->id)
            ->withLastClinicOrderItem($clinic->id)
            ->withClinicCartItem($clinic->id)
            ->whereVendorIdIn($clinic->vendors->pluck('id')->unique()->values()->toArray())
            ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id))
            ->findOrFail($productOffer->id);

        return ProductOfferResource::make($productOffer)->response();
    }
}
