<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Http\Controllers\Controller;
use App\Http\Requests\Clinics\FavoriteProductRequest;
use App\Http\Resources\Cart\Product as ProductResource;
use App\Models\Clinic;
use App\Models\ClinicProductFavorite;
use App\Models\Product;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;

final class FavoriteProductController extends Controller
{
    use AuthorizesRequests;

    /**
     * Mark a product as favorite for the clinic.
     */
    public function store(FavoriteProductRequest $request, Clinic $clinic): JsonResponse
    {
        $productId = $request->validated('product_id');

        ClinicProductFavorite::create([
            'clinic_id' => $clinic->id,
            'product_id' => $productId,
            'favorited_at' => now(),
        ]);

        $product = Product::with(['productOffers' => function ($query) use ($clinic) {
            $query->withVendorConnected([$clinic->id]);
        }])->findOrFail($productId);

        return ProductResource::make($product)
            ->response()
            ->setStatusCode(JsonResponse::HTTP_CREATED);
    }

    /**
     * Remove product from clinic's favorites.
     */
    public function destroy(Clinic $clinic, string $product): JsonResponse
    {
        $this->authorize('view', $clinic);

        $product = Product::with(['productOffers' => function ($query) use ($clinic) {
            $query->withVendorConnected([$clinic->id]);
        }])->findOrFail($product);

        ClinicProductFavorite::where([
            'clinic_id' => $clinic->id,
            'product_id' => $product->id,
        ])->delete();

        return ProductResource::make($product->refresh())
            ->response()
            ->setStatusCode(JsonResponse::HTTP_OK);
    }
}
