<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Http\Controllers\Controller;
use App\Http\Requests\Clinics\UpdateCartItemRequest;
use App\Http\Resources\Cart\Cart;
use App\Http\Resources\Cart\LightweightCart;
use App\Models\CartItem;
use App\Modules\Cart\Services\CartService;
use App\Modules\Cart\Services\ProductOfferQueryService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;

final class CartItemController extends Controller
{
    use AuthorizesRequests;

    public function __construct(
        private readonly CartService $cartService,
        private readonly ProductOfferQueryService $productOfferQueryService
    ) {}

    /**
     * Update the cart item.
     */
    public function update(UpdateCartItemRequest $request, CartItem $item): JsonResponse
    {
        $clinic = $request->clinic();

        $product = $this->productOfferQueryService->getProductOfferForCart(
            $clinic,
            $request->validated('product_offer_id')
        );

        if (! $product) {
            abort(404, 'Product offer not found or not accessible');
        }

        $quantity = $request->validated('quantity') ?? $item->quantity;
        $notes = $request->validated('notes') ?? $item->notes;

        // Remove the old item and add the new product to the cart
        $this->cartService->removeItem($clinic, $item->product_offer_id);
        $cart = $this->cartService->updateItem($clinic, $product->id, $quantity, $notes);

        return LightweightCart::make($cart)
            ->response()
            ->setStatusCode(JsonResponse::HTTP_OK);
    }
}
