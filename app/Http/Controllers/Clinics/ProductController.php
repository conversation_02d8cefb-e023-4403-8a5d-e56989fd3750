<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Http\Controllers\Controller;
use App\Http\Resources\Cart\Product as ProductResource;
use App\Http\Resources\Promotion as PromotionResource;
use App\Models\Clinic;
use App\Models\Product;
use App\Modules\Promotion\Services\ProductPromotionService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;

final class ProductController extends Controller
{
    use AuthorizesRequests;

    /**
     * Get the given product.
     */
    public function show(Clinic $clinic, Product $product): JsonResponse
    {
        $this->authorize('view', $clinic);

        $product = Product::with(['attributes', 'searchTerms', 'productOffers', 'productOffers.vendor'])
            ->with(['productOffers' => function ($query) use ($clinic) {
                $query->withVendorConnected([$clinic->id])
                    ->withClinic($clinic->id)
                    ->withLastClinicOrderItem($clinic->id)
                    ->withClinicCartItem($clinic->id)
                    ->whereVendorIdIn($clinic->vendors->pluck('id')->unique()->values()->toArray())
                    ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id));
            }])
            ->findOrFail($product->id);

        // Add the clinic to the product's clinics collection
        $product->setRelation('clinics', collect([$clinic]));

        return ProductResource::make($product)->response();
    }

    public function promotions(Clinic $clinic, Product $product): JsonResource
    {
        $this->authorize('view', $clinic);

        $promotionService = app(ProductPromotionService::class);
        $promotions = $promotionService->getActivePromotionsForProduct($product, $clinic->id);

        return PromotionResource::collection($promotions);
    }
}
