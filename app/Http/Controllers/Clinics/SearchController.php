<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Actions\Products\SearchProducts;
use App\Http\Controllers\Controller;
use App\Http\Resources\Cart\Product as ProductResource;
use App\Models\Clinic;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class SearchController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        private readonly SearchProducts $engine,
    ) {
        //
    }

    /**
     * Search for products.
     */
    public function index(Request $request, Clinic $clinic): JsonResponse
    {

        $products = $this->engine->handle(
            $clinic,
            $request->string('query')->toString(),
            $request->json('filter', [])
        );

        return ProductResource::collection($products)
            ->response();
    }
}
