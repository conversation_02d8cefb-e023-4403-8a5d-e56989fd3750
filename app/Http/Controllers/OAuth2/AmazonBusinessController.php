<?php

declare(strict_types=1);

namespace App\Http\Controllers\OAuth2;

use App\Http\Controllers\Controller;
use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

final class AmazonBusinessController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'code' => 'required_without:auth_code|string',
            'auth_code' => 'required_without:code|string',
            'state' => 'required|string',
            'amazon_callback_uri' => 'sometimes|string',
            'amazon_state' => 'required_with:amazon_callback_uri|string',
        ]);

        $code = $request->code ?? $request->auth_code;

        $response = Http::asForm()->post('https://api.amazon.com/auth/O2/token', [
            'grant_type' => 'authorization_code',
            'code' => $code,
            'client_id' => config('highfive.amazon.client_id'),
            'client_secret' => config('highfive.amazon.client_secret'),
            'redirect_uri' => config('app.url').'/api/oauth2/amazon-business',
        ]);

        $data = $response->json();

        if (! isset($data['refresh_token'])) {
            if (isset($request->amazon_callback_uri)) {
                return redirect("$request->amazon_callback_uri?amazon_state=$request->amazon_state&status=auth_code_use_failure");
            }

            return redirect(config('app.frontend_url').'/vendors?amazon_auth_state=failure');
        }

        $clinic = Clinic::findOrFail($request->state);
        $vendor = Vendor::where('key', 'AMZN')->first();

        $connection = IntegrationConnection::query()
            ->where('clinic_id', $clinic->id)
            ->where('vendor_id', $vendor->id)
            ->first();

        $connection->update([
            'credentials' => [
                ...$connection->credentials,
                'refresh_token' => $data['refresh_token'],
            ],
            'status' => IntegrationConnectionStatus::Connected,
        ]);

        if (isset($request->amazon_callback_uri)) {
            return redirect("$request->amazon_callback_uri?amazon_state=$request->amazon_state&status=auth_code_use_successful");
        }

        return redirect(config('app.frontend_url').'/vendors?amazon_auth_state=success');
    }
}
