<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\CartSummary;
use App\Models\Clinic;
use App\Modules\Cart\Services\CartService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;

final class ClinicCartSummaryController extends Controller
{
    use AuthorizesRequests;

    public function __construct(
        private readonly CartService $cartService
    ) {}

    /**
     * Get the cart summary for the given clinic.
     */
    public function show(Clinic $clinic): JsonResponse
    {
        $this->authorize('view', $clinic);

        $cart = $this->cartService->getCart($clinic);

        return CartSummary::make($cart)->response();
    }
}
