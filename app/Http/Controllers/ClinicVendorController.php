<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Actions\Clinics\ConnectToVendor;
use App\Http\Requests\ConnectClinicToVendorRequest;
use App\Http\Resources\Vendor;
use App\Models\Clinic;
use App\Models\Vendor as VendorModel;
use Illuminate\Http\JsonResponse;

final class ClinicVendorController extends Controller
{
    /**
     * Get the vendors that the clinic is connected to.
     */
    public function index(Clinic $clinic): JsonResponse
    {
        return Vendor::collection(VendorModel::isEnabled()->orderBy('name')->get())->response();
    }

    /**
     * Attempt to connect the clinic to a vendor.
     */
    public function store(
        ConnectClinicToVendorRequest $request,
        ConnectToVendor $action,
        Clinic $clinic
    ): JsonResponse {
        $action->handle($clinic, $request->validated());

        return Vendor::collection(VendorModel::isEnabled()->orderBy('name')->get())
            ->response()
            ->setStatusCode(JsonResponse::HTTP_CREATED);
    }
}
