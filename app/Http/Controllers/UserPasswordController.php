<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Actions\ResetUserPassword;
use App\Http\Requests\ResetPasswordRequest;
use App\Http\Resources\User;
use Illuminate\Http\JsonResponse;

final class UserPasswordController extends Controller
{
    /**
     * Reset the user's password.
     */
    public function update(
        ResetPasswordRequest $request,
        ResetUserPassword $action
    ): JsonResponse {
        $user = $action->handle($request->validated());

        return User::make($user)->response();
    }
}
