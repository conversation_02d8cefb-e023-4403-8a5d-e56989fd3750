<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Models\Vendor;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;

final class ConnectClinicToVendorRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('update', $this->route('clinic'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'vendor_id' => [
                'required',
                (new Exists(Vendor::class, 'id'))->where('is_enabled', true),
            ],
            'credentials' => ['required', 'array'],
            'credentials.api_key' => ['string', 'nullable'],
            'credentials.username' => ['string', 'nullable'],
            'credentials.password' => ['string', 'nullable'],
        ];
    }
}
