<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Enums\PaymentMethod;
use App\Models\Clinic;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

final class PlaceClinicOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('view', [Clinic::class, $this->route('clinic')]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'payment_method' => ['required', 'string', new Enum(PaymentMethod::class)],
            'is_billing_same_as_shipping_address' => ['boolean'],

            'shipping_address' => ['required', 'array'],
            'shipping_address.street' => ['required', 'string'],
            'shipping_address.city' => ['required', 'string'],
            'shipping_address.state' => ['required', 'string'],
            'shipping_address.postal_code' => ['required', 'string'],

            'billing_address' => ['required_if:is_billing_same_as_shipping_address,false', 'array'],
            'billing_address.street' => ['required_if:is_billing_same_as_shipping_address,false', 'string'],
            'billing_address.city' => ['required_if:is_billing_same_as_shipping_address,false', 'string'],
            'billing_address.state' => ['required_if:is_billing_same_as_shipping_address,false', 'string'],
            'billing_address.postal_code' => ['required_if:is_billing_same_as_shipping_address,false', 'string'],
        ];
    }
}
