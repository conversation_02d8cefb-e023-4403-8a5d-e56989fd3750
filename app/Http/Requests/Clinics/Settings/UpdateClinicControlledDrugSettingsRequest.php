<?php

declare(strict_types=1);

namespace App\Http\Requests\Clinics\Settings;

use App\Enums\ClinicControlledDrugsOrderFrequency;
use App\Models\Clinic;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

final class UpdateClinicControlledDrugSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('update', [Clinic::class, $this->route('clinic')]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'controlled_substance_purchases' => ['array', 'nullable'],
            'controlled_substance_purchases.controlled_substance_pct' => ['decimal:0,2', 'min:0'],
            'controlled_substance_purchases.non_controlled_prescriptions_pct' => ['decimal:0,2', 'min:0'],
            'controlled_substance_purchases.non_prescriptions_pct' => ['decimal:0,2', 'min:0'],
            'ordering_pattern' => ['nullable', new Enum(ClinicControlledDrugsOrderFrequency::class)],
            'other_suppliers' => ['string', 'nullable'],
            'top_controlled_substances' => ['array', 'nullable', 'max:5'],
            'top_controlled_substances.*.product_name' => ['string', 'nullable'],
            'top_controlled_substances.*.quantity' => ['integer', 'numeric', 'nullable'],
            'intended_controlled_substances' => ['array', 'nullable', 'max:5'],
            'intended_controlled_substances.*.product_name' => ['string', 'nullable'],
            'intended_controlled_substances.*.strength' => ['string', 'nullable'],
            'intended_controlled_substances.*.quantity' => ['integer', 'numeric', 'nullable'],
            'intended_controlled_substances.*.frequency' => ['nullable', new Enum(ClinicControlledDrugsOrderFrequency::class)],
            'is_registered_with_csos' => ['boolean', 'nullable'],
            'last_dea_inspection_date' => ['date', 'nullable'],
            'administer_medications_on_site' => ['boolean', 'nullable'],
            'take_back_controlled_substances' => ['boolean', 'nullable'],
            'reason_taking_back_controlled_substances' => ['string', 'nullable', 'required_if:take_back_controlled_substances,true'],
            'are_licenses_current' => ['boolean', 'nullable'],
            'registrant_issues' => ['array', 'nullable'],
            'registrant_issues.*' => ['string', 'nullable'],
            'inventory_manager' => ['string', 'nullable'],
            'ever_cut_off_from_purchasing' => ['boolean', 'nullable'],
            'maintain_controlled_substances_log' => ['boolean', 'nullable'],
            'reason_not_maintaining_log' => ['string', 'nullable', 'required_if:maintain_controlled_substances_log,false'],
            'has_security_policies' => ['boolean', 'nullable'],
            'reason_no_security_policies' => ['string', 'nullable', 'required_if:has_security_policies,false'],
            'are_employees_trained' => ['boolean', 'nullable'],
            'reason_employees_not_trained' => ['string', 'nullable', 'required_if:are_employees_trained,false'],
            'has_other_dea_businesses' => ['boolean', 'nullable'],
            'reason_not_dea_permit' => ['string', 'nullable', 'required_if:has_other_dea_businesses,false'],
        ];
    }
}
