<?php

declare(strict_types=1);

namespace App\Modules\Integration\Actions;

use App\Models\Vendor;
use App\Modules\Integration\Data\IntegrationConnection;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Events\ConnectingIntegration;
use App\Modules\Integration\Models\IntegrationConnection as IntegrationConnectionModel;

final class CreateIntegrationConnection
{
    public function handle(
        string $integrationId,
        string $clinicId,
        array $metadata
    ): IntegrationConnection {

        $vendor = Vendor::query()->findOrFail($integrationId);

        IntegrationConnectionModel::query()
            ->updateOrCreate([
                'clinic_id' => $clinicId,
                'vendor_id' => $vendor->id,
            ], [
                'credentials' => $metadata,
                'status' => IntegrationConnectionStatus::Connecting,
            ]);

        ConnectingIntegration::dispatch($clinicId, $integrationId);

        return IntegrationConnection::from([
            'status' => IntegrationConnectionStatus::Connecting,
            'redirect_uri' => "https://www.amazon.com/b2b/abws/oauth?state={$clinicId}&redirect_uri=https://services.highfive.vet/api/oauth2/amazon-business&applicationId=amzn1.sp.solution.b83d6cb9-da87-4673-ab22-f4854a79d9a4",
        ]);
    }
}
