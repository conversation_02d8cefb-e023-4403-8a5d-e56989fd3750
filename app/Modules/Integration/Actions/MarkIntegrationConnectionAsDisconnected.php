<?php

declare(strict_types=1);

namespace App\Modules\Integration\Actions;

use App\Modules\Integration\Models\IntegrationConnection;

final class MarkIntegrationConnectionAsDisconnected
{
    public function handle(string $connectionId, string $message): void
    {
        $connection = IntegrationConnection::query()->findOrFail($connectionId);

        $connection->markAsDisconnected($message);
    }
}
