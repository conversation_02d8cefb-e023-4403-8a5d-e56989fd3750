<?php

declare(strict_types=1);

namespace App\Modules\Integration\Mails;

use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

final class NotifyIntegrationDisconnected extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(private readonly IntegrationConnection $integrationConnection)
    {
        //
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: '⛓️‍💥 Highfive Integrations – Account Disconnected',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $integrationName = $this->integrationConnection->vendor->name;

        return new Content(
            markdown: 'mail.integration-disconnected',
            with: [
                'integrationName' => $integrationName,
            ]
        );
    }
}
