<?php

declare(strict_types=1);

namespace App\Modules\Integration\Services\Vendor;

use App\Models\Order;
use App\Models\Vendor;
use App\Models\WebhookRequest;
use App\Modules\Integration\Contracts\WebhookHandler;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Enums\IntegrationSessionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

final class PlaceOrderBotWebhookHandler implements WebhookHandler
{
    public function handle(WebhookRequest $webhookRequest): void
    {
        Log::info('Processing PlaceOrderBot webhook', [
            'slug' => $webhookRequest->slug,
            'vendor_id' => $webhookRequest->vendor_id,
            'payload' => $webhookRequest->payload,
        ]);

        match ($webhookRequest->slug) {
            'order.confirmation' => $this->handleOrderConfirmation($webhookRequest),
            default => throw new InvalidArgumentException("Unsupported Place Order Bot webhook event: {$webhookRequest->slug}"),
        };
    }

    public function validateWebhook(array $headers, string $payload): bool
    {
        if (! isset($headers['x-webhook-signature'])) {
            return false;
        }

        if (config('highfive.place_order_bot.webhook_secret') === $headers['x-webhook-signature'][0]) {
            return true;
        }

        return true;
    }

    private function handleOrderConfirmation(WebhookRequest $webhookRequest): void
    {
        $payload = $webhookRequest->payload;
        Log::info('Handling PlaceOrderBot order confirmation webhook', [
            'payload' => $payload,
        ]);

        $order = Order::where('order_number', $payload['reference'] ?? null)->first();
        if (! $order) {
            Log::warning('Order not found for Place Order Bot webhook', [
                'reference' => $payload['reference'] ?? null,
                'slug' => $webhookRequest->slug,
            ]);

            return;
        }
        $vendor = Vendor::where('slug', $payload['vendor'])->first();
        $subOrderFromVendor = $order->subOrders()
            ->where('vendor_id', $vendor->id)
            ->first();
        $integrationConnection = IntegrationConnection::whereClinicId($order->clinic_id)
            ->whereVendorId($vendor->id)
            ->first();
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::PlaceOrders,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);
        $session->logSuccessEvent('PlaceOrderBotWebhookHandler::handleOrderConfirmation', $subOrderFromVendor, $payload);
    }
}
