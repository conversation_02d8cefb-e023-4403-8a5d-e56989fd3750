<?php

declare(strict_types=1);

namespace App\Modules\Integration\Services\Vendor;

use App\Models\WebhookRequest;
use App\Modules\Integration\Contracts\WebhookHandler;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

final class PattersonWebhookHandler implements WebhookHandler
{
    public function handle(WebhookRequest $webhookRequest): void
    {
        Log::info('Processing Patterson webhook', [
            'slug' => $webhookRequest->slug,
            'vendor_id' => $webhookRequest->vendor_id,
            'payload' => $webhookRequest->payload,
        ]);

        // Handle Patterson webhook events based on slug
        match ($webhookRequest->slug) {
            'order.confirmation' => $this->handleOrderConfirmation($webhookRequest),
            default => throw new InvalidArgumentException("Unsupported Patterson webhook event: {$webhookRequest->slug}"),
        };
    }

    public function validateWebhook(array $headers, string $payload): bool
    {
        // TODO: Implement Patterson webhook signature validation
        // - <PERSON> doesn't provide webhook validation/secret yet
        // - <PERSON> need to implement signature validation once <PERSON> provides the method
        // - For now, accepting all webhooks from Patterson

        return true;
    }

    private function handleOrderConfirmation(WebhookRequest $webhookRequest): void
    {
        $payload = $webhookRequest->payload;
        Log::info('Handling Patterson order confirmation webhook', [
            'payload' => $payload,
        ]);
    }
}
