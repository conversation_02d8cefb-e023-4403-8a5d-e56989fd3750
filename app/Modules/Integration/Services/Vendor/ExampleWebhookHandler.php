<?php

declare(strict_types=1);

namespace App\Modules\Integration\Services\Vendor;

use App\Models\WebhookRequest;
use App\Modules\Integration\Contracts\WebhookHandler;
use InvalidArgumentException;

final class ExampleWebhookHandler implements WebhookHandler
{
    public function handle(WebhookRequest $webhookRequest): void
    {
        // Example implementation of handling different webhook events based on slug
        match ($webhookRequest->slug) {
            'order.created' => $this->handleOrderCreated($webhookRequest),
            'order.updated' => $this->handleOrderUpdated($webhookRequest),
            'order.cancelled' => $this->handleOrderCancelled($webhookRequest),
            default => throw new InvalidArgumentException('Unsupported webhook event'),
        };
    }

    public function validateWebhook(array $headers, string $payload): bool
    {
        // Example implementation of webhook signature validation
        $signature = $headers['X-Webhook-Signature'][0] ?? null;
        if (! $signature) {
            return false;
        }

        // In a real implementation, you would use the vendor's specific signature validation method
        // This is just an example using a simple HMAC
        $secret = config('services.example_vendor.webhook_secret');
        $expectedSignature = hash_hmac('sha256', $payload, $secret);

        return hash_equals($signature, $expectedSignature);
    }

    private function handleOrderCreated(WebhookRequest $webhookRequest): void
    {
        // Example implementation
        $payload = $webhookRequest->payload;
        // Process order created event...
    }

    private function handleOrderUpdated(WebhookRequest $webhookRequest): void
    {
        // Example implementation
        $payload = $webhookRequest->payload;
        // Process order updated event...
    }

    private function handleOrderCancelled(WebhookRequest $webhookRequest): void
    {
        // Example implementation
        $payload = $webhookRequest->payload;
        // Process order cancelled event...
    }
}
