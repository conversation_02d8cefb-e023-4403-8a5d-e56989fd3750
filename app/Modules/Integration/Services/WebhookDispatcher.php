<?php

declare(strict_types=1);

namespace App\Modules\Integration\Services;

use App\Enums\WebhookRequestStatus;
use App\Models\Vendor;
use App\Models\WebhookRequest;
use App\Modules\Integration\Contracts\WebhookHandler;
use App\Modules\Integration\Exceptions\InvalidWebhookException;
use App\Modules\Integration\Exceptions\WebhookHandlerNotFoundException;
use App\Modules\Integration\Services\Vendor\PlaceOrderBotWebhookHandler;
use Exception;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

final class WebhookDispatcher
{
    /**
     * Process an incoming webhook request.
     *
     * @param  string  $vendorKey  The vendor identifier
     * @param  string|null  $slug  Optional slug for event type or context
     * @param  array  $headers  Request headers
     * @param  array  $payload  Request payload
     * @param  string  $method  The HTTP method used for the request
     *
     * @throws InvalidWebhookException|WebhookHandlerNotFoundException
     */
    public function dispatch(string $vendorKey, ?string $slug, array $headers, array $payload, string $method): void
    {
        $isPlaceOrderBot = $vendorKey === 'placeOrderBot';
        // Find the vendor by key
        $vendor = Vendor::where('key', $vendorKey)->first();
        if (! $vendor && ! $isPlaceOrderBot) {
            throw new WebhookHandlerNotFoundException("No vendor found for key: {$vendorKey}");
        }

        // Create webhook request record
        $webhookRequest = WebhookRequest::create([
            'vendor_id' => $vendor->id ?? null,
            'method' => $method,
            'slug' => $slug,
            'headers' => $headers,
            'payload' => $payload,
            'status' => WebhookRequestStatus::Pending,
        ]);

        try {
            // Get the handler class from the vendor's configuration
            $handlerClass = $this->getWebhookHandlerClass($vendor, $isPlaceOrderBot);
            if (! class_exists($handlerClass)) {
                throw new WebhookHandlerNotFoundException("Handler class not found: {$handlerClass}");
            }

            /** @var WebhookHandler $handler */
            $handler = app($handlerClass);

            // Validate webhook signature/auth
            if (! $handler->validateWebhook($headers, json_encode($payload))) {
                throw new InvalidWebhookException('Invalid webhook signature');
            }

            // Process the webhook
            $handler->handle($webhookRequest);

            // Update status to completed
            $webhookRequest->update(['status' => WebhookRequestStatus::Completed]);

        } catch (InvalidArgumentException $e) {
            // Handle unsupported webhook events - this is a client error, not server error
            Log::warning('Unsupported webhook event', [
                'vendor_key' => $vendorKey,
                'slug' => $slug,
                'error' => $e->getMessage(),
            ]);

            $webhookRequest->update([
                'status' => WebhookRequestStatus::Failed,
                'error_message' => $e->getMessage(),
            ]);

            throw $e;
        } catch (Exception $e) {
            // Log error and update webhook request status
            Log::error('Webhook processing failed', [
                'vendor_key' => $vendorKey,
                'slug' => $slug,
                'error' => $e->getMessage(),
            ]);

            $webhookRequest->update([
                'status' => WebhookRequestStatus::Failed,
                'error_message' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Get the webhook handler class from the vendor's configuration.
     */
    private function getWebhookHandlerClass(?Vendor $vendor, bool $isPlaceOrderBot): string
    {
        if ($isPlaceOrderBot) {
            return PlaceOrderBotWebhookHandler::class;
        }

        $config = $vendor->authentication_configuration;
        if (! isset($config['webhook_handler'])) {
            throw new WebhookHandlerNotFoundException('No webhook handler configured for vendor');
        }

        return $config['webhook_handler'];
    }
}
