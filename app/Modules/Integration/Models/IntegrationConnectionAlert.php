<?php

declare(strict_types=1);

namespace App\Modules\Integration\Models;

use App\Modules\Integration\Enums\IntegrationConnectionAlertType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class IntegrationConnectionAlert extends Model
{
    use HasUuids;

    protected $guarded = [];

    public function connection(): BelongsTo
    {
        return $this->belongsTo(IntegrationConnection::class, 'integration_connection_id');
    }

    protected function cast(): array
    {
        return [
            'type' => IntegrationConnectionAlertType::class,
        ];
    }
}
