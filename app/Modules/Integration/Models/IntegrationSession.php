<?php

declare(strict_types=1);

namespace App\Modules\Integration\Models;

use App\Modules\Integration\Enums\IntegrationEventStatus;
use App\Modules\Integration\Enums\IntegrationSessionStatus;
use Database\Factories\Modules\Integration\IntegrationSessionFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class IntegrationSession extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = [];

    protected $casts = [
        'finished_at' => 'datetime',
    ];

    public static function newFactory()
    {
        return IntegrationSessionFactory::new();
    }

    public function connection(): BelongsTo
    {
        return $this->belongsTo(IntegrationConnection::class, 'integration_connection_id');
    }

    public function events()
    {
        return $this->hasMany(IntegrationEvent::class);
    }

    public function success(): void
    {
        $this->update([
            'status' => IntegrationSessionStatus::Succeeded,
            'finished_at' => now(),
        ]);
    }

    public function failed(): void
    {
        $this->update([
            'status' => IntegrationSessionStatus::Failed,
            'finished_at' => now(),
        ]);
    }

    /**
     * Log a generic event with a given status.
     */
    public function logEvent(
        IntegrationEventStatus $status,
        string $action,
        $subject, // Eloquent model
        array $metadata = []
    ): IntegrationEvent {
        return $this->events()->create([
            'status' => $status->value,
            'action' => $action,
            'subject_type' => $subject ? get_class($subject) : null,
            'subject_id' => $subject?->id,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Log a success event.
     */
    public function logSuccessEvent(
        string $action,
        $subject, // Eloquent model
        array $metadata = []
    ): IntegrationEvent {
        return $this->logEvent(IntegrationEventStatus::Success, $action, $subject, $metadata);
    }

    /**
     * Log an error event.
     */
    public function logErrorEvent(
        string $action,
        $subject, // Eloquent model
        array $metadata = []
    ): IntegrationEvent {
        return $this->logEvent(IntegrationEventStatus::Error, $action, $subject, $metadata);
    }
}
