<?php

declare(strict_types=1);

namespace App\Modules\Integration\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

final class IntegrationEvent extends Model
{
    use HasUuids;

    protected $guarded = [];

    protected $casts = [
        'metadata' => 'array',
    ];

    public function session(): BelongsTo
    {
        return $this->belongsTo(IntegrationSession::class, 'integration_session_id');
    }

    public function connection(): BelongsTo
    {
        return $this->belongsTo(IntegrationConnection::class);
    }

    public function subject(): MorphTo
    {
        return $this->morphTo();
    }
}
