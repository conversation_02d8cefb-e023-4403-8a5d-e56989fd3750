<?php

declare(strict_types=1);

namespace App\Modules\Integration\Jobs;

use App\Modules\CatalogSync\Contracts\FetchClient;
use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;
use App\Modules\CatalogSync\Models\CatalogSyncTask;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

final class UpdateCatalogSyncTaskStatus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 10;

    public function __construct(private readonly CatalogSyncTask $task) {}

    public function handle(FetchClient $fetch): void
    {
        $status = $fetch->getCatalogSyncTaskStatus($this->task->id);

        if (in_array($status, [CatalogSyncTaskStatus::Pending, CatalogSyncTaskStatus::Running])) {
            $this->release(10 * 60);
        }

        if ($status === CatalogSyncTaskStatus::Succeeded) {
            $this->task->connection->removeAlert();
        }

        if ($status === CatalogSyncTaskStatus::Failed) {
            $this->task->connection->markAsDisconnected(
                '**We couldn’t authenticate with your vendor account.** Please verify that your credentials are correct and that you’re able to access the vendor platform directly using them.'
            );
        }

        $this->task->update(['status' => $status]);
    }
}
