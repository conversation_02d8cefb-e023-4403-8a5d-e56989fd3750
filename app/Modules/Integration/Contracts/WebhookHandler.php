<?php

declare(strict_types=1);

namespace App\Modules\Integration\Contracts;

use App\Models\WebhookRequest;
use Exception;

interface WebhookHandler
{
    /**
     * Handle an incoming webhook request.
     *
     * @param  WebhookRequest  $webhookRequest  The webhook request to process
     *
     * @throws Exception If the webhook cannot be processed
     */
    public function handle(WebhookRequest $webhookRequest): void;

    /**
     * Validate the webhook request signature or authentication.
     *
     * @param  array  $headers  The request headers
     * @param  string  $payload  The raw request payload
     * @return bool Whether the webhook is valid
     */
    public function validateWebhook(array $headers, string $payload): bool;
}
