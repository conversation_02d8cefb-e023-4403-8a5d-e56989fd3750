<?php

declare(strict_types=1);

namespace App\Modules\Integration\Data;

use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;

#[MapInputName(SnakeCaseMapper::class)]
final class IntegrationConnection extends Data
{
    public function __construct(
        public IntegrationConnectionStatus $status,
        public ?string $redirectUri = null,
    ) {}
}
