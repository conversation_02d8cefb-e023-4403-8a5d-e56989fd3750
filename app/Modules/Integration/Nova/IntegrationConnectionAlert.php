<?php

declare(strict_types=1);

namespace App\Modules\Integration\Nova;

use App\Modules\Integration\Enums\IntegrationConnectionAlertType;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Markdown;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Resource;

final class IntegrationConnectionAlert extends Resource
{
    public static $model = \App\Modules\Integration\Models\IntegrationConnectionAlert::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Integration Connection', 'connection', IntegrationConnection::class)
                ->required()
                ->searchable()
                ->sortable(),

            Select::make('Type', 'type')
                ->required()
                ->options(IntegrationConnectionAlertType::class)
                ->displayUsingLabels()
                ->sortable(),

            Markdown::make('Message', 'message')
                ->required(),
        ];
    }
}
