<?php

declare(strict_types=1);

namespace App\Modules\Integration\Nova;

use App\Modules\Integration\Enums\IntegrationConnectionRole;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use Laravel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\HasOne;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Resource;

final class IntegrationConnection extends Resource
{
    public static $model = \App\Modules\Integration\Models\IntegrationConnection::class;

    public static $title = 'id';

    public static $search = [
        'id',
        'clinic.name',
        'vendor.name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Clinic')
                ->required()
                ->searchable()
                ->sortable(),

            BelongsTo::make('Vendor')
                ->required()
                ->searchable()
                ->sortable(),

            Select::make('Role')
                ->nullable()
                ->options(IntegrationConnectionRole::labels())
                ->sortable(),

            Code::make('Credentials')
                ->required()
                ->json(),

            Select::make('Status')
                ->required()
                ->options(IntegrationConnectionStatus::class)
                ->displayUsingLabels()
                ->sortable(),

            Text::make('Status Reason')
                ->nullable()
                ->hideFromIndex(),

            HasOne::make('Alert', 'alert', IntegrationConnectionAlert::class),
        ];
    }
}
