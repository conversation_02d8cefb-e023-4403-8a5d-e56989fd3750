<?php

declare(strict_types=1);

use App\Modules\Integration\Http\Controllers\ConnectController;
use App\Modules\Integration\Http\Controllers\WebhookController;
use Illuminate\Support\Facades\Route;

Route::middleware(['api'])->prefix('api')->group(function () {
    Route::middleware(['auth:sanctum', 'header:highfive-clinic'])->post('integrations/{vendor}/connect', ConnectController::class);

    // Webhook endpoint - no authentication middleware as each vendor will handle their own validation
    Route::any('webhook/{vendorKey}/{slug?}', WebhookController::class);
});
