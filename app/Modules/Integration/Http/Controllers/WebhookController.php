<?php

declare(strict_types=1);

namespace App\Modules\Integration\Http\Controllers;

use App\Modules\Integration\Exceptions\InvalidWebhookException;
use App\Modules\Integration\Exceptions\WebhookHandlerNotFoundException;
use App\Modules\Integration\Services\WebhookDispatcher;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

final readonly class WebhookController
{
    public function __construct(private WebhookDispatcher $dispatcher) {}

    public function __invoke(Request $request, string $vendorKey, ?string $slug = null): JsonResponse
    {
        try {
            // Get payload - handle JSON, XML, and form data
            $payload = $this->getPayload($request);

            $this->dispatcher->dispatch(
                vendorKey: $vendorKey,
                slug: $slug,
                headers: $request->headers->all(),
                payload: $payload,
                method: $request->method()
            );

            return response()->json(['message' => 'Webhook processed successfully'], JsonResponse::HTTP_OK);

        } catch (WebhookHandlerNotFoundException $e) {
            Log::warning('Webhook handler not found', [
                'vendor_key' => $vendorKey,
                'slug' => $slug,
                'error' => $e->getMessage(),
            ]);

            return response()->json(['error' => $e->getMessage()], JsonResponse::HTTP_NOT_FOUND);

        } catch (InvalidWebhookException $e) {
            Log::warning('Invalid webhook signature', [
                'vendor_key' => $vendorKey,
                'slug' => $slug,
                'error' => $e->getMessage(),
            ]);

            return response()->json(['error' => 'Invalid webhook signature'], JsonResponse::HTTP_UNAUTHORIZED);

        } catch (InvalidArgumentException $e) {
            Log::warning('Invalid webhook request', [
                'vendor_key' => $vendorKey,
                'slug' => $slug,
                'error' => $e->getMessage(),
            ]);

            return response()->json(['error' => $e->getMessage()], JsonResponse::HTTP_BAD_REQUEST);

        } catch (Exception $e) {
            Log::error('Webhook processing failed', [
                'vendor_key' => $vendorKey,
                'slug' => $slug,
                'error' => $e->getMessage(),
            ]);

            return response()->json(['error' => 'Internal server error'], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Extract payload from request - handles JSON, XML, and any other content type.
     */
    private function getPayload(Request $request): array
    {
        $contentType = $request->header('Content-Type', '');

        // Handle JSON requests
        if ($request->isJson()) {
            return $request->json()->all();
        }

        // Handle XML requests
        if (str_contains($contentType, 'application/xml') || str_contains($contentType, 'text/xml')) {
            return ['xml_content' => $request->getContent()];
        }

        // Fallback for any other content type
        $rawContent = $request->getContent();
        if (! empty($rawContent)) {
            return [
                'content_type' => $contentType,
                'raw_content' => $rawContent,
            ];
        }

        // If no raw content, try form data
        return $request->all();
    }
}
