<?php

declare(strict_types=1);

namespace App\Modules\Integration\Http\Controllers;

use App\Models\Vendor;
use App\Modules\Integration\Actions\CreateIntegrationConnection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final readonly class ConnectController
{
    public function __construct(private CreateIntegrationConnection $action) {}

    public function __invoke(Request $request, Vendor $vendor): JsonResponse
    {
        $data = $request->validate([
            'buying_group_id' => ['required', 'string'],
        ]);

        $connection = $this->action->handle(
            integrationId: $vendor->id,
            clinicId: $request->clinicId(),
            metadata: $data,
        );

        return response()->json($connection, JsonResponse::HTTP_CREATED);
    }
}
