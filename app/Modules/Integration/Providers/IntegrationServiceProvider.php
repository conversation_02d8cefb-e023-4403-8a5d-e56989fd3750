<?php

declare(strict_types=1);

namespace App\Modules\Integration\Providers;

use App\Modules\Integration\Nova\IntegrationConnection;
use App\Modules\Integration\Nova\IntegrationConnectionAlert;
use Illuminate\Support\ServiceProvider;
use Laravel\Nova\Nova;

final class IntegrationServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->registerNovaResources();

        $this->registerRoutes();
    }

    private function registerNovaResources(): void
    {
        Nova::resources([
            IntegrationConnection::class,
            IntegrationConnectionAlert::class,
        ]);
    }

    private function registerRoutes(): void
    {
        $this->loadRoutesFrom(__DIR__.'/../Http/routes.php');
    }
}
