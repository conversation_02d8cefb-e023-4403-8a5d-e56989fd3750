<?php

declare(strict_types=1);

namespace App\Modules\Integration\Enums;

enum IntegrationConnectionRole: string
{
    case PrimaryDistributor = 'primary_distributor';
    case SecondaryDistributor = 'secondary_distributor';
    case PreferredManufacturer = 'preferred_manufacturer';

    public static function labels(): array
    {
        return [
            self::PrimaryDistributor->value => 'Primary Distributor',
            self::SecondaryDistributor->value => 'Secondary Distributor',
            self::PreferredManufacturer->value => 'Preferred Manufacturer',
        ];
    }
}
