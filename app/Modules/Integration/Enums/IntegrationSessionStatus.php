<?php

declare(strict_types=1);

namespace App\Modules\Integration\Enums;

enum IntegrationSessionStatus: string
{
    case Started = 'started';
    case Succeeded = 'succeeded';
    case Failed = 'failed';

    public static function colors(): array
    {
        return [
            self::Started->value => 'info',
            self::Succeeded->value => 'success',
            self::Failed->value => 'danger',
        ];
    }
}
