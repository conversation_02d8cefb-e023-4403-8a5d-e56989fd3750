<?php

declare(strict_types=1);

namespace App\Modules\Integration\Enums;

enum IntegrationPoint: string
{
    case SyncProductCatalog = 'sync_product_catalog';
    case PlaceOrders = 'place_orders';
    case SyncOrderStatus = 'sync_order_status';
    case SyncOrderShipments = 'sync_order_shipments';
    case SyncOrderInvoices = 'sync_order_invoices';
    case ReconcileOrderLines = 'reconcile_order_lines';

    public static function toOptionsWithLabels(): array
    {
        return array_combine(
            array_map(fn (self $point) => $point->value, self::cases()),
            array_map(fn (self $point) => $point->label(), self::cases())
        );
    }

    public function label(): string
    {
        return match ($this) {
            self::SyncProductCatalog => 'Sync Product Catalog',
            self::PlaceOrders => 'Place Orders',
            self::SyncOrderStatus => 'Sync Order Status',
            self::SyncOrderShipments => 'Sync Order Shipments',
            self::SyncOrderInvoices => 'Sync Order Invoices',
            self::ReconcileOrderLines => 'Reconcile Order Lines',
        };
    }
}
