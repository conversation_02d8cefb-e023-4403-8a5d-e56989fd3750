<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Providers;

use App\Modules\Promotion\Nova\Promotion;
use App\Modules\Promotion\Nova\PromotionAction;
use App\Modules\Promotion\Nova\PromotionCondition;
use App\Modules\Promotion\Nova\PromotionRule;
use Illuminate\Support\ServiceProvider;
use Laravel\Nova\Nova;

final class PromotionServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->registerNovaResources();

        $this->registerRoutes();
    }

    private function registerNovaResources(): void
    {
        Nova::resources([
            Promotion::class,
            PromotionRule::class,
            PromotionCondition::class,
            PromotionAction::class,
        ]);
    }

    private function registerRoutes(): void
    {
        $this->loadRoutesFrom(__DIR__.'/../Http/routes.php');
    }
}
