<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Engine\Conditions;

use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Models\PromotionRule;

final readonly class MinimumQuantityCondition implements Condition
{
    public function evaluate(PromotionRule $rule, Context $context, array $config): bool
    {
        $requiredQuantity = $config['quantity'] ?? null;

        if ($requiredQuantity === null) {
            return false;
        }

        if (! is_array($context->cart)) {
            return false;
        }

        // Get all product offers from the promotion
        $promotion = $rule->promotion;

        if ($promotion === null) {
            return false;
        }

        $productOffers = $promotion->productOffers;

        if ($productOffers->isEmpty()) {
            return false;
        }

        // Check if ANY product from the promotion's product offers meets the minimum quantity
        foreach ($productOffers as $productOffer) {
            $productId = $productOffer->product_id;
            $cartQty = $context->cart[$productId] ?? 0;

            if ($cartQty >= $requiredQuantity) {
                return true;
            }
        }

        return false;
    }
}
