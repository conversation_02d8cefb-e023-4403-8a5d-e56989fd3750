<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Engine\Conditions;

use App\Modules\Order\Actions\GetSpendAmountInPeriod;
use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Models\PromotionRule;
use Brick\Money\Money;

final readonly class MinimumSpendAmount implements Condition
{
    public function __construct(private GetSpendAmountInPeriod $getSpendAmountInPeriod) {}

    public function evaluate(PromotionRule $rule, Context $context, array $config): bool
    {
        $promotion = $rule->promotion;
        $productOfferIds = $promotion->productOffers->pluck('id');

        $spendInPeriod = $this->getSpendAmountInPeriod->handle(
            clinicId: $context->clinicId,
            productOfferIds: $productOfferIds->toArray(),
            startDate: $promotion->started_at,
            endDate: $promotion->ended_at,
        );

        $minimumSpendAmount = Money::ofMinor($config['minimum_spend_amount'] ?? 0, 'USD');

        return $spendInPeriod->isGreaterThanOrEqualTo($minimumSpendAmount);
    }
}
