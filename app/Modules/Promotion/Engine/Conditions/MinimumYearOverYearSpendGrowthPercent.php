<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Engine\Conditions;

use App\Modules\Order\Actions\GetSpendAmountInPeriod;
use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Models\PromotionRule;

final readonly class MinimumYearOverYearSpendGrowthPercent implements Condition
{
    public function __construct(private GetSpendAmountInPeriod $getSpendAmountInPeriod) {}

    public function evaluate(PromotionRule $rule, Context $context, array $config): bool
    {
        $promotion = $rule->promotion;
        $productOfferIds = $promotion->productOffers->pluck('id');

        $spendInCurrentPeriod = $this->getSpendAmountInPeriod->handle(
            clinicId: $context->clinicId,
            productOfferIds: $productOfferIds->toArray(),
            startDate: $promotion->started_at,
            endDate: $promotion->ended_at,
        );

        $spendInPreviousPeriod = $this->getSpendAmountInPeriod->handle(
            clinicId: $context->clinicId,
            productOfferIds: $productOfferIds->toArray(),
            startDate: $promotion->started_at->clone()->subYear(),
            endDate: $promotion->ended_at->clone()->subYear(),
        );

        if ($spendInPreviousPeriod->isZero()) {
            return $spendInCurrentPeriod->isPositive();
        }

        $growthPercent = $this->calculateGrowthPercent($spendInCurrentPeriod, $spendInPreviousPeriod);

        return $growthPercent >= $config['minimum_year_over_year_spend_growth_percent'];
    }

    private function calculateGrowthPercent($currentSpend, $previousSpend): float
    {
        return
            ($currentSpend->minus($previousSpend)->getAmount()->toInt() / $previousSpend->getAmount()->toInt())
            * 100;
    }
}
