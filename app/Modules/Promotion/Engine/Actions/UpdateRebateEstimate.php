<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Engine\Actions;

use App\Modules\Order\Actions\GetSpendAmountInPeriod;
use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Exceptions\PromotionRuleAlreadyLastException;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Models\PromotionRule;
use App\Modules\Promotion\Models\RebateEstimate;

final readonly class UpdateRebateEstimate implements Action
{
    public function __construct(private GetSpendAmountInPeriod $getSpendAmountInPeriod) {}

    public function apply(PromotionRule $rule, Context $context, array $config): void
    {
        $currentRebatePercent = $config['rebate_percent'];

        $promotion = $rule->promotion;

        $spendInPeriod = $this->getSpendAmountInPeriod->handle(
            clinicId: $context->clinicId,
            productOfferIds: $promotion->productOffers->pluck('id')->toArray(),
            startDate: $promotion->started_at,
            endDate: $promotion->ended_at
        );

        $nextRebateTier = $this->getNextRebateTier($promotion, $rule, $context);

        RebateEstimate::query()
            ->updateOrCreate([
                'clinic_id' => $context->clinicId,
                'promotion_id' => $promotion->id,
            ], [
                'current_spend_amount' => $spendInPeriod->getMinorAmount()->toInt(),
                'current_rebate_percent' => $currentRebatePercent,
                ...$nextRebateTier,
            ]);
    }

    private function getNextRebateTier(Promotion $promotion, PromotionRule $rule, Context $context): array
    {
        try {
            $nextRule = $promotion->getRuleAfter($rule);
        } catch (PromotionRuleAlreadyLastException $e) {
            return [
                'next_tier_minimum_spend_amount_threshold' => null,
                'next_tier_rebate_percent' => null,
            ];
        }

        $threshold = $nextRule->getMinimumSpendAmountThreshold($context->clinicId);

        return [
            'next_tier_minimum_spend_amount_threshold' => $threshold->getMinorAmount()->toInt(),
            'next_tier_rebate_percent' => $nextRule->getRebatePercent(),
        ];
    }
}
