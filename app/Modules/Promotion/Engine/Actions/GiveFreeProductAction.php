<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Engine\Actions;

use App\Models\ProductOffer;
use App\Modules\Promotion\Engine\Conditions\MinimumQuantityCondition;
use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Enums\ConditionType;
use App\Modules\Promotion\Models\PromotionRule;

final readonly class GiveFreeProductAction implements Action
{
    public function __construct(
        private MinimumQuantityCondition $minimumQuantityCondition
    ) {}

    public function apply(PromotionRule $rule, Context $context, array $config): void
    {
        $quantity = $config['quantity'] ?? 1;
        $freeProductOfferId = $config['free_product_offer_id'] ?? null;

        // Get trigger quantity from the condition
        $condition = $rule->conditions()->where('type', ConditionType::MinimumQuantity)->first();

        if (! $condition) {
            return;
        }

        $triggerQuantity = $condition->config['quantity'] ?? 1;

        // Validate trigger quantity is numeric and greater than zero
        if (! is_numeric($triggerQuantity) || $triggerQuantity <= 0) {
            $triggerQuantity = 1;
        }

        // Check if promotion is eligible
        $eligible = $this->minimumQuantityCondition->evaluate($rule, $context, $condition->config);

        if (! $eligible) {
            return;
        }

        // Get all product offers from the promotion
        $promotion = $rule->promotion;
        $productOffers = $promotion->productOffers;

        if ($productOffers->isEmpty() || ! is_array($context->cart)) {
            return;
        }

        // Collect all target product offer IDs to avoid N+1 queries
        $targetProductOfferIds = [];
        foreach ($productOffers as $productOffer) {
            $productId = $productOffer->product_id;
            $cartQty = $context->cart[$productId] ?? 0;

            if ($cartQty >= $triggerQuantity) {
                $targetProductOfferId = $freeProductOfferId ?: $productOffer->id;
                $targetProductOfferIds[] = $targetProductOfferId;
            }
        }

        // Eager load all target product offers in a single query
        $targetProductOffers = ProductOffer::whereIn('id', array_unique($targetProductOfferIds))->get()->keyBy('id');

        // Find which products are actually in the cart and meet the minimum quantity
        foreach ($productOffers as $productOffer) {
            $productId = $productOffer->product_id;
            $cartQty = $context->cart[$productId] ?? 0;

            if ($cartQty >= $triggerQuantity) {
                // Calculate how many "sets" of the promotion the customer qualifies for this product
                // Use triggerQuantity for the calculation (Buy X Get Y where X = triggerQuantity)
                $qualifyingSets = (int) ($cartQty / $triggerQuantity);
                $totalGetQuantity = $qualifyingSets * $quantity; // Get quantity * number of qualifying sets

                if ($totalGetQuantity > 0) {
                    // Determine which product to give
                    $targetProductOfferId = $freeProductOfferId ?: $productOffer->id;
                    $targetProductOffer = $targetProductOffers->get($targetProductOfferId);

                    if ($targetProductOffer) {
                        // Add free products to context
                        $context->freeProducts[] = [
                            'product_offer_id' => $targetProductOfferId,
                            'product_id' => $targetProductOffer->product_id,
                            'quantity' => $totalGetQuantity,
                            'reason' => "Buy {$triggerQuantity} Get {$quantity} promotion",
                            'promotion_id' => $rule->promotion->id,
                            'rule_id' => $rule->id,
                            'qualifying_product_id' => $productId, // Track which product qualified for this free product
                        ];
                    }
                }
            }
        }
    }
}
