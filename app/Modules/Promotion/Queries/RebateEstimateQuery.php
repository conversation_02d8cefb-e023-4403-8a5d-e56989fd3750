<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Queries;

use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\QueryBuilder;

final class RebateEstimateQuery extends QueryBuilder
{
    public function __construct(Builder $query)
    {
        parent::__construct($query);

        $this->allowedSorts([
            'current_spend_amount',
        ]);

        $this->defaultSort('-current_spend_amount');
    }
}
