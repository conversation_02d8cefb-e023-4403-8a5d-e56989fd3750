<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Queries;

use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedSort;
use Spatie\QueryBuilder\QueryBuilder;

final class PromotionQuery extends QueryBuilder
{
    public function __construct(Builder $query)
    {
        parent::__construct($query);

        $this->allowedFilters([
            AllowedFilter::callback('product_offer_id', function (Builder $query, string $value) {
                $query->whereHas('productOffers', fn ($query) => $query->where('product_offer_id', $value));
            }),
            AllowedFilter::exact('type'),
        ]);

        $this->allowedSorts([
            AllowedSort::field('started_at'),
            AllowedSort::field('ended_at'),
        ]);

        $this->defaultSort(
            AllowedSort::field('-ended_at'),
        );
    }
}
