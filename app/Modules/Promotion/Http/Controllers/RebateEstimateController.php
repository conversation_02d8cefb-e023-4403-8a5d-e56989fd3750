<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Clinic;
use App\Modules\Promotion\Actions\GetRebateEstimate;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class RebateEstimateController extends Controller
{
    public function __construct(
        private readonly GetRebateEstimate $getRebateEstimate
    ) {}

    public function index(Request $request): JsonResponse
    {
        $clinicId = $request->header('highfive-clinic');

        $clinic = Clinic::query()
            ->with(['account', 'account.gpo'])
            ->findOrFail($request->clinicId());

        $gpoId = $clinic->account->gpo_account_id;

        if ($gpoId === null) {
            return new JsonResponse([]);
        }

        $estimates = $this->getRebateEstimate->handle($clinicId, $gpoId);

        return new JsonResponse($estimates);
    }
}
