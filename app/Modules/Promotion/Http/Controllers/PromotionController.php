<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Http\Controllers;

use App\Models\Clinic;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Data\PromotionData;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Queries\PromotionQuery;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

final class PromotionController
{
    public function index(Request $request): JsonResponse
    {
        $clinic = Clinic::query()
            ->with(['account', 'account.gpo'])
            ->findOrFail($request->clinicId());

        $gpoId = $clinic->account->gpo_account_id;

        if ($gpoId === null) {
            return new JsonResponse([]);
        }

        $today = Carbon::today();

        $query = Promotion::query()
            ->with(['vendor', 'productOffers.clinics', 'productOffers.product', 'rules.conditions', 'rules.actions'])
            ->where('promotionable_type', GpoAccount::class)
            ->where('promotionable_id', $gpoId)
            ->where('started_at', '<=', $today->startOfDay())
            ->where('ended_at', '>=', $today->endOfDay())
            ->where('status', PromotionStatus::Active);

        // Filter by type if provided
        if ($request->has('type')) {
            $query->where('type', $request->get('type'));
        }

        $promotions = PromotionQuery::for($query)->get();

        $data = PromotionData::collect($promotions);

        return new JsonResponse($data);
    }

    public function show(Request $request, string $id): JsonResponse
    {
        $clinic = Clinic::query()
            ->with(['account', 'account.gpo'])
            ->findOrFail($request->clinicId());

        $gpoId = $clinic->account->gpo_account_id;

        if ($gpoId === null) {
            return new JsonResponse([]);
        }

        $promotion = Promotion::query()
            ->with(['vendor', 'productOffers.clinics', 'productOffers.product', 'rules.conditions', 'rules.actions'])
            ->where('promotionable_type', GpoAccount::class)
            ->where('promotionable_id', $gpoId)
            ->where('id', $id)
            ->firstOrFail();

        $data = PromotionData::from($promotion);

        return new JsonResponse($data);
    }
}
