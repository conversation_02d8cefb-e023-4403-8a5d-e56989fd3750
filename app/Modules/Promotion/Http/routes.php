<?php

declare(strict_types=1);

use App\Modules\Promotion\Http\Controllers\PromotionController;
use App\Modules\Promotion\Http\Controllers\RebateEstimateController;
use Illuminate\Support\Facades\Route;

Route::middleware(['api', 'auth:sanctum', 'header:highfive-clinic'])->prefix('api')->group(function () {
    Route::get('promotions', [PromotionController::class, 'index']);
    Route::get('promotions/{id}', [PromotionController::class, 'show']);
    Route::get('rebate-estimates', [RebateEstimateController::class, 'index']);
});
