<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Data;

use App\Data\Casts\MoneyCast;
use App\Data\Transformers\MoneyTransformer;
use Brick\Money\Money;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;

final class RebateEstimateData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly PromotionData $promotion,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly Money $currentSpendAmount,
        public readonly float $currentRebatePercent,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly ?Money $nextTierMinimumSpendAmountThreshold,
        public readonly ?float $nextTierRebatePercent,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly Money $estimatedRebateAmount,
        #[DataCollectionOf(SuggestedProductOfferData::class)]
        public readonly Collection $suggestedProductOffers,
    ) {}

    public function with(): array
    {
        return [
            'isLastTier' => $this->nextTierMinimumSpendAmountThreshold === null && $this->nextTierRebatePercent === null,
        ];
    }
}
