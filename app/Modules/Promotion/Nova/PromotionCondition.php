<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Nova;

use App\Modules\Promotion\Enums\ConditionType;
use App\Nova\Resource;
use <PERSON>vel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class PromotionCondition extends Resource
{
    public static $model = \App\Modules\Promotion\Models\PromotionCondition::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Rule', 'rule', PromotionRule::class)
                ->searchable()
                ->sortable(),

            Select::make('Type')
                ->options(ConditionType::class)
                ->displayUsingLabels()
                ->rules('required')
                ->sortable(),

            // Dynamic fields based on type selection
            Number::make('Minimum Spend Amount (cents)', 'config->minimum_spend_amount')
                ->rules('sometimes', 'required', 'integer', 'min:0')
                ->help('Amount in cents (e.g., 10000 for $100.00)')
                ->hideFromIndex()
                ->showOnDetail(function () {
                    return $this->type === ConditionType::MinimumSpendAmount;
                })
                ->displayUsing(function ($value) {
                    return $value ? '$'.number_format($value / 100, 2) : null;
                })
                ->dependsOn('type', function (Number $field, NovaRequest $_, $formData) {
                    if ($formData['type'] === ConditionType::MinimumSpendAmount->value) {
                        $field->show()->rules('required', 'integer', 'min:0');
                    } else {
                        $field->hide()->rules('nullable');
                    }
                }),

            Number::make('Minimum Growth Percent', 'config->minimum_year_over_year_spend_growth_percent')
                ->rules('sometimes', 'required', 'numeric', 'min:0')
                ->step(0.01)
                ->help('Percentage growth required (e.g., 25 for 25%)')
                ->hideFromIndex()
                ->showOnDetail(function () {
                    return $this->type === ConditionType::MinimumYearOverYearSpendGrowthPercent;
                })
                ->displayUsing(function ($value) {
                    return $value ? "{$value}%" : null;
                })
                ->dependsOn('type', function (Number $field, NovaRequest $_, $formData) {
                    if ($formData['type'] === ConditionType::MinimumYearOverYearSpendGrowthPercent->value) {
                        $field->show()->rules('required', 'numeric', 'min:0');
                    } else {
                        $field->hide()->rules('nullable');
                    }
                }),

            Number::make('Minimum Quantity', 'config->quantity')
                ->rules('sometimes', 'required', 'integer', 'min:1')
                ->help('Minimum quantity required to trigger the promotion')
                ->hideFromIndex()
                ->showOnDetail(function () {
                    return $this->type === ConditionType::MinimumQuantity;
                })
                ->dependsOn('type', function (Number $field, NovaRequest $_, $formData) {
                    if ($formData['type'] === ConditionType::MinimumQuantity->value) {
                        $field->show()->rules('required', 'integer', 'min:1');
                    } else {
                        $field->hide()->rules('nullable');
                    }
                }),

            // JSON config field for debugging and unknown types
            Code::make('Config')
                ->json()
                ->rules('sometimes', 'required', 'json')
                ->hideFromIndex()
                ->dependsOn('type', function (Code $field, NovaRequest $_, $formData) {
                    $knownTypes = [
                        ConditionType::MinimumSpendAmount->value,
                        ConditionType::MinimumYearOverYearSpendGrowthPercent->value,
                        ConditionType::MinimumQuantity->value,
                    ];

                    if (in_array($formData['type'], $knownTypes)) {
                        $field->hide()->rules('nullable');
                    } else {
                        $field->show()->rules('required', 'json');
                    }
                }),
        ];
    }
}
