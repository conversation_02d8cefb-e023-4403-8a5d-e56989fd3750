<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Nova;

use App\Modules\Gpo\Nova\GpoAccount;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Nova\Vendor;
use <PERSON>vel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Markdown;
use Laravel\Nova\Fields\MorphTo;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Resource;

final class Promotion extends Resource
{
    public static $model = \App\Modules\Promotion\Models\Promotion::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->withCount('rules');
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            MorphTo::make('Promotionable')
                ->types([
                    GpoAccount::class,
                ])
                ->nullable()
                ->searchable()
                ->readonly(function (NovaRequest $request) {
                    return $request->isUpdateOrUpdateAttachedRequest();
                }),

            BelongsTo::make('Vendor', 'vendor', Vendor::class)
                ->readonly(function (NovaRequest $request) {
                    return $request->isUpdateOrUpdateAttachedRequest();
                }),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            Markdown::make('Description')
                ->hideFromIndex()
                ->rules('nullable'),

            Number::make('Priority')
                ->rules('required', 'integer', 'min:1')
                ->sortable(),

            Select::make('Type')
                ->options(PromotionType::class)
                ->displayUsingLabels()
                ->rules('required')
                ->sortable()
                ->readonly(function (NovaRequest $request) {
                    return $request->isUpdateOrUpdateAttachedRequest();
                }),

            Select::make('Status')
                ->options(PromotionStatus::class)
                ->displayUsingLabels()
                ->rules('required')
                ->sortable(),

            DateTime::make('Started At')
                ->rules('required', 'date')
                ->sortable(),

            DateTime::make('Ended At')
                ->rules('nullable', 'date', 'after:started_at')
                ->sortable(),

            Text::make('Rules Count', 'rules_count')
                ->onlyOnIndex(),

            BelongsToMany::make('Product Offers')
                ->searchable()
                ->withSubtitles(),

            HasMany::make('Rules', 'rules', PromotionRule::class),
        ];
    }
}
