<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Nova;

use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Resource;

final class PromotionRule extends Resource
{
    public static $model = \App\Modules\Promotion\Models\PromotionRule::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->withCount('conditions', 'actions');
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Promotion', 'promotion', Promotion::class)
                ->searchable()
                ->sortable(),

            Number::make('Priority')
                ->rules('required', 'integer', 'min:1')
                ->sortable(),

            Text::make('Conditions Count', 'conditions_count')
                ->onlyOnIndex(),

            Text::make('Actions Count', 'actions_count')
                ->onlyOnIndex(),

            HasMany::make('Conditions', 'conditions', PromotionCondition::class),

            HasMany::make('Actions', 'actions', PromotionAction::class),
        ];
    }
}
