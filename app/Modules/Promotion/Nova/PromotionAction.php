<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Nova;

use App\Models\ProductOffer;
use App\Modules\Promotion\Enums\ActionType;
use App\Nova\Resource;
use Laravel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

final class PromotionAction extends Resource
{
    public static $model = \App\Modules\Promotion\Models\PromotionAction::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Rule', 'rule', PromotionRule::class)
                ->searchable()
                ->sortable(),

            Select::make('Type')
                ->options(ActionType::class)
                ->displayUsingLabels()
                ->rules('required')
                ->sortable(),

            // Dynamic fields based on type selection
            Number::make('Rebate Percent', 'config->rebate_percent')
                ->rules('sometimes', 'required', 'numeric', 'min:0', 'max:100')
                ->step(0.01)
                ->help('Percentage rebate (e.g., 5 for 5%)')
                ->hideFromIndex()
                ->showOnDetail(function () {
                    return $this->type === ActionType::UpdateRebateEstimate;
                })
                ->displayUsing(function ($value) {
                    return $value ? "{$value}%" : null;
                })
                ->dependsOn('type', function (Number $field, NovaRequest $_, $formData) {
                    if ($formData['type'] === ActionType::UpdateRebateEstimate->value) {
                        $field->show()->rules('required', 'numeric', 'min:0', 'max:100');
                    } else {
                        $field->hide()->rules('nullable');
                    }
                }),

            Number::make('Quantity', 'config->quantity')
                ->rules('sometimes', 'required', 'integer', 'min:1')
                ->help('Number of free products to give')
                ->hideFromIndex()
                ->showOnDetail(function () {
                    return $this->type === ActionType::GiveFreeProduct;
                })
                ->dependsOn('type', function (Number $field, NovaRequest $_, $formData) {
                    if ($formData['type'] === ActionType::GiveFreeProduct->value) {
                        $field->show()->rules('required', 'integer', 'min:1');
                    } else {
                        $field->hide()->rules('nullable');
                    }
                }),

            Text::make('Free Product Offer ID', 'config->free_product_offer_id')
                ->nullable()
                ->help('Enter the Product Offer ID to give for free. You can find Product Offer IDs in the Product Offers section. Leave empty to give the same product.')
                ->hideFromIndex()
                ->showOnDetail(function () {
                    return $this->type === ActionType::GiveFreeProduct;
                })
                ->displayUsing(function ($value) {
                    if (! $value) {
                        return 'Same product (no free product specified)';
                    }

                    $productOffer = ProductOffer::find($value);
                    if ($productOffer) {
                        return "{$productOffer->name} (ID: {$value})";
                    }

                    return "Product not found (ID: {$value})";
                })
                ->dependsOn('type', function (Text $field, NovaRequest $_, $formData) {
                    if ($formData['type'] === ActionType::GiveFreeProduct->value) {
                        $field->show()->rules('nullable');
                    } else {
                        $field->hide()->rules('nullable');
                    }
                }),

            Text::make('Message', 'config->message')
                ->nullable()
                ->rules('sometimes', 'nullable', 'string', 'max:255')
                ->help('Optional message to display with the promotion')
                ->hideFromIndex()
                ->showOnDetail(function () {
                    return $this->type === ActionType::GiveFreeProduct;
                })
                ->displayUsing(function ($value) {
                    return $value ?: 'No message';
                })
                ->dependsOn('type', function (Text $field, NovaRequest $_, $formData) {
                    if ($formData['type'] === ActionType::GiveFreeProduct->value) {
                        $field->show()->rules('nullable', 'string', 'max:255');
                    } else {
                        $field->hide()->rules('nullable');
                    }
                }),

            // JSON config field for debugging and unknown types
            Code::make('Config')
                ->json()
                ->rules('sometimes', 'required', 'json')
                ->hideFromIndex()
                ->dependsOn('type', function (Code $field, NovaRequest $_, $formData) {
                    $knownTypes = [
                        ActionType::UpdateRebateEstimate->value,
                        ActionType::GiveFreeProduct->value,
                    ];

                    if (in_array($formData['type'], $knownTypes)) {
                        $field->hide()->rules('nullable');
                    } else {
                        $field->show()->rules('required', 'json');
                    }
                }),
        ];
    }
}
