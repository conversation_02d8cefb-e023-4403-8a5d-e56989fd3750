<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Listeners;

use App\Modules\Order\Events\OrderCreated;
use App\Modules\Promotion\Actions\ProcessOrderPromotions as ProcessOrderPromotionsAction;
use Illuminate\Contracts\Queue\ShouldQueue;

final class ProcessOrderPromotions implements ShouldQueue
{
    public function __construct(
        private ProcessOrderPromotionsAction $processOrderPromotions,
    ) {}

    public function handle(OrderCreated $event): void
    {
        $this->processOrderPromotions->handle($event->order->id, $event->order->clinic_id);
    }
}
