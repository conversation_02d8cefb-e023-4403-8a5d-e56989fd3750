<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Enums;

enum ConditionType: string
{
    case MinimumSpendAmount = 'minimum_spend_amount';
    case MinimumYearOverYearSpendGrowthPercent = 'minimum_year_over_year_spend_growth_percent';
    case MinimumQuantity = 'minimum_quantity';

    public function label(): string
    {
        return match ($this) {
            self::MinimumSpendAmount => 'Minimum Spend Amount',
            self::MinimumYearOverYearSpendGrowthPercent => 'Minimum Year Over Year Spend Growth Percent',
            self::MinimumQuantity => 'Minimum Quantity',
        };
    }
}
