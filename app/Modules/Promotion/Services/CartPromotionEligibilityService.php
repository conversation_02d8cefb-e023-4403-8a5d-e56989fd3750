<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Services;

use App\Models\Cart;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Data\PromotionData;
use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Models\PromotionCondition;
use App\Modules\Promotion\Models\PromotionRule;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

final class CartPromotionEligibilityService
{
    public function checkCartEligibility(Cart $cart): array
    {
        $clinic = $cart->clinic;
        $cartItems = $cart->items;

        if ($cartItems->isEmpty()) {
            return [
                'eligiblePromotions' => [],
                'itemsWithPromotions' => [],
            ];
        }

        $productOfferIds = $cartItems->pluck('product_offer_id')->filter()->unique()->toArray();
        if (empty($productOfferIds)) {
            return [
                'eligiblePromotions' => [],
                'itemsWithPromotions' => [],
            ];
        }
        $activePromotions = $this->fetchActivePromotions($clinic->id, $productOfferIds);

        if ($activePromotions->isEmpty()) {
            return [
                'eligiblePromotions' => [],
                'itemsWithPromotions' => [],
            ];
        }

        // Build cart data for context: ['product_id' => quantity, ...]
        $cartData = [];
        foreach ($cartItems as $cartItem) {
            if ($cartItem->productOffer?->product) {
                $productId = $cartItem->productOffer->product_id;
                $cartData[$productId] = ($cartData[$productId] ?? 0) + $cartItem->quantity;
            }
        }

        $context = new Context($clinic->id, $cartData);
        $eligiblePromotions = [];
        $itemPromotions = [];

        foreach ($activePromotions as $promotion) {
            $eligibilityResult = $this->checkPromotionEligibility($promotion, $cartItems, $context);

            if ($eligibilityResult['is_eligible']) {
                $eligiblePromotions[] = [
                    'promotion' => PromotionData::from($promotion),
                    'applicableItems' => $eligibilityResult['applicable_items'],
                    'missingRequirements' => $eligibilityResult['missing_requirements'],
                ];

                foreach ($eligibilityResult['applicable_items'] as $itemId) {
                    if (! isset($itemPromotions[$itemId])) {
                        $itemPromotions[$itemId] = [];
                    }
                    $itemPromotions[$itemId][] = [
                        'promotionId' => $promotion->id,
                        'promotionName' => $promotion->name,
                        'promotionType' => $promotion->type,
                        'isEligible' => true,
                    ];
                }
            }
        }

        return [
            'eligiblePromotions' => $eligiblePromotions,
            'itemsWithPromotions' => $itemPromotions,
        ];
    }

    private function fetchActivePromotions(string $clinicId, array $productOfferIds): Collection
    {
        $today = Carbon::today();

        $clinic = \App\Models\Clinic::query()->with(['account', 'account.gpo'])->findOrFail($clinicId);
        $gpoId = $clinic->account->gpo_account_id;

        if ($gpoId === null) {
            return new Collection();
        }

        return Promotion::query()
            ->where('promotionable_type', GpoAccount::class)
            ->where('promotionable_id', $gpoId)
            ->whereHas('productOffers', fn (Builder $query) => $query->whereIn('id', $productOfferIds))
            ->where('status', PromotionStatus::Active)
            ->where('started_at', '<=', $today)
            ->where(function ($query) use ($today) {
                $query->whereNull('ended_at')
                    ->orWhere('ended_at', '>=', $today);
            })
            ->with([
                'rules' => fn (HasMany $query) => $query->orderBy('priority'),
                'rules.conditions',
                'rules.actions',
                'productOffers',
                'vendor',
            ])
            ->get();
    }

    private function checkPromotionEligibility(Promotion $promotion, Collection $cartItems, Context $context): array
    {
        $applicableItems = [];
        $missingRequirements = [];
        $isEligible = false;

        $promotionProductOfferIds = $promotion->productOffers->pluck('id')->toArray();
        $relevantCartItems = $cartItems->whereIn('product_offer_id', $promotionProductOfferIds);

        if ($relevantCartItems->isEmpty()) {
            return [
                'is_eligible' => false,
                'applicable_items' => [],
                'missing_requirements' => ['No qualifying products in cart'],
            ];
        }

        $matchingRule = $promotion->rules->first(function (PromotionRule $rule) use ($context, $cartItems, &$missingRequirements) {
            if ($rule->conditions->isEmpty()) {
                return true;
            }

            $allConditionsMet = true;
            foreach ($rule->conditions as $condition) {
                $conditionResult = $this->evaluateConditionForCart($condition, $cartItems, $context);
                if (! $conditionResult['is_met']) {
                    $allConditionsMet = false;
                    $missingRequirements[] = $conditionResult['requirement'];
                }
            }

            return $allConditionsMet;
        });

        if ($matchingRule) {
            $isEligible = true;
            $applicableItems = $relevantCartItems->pluck('id')->toArray();
        }

        return [
            'is_eligible' => $isEligible,
            'applicable_items' => $applicableItems,
            'missing_requirements' => $missingRequirements,
        ];
    }

    private function evaluateConditionForCart(PromotionCondition $condition, Collection $cartItems, Context $context): array
    {
        try {
            $isMet = $condition->evaluate($context);

            return [
                'is_met' => $isMet,
                'requirement' => $this->getConditionDescription($condition),
            ];
        } catch (Exception $e) {
            return [
                'is_met' => false,
                'requirement' => 'Unable to evaluate condition: '.$e->getMessage(),
            ];
        }
    }

    private function getConditionDescription(PromotionCondition $condition): string
    {
        $config = $condition->config ?? [];

        return match ($condition->type->value) {
            'minimum_spend_amount' => sprintf('Minimum spend of $%.2f required', ($config['amount'] ?? 0) / 100),
            'minimum_quantity' => sprintf('Minimum quantity of %d required', $config['quantity'] ?? 0),
            'minimum_year_over_year_spend_growth_percent' => sprintf('Minimum %.1f%% year-over-year growth required', $config['percent'] ?? 0),
            default => 'Unknown condition requirement',
        };
    }
}
