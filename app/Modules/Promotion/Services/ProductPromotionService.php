<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Services;

use App\Models\Product;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Data\PromotionData;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Models\Promotion;
use Carbon\Carbon;
use Illuminate\Support\Collection;

final class ProductPromotionService
{
    /**
     * Get active promotions for a specific product
     *
     * @return Collection<int, PromotionData>
     */
    public function getActivePromotionsForProduct(Product $product, string|int|null $clinicId = null): Collection
    {
        $query = Promotion::query()
            ->where('status', PromotionStatus::Active)
            ->where('started_at', '<=', Carbon::now())
            ->where(function ($query) {
                $query->whereNull('ended_at')
                    ->orWhere('ended_at', '>=', Carbon::now());
            })
            ->with([
                'vendor',
                'productOffers.product',
                'rules.conditions',
                'rules.actions',
            ]);

        // Filter by clinic's GPO account if clinic ID is provided
        if ($clinicId) {
            $query->whereHasMorph('promotionable', [GpoAccount::class], function ($query) use ($clinicId) {
                $query->whereHas('members.clinics', function ($clinicQuery) use ($clinicId) {
                    $clinicQuery->where('clinics.id', $clinicId);
                });
            });
        }

        // Filter promotions that apply to this product
        $query->whereHas('productOffers', function ($query) use ($product) {
            $query->where('product_id', $product->id);
        });

        $promotions = $query->get();

        return $promotions->map(function (Promotion $promotion) {
            return PromotionData::from($promotion);
        });
    }
}
