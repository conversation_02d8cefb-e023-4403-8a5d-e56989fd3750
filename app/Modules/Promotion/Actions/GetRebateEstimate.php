<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Actions;

use App\Actions\Products\GetRebateEstimateByProductOffer;
use App\Models\Clinic;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Data\PromotionData;
use App\Modules\Promotion\Data\RebateEstimateData;
use App\Modules\Promotion\Data\SuggestedProductOfferData;
use App\Modules\Promotion\Data\VendorData;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\RebateEstimate;
use App\Modules\Promotion\Queries\RebateEstimateQuery;
use Brick\Money\Money;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;

final readonly class GetRebateEstimate
{
    public function __construct(
        private GetRebateEstimateByProductOffer $getRebateEstimateByProductOffer
    ) {}

    public function handle(string $clinicId, string $gpoId)
    {
        $cacheKey = "rebate-estimate:{$clinicId}:{$gpoId}";

        return Cache::flexible($cacheKey, [300, 600], function () use ($clinicId, $gpoId) {
            $today = Carbon::today();
            $clinic = Clinic::findOrFail($clinicId);
            $twelveMonthsAgo = now()->subMonths(12);

            $query = RebateEstimate::query()
                ->with(['promotion', 'promotion.vendor'])
                ->where('clinic_id', $clinicId)
                ->whereHas('promotion', function (Builder $query) use ($gpoId, $today) {
                    $query->where('promotionable_type', GpoAccount::class)
                        ->where('promotionable_id', $gpoId)
                        ->where('type', PromotionType::Rebate)
                        ->where('status', PromotionStatus::Active)
                        ->where('started_at', '<=', $today)
                        ->where('ended_at', '>=', $today);
                });

            $estimates = RebateEstimateQuery::for($query)->get();

            // Bulk load all rebate estimates for the product offers to avoid N+1
            $allOfferIds = $estimates->flatMap(function ($estimate) use ($clinicId, $clinic, $twelveMonthsAgo) {
                return $estimate->promotion->productOffers()
                    ->select('id')
                    ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id))
                    ->whereExists(function ($query) use ($clinicId, $twelveMonthsAgo) {
                        $query->select('id')
                            ->from('order_items')
                            ->whereColumn('order_items.product_offer_id', 'product_offers.id')
                            ->whereExists(function ($query) use ($clinicId, $twelveMonthsAgo) {
                                $query->select('id')
                                    ->from('orders')
                                    ->whereColumn('orders.id', 'order_items.order_id')
                                    ->where('orders.clinic_id', $clinicId)
                                    ->where('orders.created_at', '>=', $twelveMonthsAgo)
                                    ->where('orders.created_at', '<=', now());
                            });
                    })
                    ->pluck('id');
            });

            // Bulk load rebate estimates for all offers at once
            $rebateEstimatesByOfferId = collect();
            if ($allOfferIds->isNotEmpty()) {
                $rebateEstimatesByOfferId = RebateEstimate::query()
                    ->where('clinic_id', $clinicId)
                    ->whereHas('promotion', function (Builder $query) use ($gpoId, $today, $allOfferIds) {
                        $query->whereHas('productOffers', function (Builder $query) use ($allOfferIds) {
                            $query->whereIn('product_offer_id', $allOfferIds->toArray());
                        })
                            ->where('promotionable_type', GpoAccount::class)
                            ->where('promotionable_id', $gpoId)
                            ->where('type', PromotionType::Rebate)
                            ->where('status', PromotionStatus::Active)
                            ->where('started_at', '<=', $today)
                            ->where('ended_at', '>=', $today);
                    })
                    ->with(['promotion.productOffers'])
                    ->get()
                    ->flatMap(function ($estimate) {
                        return $estimate->promotion->productOffers->mapWithKeys(function ($offer) use ($estimate) {
                            return [$offer->id => $estimate];
                        });
                    });
            }

            return $estimates->map(function (RebateEstimate $estimate) use ($clinicId, $clinic, $twelveMonthsAgo, $rebateEstimatesByOfferId) {
                // Optimize the subquery by using a single query with proper joins
                $suggestedOffers = $estimate->promotion->productOffers()
                    ->select([
                        'product_offers.*',
                        'order_totals.total_price as order_items_sum_total_price',
                    ])
                    ->with(['vendor', 'product', 'clinics'])
                    ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id))
                    ->leftJoinSub(
                        function ($query) use ($clinicId, $twelveMonthsAgo) {
                            $query->select([
                                'order_items.product_offer_id',
                                DB::raw('SUM(order_items.total_price) as total_price'),
                            ])
                                ->from('order_items')
                                ->join('orders', 'orders.id', '=', 'order_items.order_id')
                                ->where('orders.clinic_id', $clinicId)
                                ->where('orders.created_at', '>=', $twelveMonthsAgo)
                                ->where('orders.created_at', '<=', now())
                                ->groupBy('order_items.product_offer_id');
                        },
                        'order_totals',
                        'product_offers.id',
                        '=',
                        'order_totals.product_offer_id'
                    )
                    ->whereNotNull('order_totals.total_price')
                    ->orderByDesc('order_totals.total_price')
                    ->limit(3)
                    ->get();

                return RebateEstimateData::from([
                    'id' => $estimate->id,
                    'promotion' => PromotionData::from($estimate->promotion),
                    'currentSpendAmount' => Money::ofMinor($estimate->current_spend_amount, 'USD'),
                    'currentRebatePercent' => $estimate->current_rebate_percent,
                    'nextTierMinimumSpendAmountThreshold' => $estimate->next_tier_minimum_spend_amount_threshold
                        ? Money::ofMinor($estimate->next_tier_minimum_spend_amount_threshold, 'USD')
                        : null,
                    'nextTierRebatePercent' => $estimate->next_tier_rebate_percent,
                    'estimatedRebateAmount' => Money::ofMinor($estimate->estimated_rebate_amount, 'USD'),
                    'suggestedProductOffers' => $suggestedOffers->map(function ($offer) use ($clinicId, $rebateEstimatesByOfferId) {
                        $clinicPrice = null;
                        $clinic = $offer->clinics->where('id', $clinicId)->first();
                        if ($clinic && $clinic->pivot && $clinic->pivot->price) {
                            $clinicPrice = Money::ofMinor($clinic->pivot->price, 'USD');
                        }

                        // Use pre-loaded rebate estimate instead of N+1 query
                        $rebateEstimate = $rebateEstimatesByOfferId->get($offer->id);

                        return SuggestedProductOfferData::from([
                            'id' => $offer->id,
                            'name' => $offer->name,
                            'imageUrl' => $offer->image_url,
                            'vendorSku' => $offer->vendor_sku,
                            'price' => $offer->price ? Money::ofMinor($offer->price, 'USD') : null,
                            'clinicPrice' => $clinicPrice,
                            'stockStatus' => $offer->stock_status,
                            'vendor' => VendorData::from($offer->vendor),
                            'orderItemsSumTotalPrice' => $offer->order_items_sum_total_price,
                            'increments' => $offer->increments,
                            'product' => $offer->product,
                            'isRecommended' => $offer->is_recommended ?? false,
                            'rebatePercent' => $rebateEstimate?->current_rebate_percent,
                        ]);
                    }),
                ]);
            });
        });
    }
}
