<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Actions;

use App\Models\Clinic;
use App\Models\Order;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Order\Actions\FetchProductOfferIds;
use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Models\PromotionAction;
use App\Modules\Promotion\Models\PromotionCondition;
use App\Modules\Promotion\Models\PromotionRule;
use App\Modules\Promotion\Services\OrderPromotionTracker;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

final readonly class ProcessOrderPromotions
{
    public function __construct(
        private FetchProductOfferIds $fetchProductOfferIds,
        private OrderPromotionTracker $promotionTracker,
    ) {}

    public function handle(string $orderId, string $clinicId): void
    {
        try {
            $this->validateInputs($orderId, $clinicId);

            $order = $this->loadOrderWithRelations($orderId);
            $productOfferIds = $this->fetchProductOfferIds->handle($orderId);

            if ($productOfferIds->isEmpty()) {
                Log::info('No product offers found for order', ['orderId' => $orderId]);

                return;
            }

            $promotions = $this->fetchActivePromotions($clinicId, $productOfferIds->toArray());

            Log::info('Found promotions for order', [
                'orderId' => $orderId,
                'clinicId' => $clinicId,
                'promotionCount' => $promotions->count(),
                'productOfferCount' => $productOfferIds->count(),
            ]);

            if ($promotions->isEmpty()) {
                Log::info('No active promotions found for order', ['orderId' => $orderId]);

                return;
            }

            $orderData = $this->buildOrderData($order);
            $context = new Context($clinicId, $orderData);
            $appliedPromotions = collect();

            $this->processPromotions($promotions, $context, $appliedPromotions);

            Log::info('Processed promotions for order', [
                'orderId' => $orderId,
                'appliedPromotionsCount' => $appliedPromotions->count(),
            ]);

            if ($appliedPromotions->isNotEmpty()) {
                $this->promotionTracker->trackAppliedPromotions($order, $appliedPromotions, $context);
                Log::info('Successfully tracked applied promotions', [
                    'orderId' => $orderId,
                    'trackedCount' => $appliedPromotions->count(),
                ]);
            }
        } catch (Exception $e) {
            Log::error('Failed to process order promotions', [
                'orderId' => $orderId,
                'clinicId' => $clinicId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    /**
     * Validate input parameters
     */
    private function validateInputs(string $orderId, string $clinicId): void
    {
        if (empty($orderId)) {
            throw new InvalidArgumentException('Order ID cannot be empty');
        }

        if (empty($clinicId)) {
            throw new InvalidArgumentException('Clinic ID cannot be empty');
        }
    }

    /**
     * Load order with necessary relationships
     */
    private function loadOrderWithRelations(string $orderId): Order
    {
        $order = Order::with([
            'items.productOffer.product',
            'clinic.account.gpo',
        ])->findOrFail($orderId);

        if (! $order->clinic) {
            throw new InvalidArgumentException("Order {$orderId} has no associated clinic");
        }

        return $order;
    }

    /**
     * Build order data in the format expected by the Context
     */
    private function buildOrderData(Order $order): array
    {
        $orderData = [];

        foreach ($order->items as $orderItem) {
            if (! $orderItem->productOffer || ! $orderItem->productOffer->product) {
                Log::warning('Order item missing product offer or product', [
                    'orderItemId' => $orderItem->id,
                    'productOfferId' => $orderItem->product_offer_id,
                ]);

                continue;
            }

            $productId = $orderItem->productOffer->product_id;
            $orderData[$productId] = ($orderData[$productId] ?? 0) + $orderItem->quantity;
        }

        Log::debug('Built order data', [
            'orderId' => $order->id,
            'orderData' => $orderData,
        ]);

        return $orderData;
    }

    /**
     * Fetch active promotions for the given clinic and product offers
     *
     * @return \Illuminate\Database\Eloquent\Collection<int, Promotion>
     */
    private function fetchActivePromotions(string $clinicId, array $productOfferIds): Collection
    {
        $today = Carbon::today();

        $clinic = Clinic::query()
            ->with(['account.gpo'])
            ->findOrFail($clinicId);

        $gpoId = $clinic->account->gpo_account_id;

        if ($gpoId === null) {
            Log::info('Clinic has no GPO account', ['clinicId' => $clinicId]);

            return new Collection();
        }

        return Promotion::query()
            ->where('promotionable_type', GpoAccount::class)
            ->where('promotionable_id', $gpoId)
            ->whereHas('productOffers', fn (Builder $query) => $query->whereIn('id', $productOfferIds))
            ->where('status', PromotionStatus::Active)
            ->where('started_at', '<=', $today)
            ->where(function ($query) use ($today) {
                $query->whereNull('ended_at')
                    ->orWhere('ended_at', '>=', $today);
            })
            ->with([
                'rules' => fn (HasMany $query) => $query->orderBy('priority'),
                'rules.conditions',
                'rules.actions',
            ])
            ->get();
    }

    /**
     * Process promotions and apply matching rules
     */
    private function processPromotions(Collection $promotions, Context $context, Collection $appliedPromotions): void
    {
        $promotions->each(function (Promotion $promotion) use ($context, $appliedPromotions) {
            try {
                $appliedRule = $this->applyFirstMatchingRuleActions($promotion->rules, $context);

                if ($appliedRule) {
                    $appliedPromotions->push([
                        'promotion' => $promotion,
                        'rule' => $appliedRule,
                    ]);

                    Log::debug('Applied promotion rule', [
                        'promotionId' => $promotion->id,
                        'promotionName' => $promotion->name,
                        'ruleId' => $appliedRule->id,
                    ]);
                }
            } catch (Exception $e) {
                Log::error('Failed to process promotion', [
                    'promotionId' => $promotion->id,
                    'promotionName' => $promotion->name,
                    'error' => $e->getMessage(),
                ]);
                // Continue processing other promotions even if one fails
            }
        });
    }

    /**
     * Apply the first matching rule actions
     */
    private function applyFirstMatchingRuleActions(Collection $rules, Context $context): ?PromotionRule
    {
        $matchingRule = $rules->first(function (PromotionRule $rule) use ($context) {
            return $this->evaluateRuleConditions($rule, $context);
        });

        if ($matchingRule) {
            $this->applyRuleActions($matchingRule, $context);

            return $matchingRule;
        }

        return null;
    }

    /**
     * Evaluate all conditions for a rule
     */
    private function evaluateRuleConditions(PromotionRule $rule, Context $context): bool
    {
        if ($rule->conditions->isEmpty()) {
            return true;
        }

        return $rule->conditions->every(function (PromotionCondition $condition) use ($context) {
            try {
                return $condition->evaluate($context);
            } catch (Exception $e) {
                Log::error('Failed to evaluate promotion condition', [
                    'conditionId' => $condition->id,
                    'conditionType' => $condition->type->value,
                    'error' => $e->getMessage(),
                ]);

                return false;
            }
        });
    }

    /**
     * Apply all actions for a rule
     */
    private function applyRuleActions(PromotionRule $rule, Context $context): void
    {
        $rule->actions->each(function (PromotionAction $action) use ($context, $rule) {
            try {
                $action->apply($context);

                Log::debug('Applied promotion action', [
                    'actionId' => $action->id,
                    'actionType' => $action->type->value,
                    'ruleId' => $rule->id,
                ]);
            } catch (Exception $e) {
                Log::error('Failed to apply promotion action', [
                    'actionId' => $action->id,
                    'actionType' => $action->type->value,
                    'ruleId' => $rule->id,
                    'error' => $e->getMessage(),
                ]);
                // Continue applying other actions even if one fails
            }
        });
    }
}
