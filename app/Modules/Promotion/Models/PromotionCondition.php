<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Models;

use App\Modules\Promotion\Engine\Conditions\MinimumQuantityCondition;
use App\Modules\Promotion\Engine\Conditions\MinimumSpendAmount;
use App\Modules\Promotion\Engine\Conditions\MinimumYearOverYearSpendGrowthPercent;
use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Enums\ConditionType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use InvalidArgumentException;

final class PromotionCondition extends Model
{
    use HasFactory;
    use HasUuids;

    protected $guarded = [];

    public function rule(): BelongsTo
    {
        return $this->belongsTo(PromotionRule::class, 'promotion_rule_id');
    }

    /**
     * @throws InvalidArgumentException
     */
    public function evaluate(Context $context): bool
    {
        $condition = match ($this->type) {
            ConditionType::MinimumSpendAmount => app(MinimumSpendAmount::class),
            ConditionType::MinimumYearOverYearSpendGrowthPercent => app(MinimumYearOverYearSpendGrowthPercent::class),
            ConditionType::MinimumQuantity => app(MinimumQuantityCondition::class),
            default => throw new InvalidArgumentException('Invalid condition type'),
        };

        return $condition->evaluate($this->rule, $context, $this->config);
    }

    protected function casts(): array
    {
        return [
            'type' => ConditionType::class,
            'config' => 'json',
        ];
    }
}
