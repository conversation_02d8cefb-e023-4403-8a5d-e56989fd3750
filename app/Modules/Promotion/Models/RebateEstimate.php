<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Models;

use App\Models\Clinic;
use App\Modules\Promotion\Factories\RebateEstimateFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class RebateEstimate extends Model
{
    use HasFactory;
    use HasUuids;

    protected $guarded = [];

    public static function newFactory(): RebateEstimateFactory
    {
        return RebateEstimateFactory::new();
    }

    public function promotion(): BelongsTo
    {
        return $this->belongsTo(Promotion::class);
    }

    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    protected function estimatedRebateAmount(): Attribute
    {
        return Attribute::make(
            get: fn () => (int) (floor($this->current_spend_amount * ($this->current_rebate_percent / 100))),
        );
    }
}
