<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Models;

use App\Modules\Order\Actions\GetSpendAmountInPeriod;
use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\ConditionType;
use Brick\Money\Money;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property-read Promotion $promotion
 * @property-read \Illuminate\Database\Eloquent\Collection<int, PromotionCondition> $conditions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, PromotionAction> $actions
 */
final class PromotionRule extends Model
{
    use HasFactory;
    use HasUuids;

    protected $guarded = [];

    public function promotion(): BelongsTo
    {
        return $this->belongsTo(Promotion::class);
    }

    public function conditions(): HasMany
    {
        return $this->hasMany(PromotionCondition::class);
    }

    public function actions(): HasMany
    {
        return $this->hasMany(PromotionAction::class);
    }

    public function getMinimumSpendAmountThreshold(string $clinicId): Money
    {
        $conditions = [
            ConditionType::MinimumSpendAmount,
            ConditionType::MinimumYearOverYearSpendGrowthPercent,
        ];

        return $this->conditions
            ->filter(fn (PromotionCondition $condition) => in_array($condition->type, $conditions))
            ->reduce(function (Money $threshold, PromotionCondition $condition) use ($clinicId) {
                return $threshold->plus(match ($condition->type) {
                    ConditionType::MinimumSpendAmount => Money::ofMinor((int) $condition->config['minimum_spend_amount'], 'USD'),
                    ConditionType::MinimumYearOverYearSpendGrowthPercent => $this->getMinimumSpendAmountThresholdFromGrowthPercent(
                        $clinicId,
                        (float) $condition->config['minimum_year_over_year_spend_growth_percent']
                    ),
                    default => Money::ofMinor(0, 'USD'),
                });
            }, Money::ofMinor(0, 'USD'));
    }

    public function getRebatePercent(): float
    {
        $action = $this->actions->firstWhere('type', ActionType::UpdateRebateEstimate);

        if (! $action) {
            return 0;
        }

        return $action->config['rebate_percent'];
    }

    private function getMinimumSpendAmountThresholdFromGrowthPercent(string $clinicId, float $growthPercent): Money
    {
        $productOfferIds = $this->promotion->productOffers->pluck('id')->toArray();

        $previousPeriodStart = $this->promotion->started_at->clone()->subYear();
        $previousPeriodEnd = $this->promotion->ended_at->clone()->subYear();

        $getSpendAmountInPeriod = app(GetSpendAmountInPeriod::class);

        $spendInPreviousPeriod = $getSpendAmountInPeriod->handle(
            $clinicId,
            $productOfferIds,
            $previousPeriodStart,
            $previousPeriodEnd
        );

        $growthMultiplier = 1 + ($growthPercent / 100);
        $threshold = $spendInPreviousPeriod->multipliedBy($growthMultiplier);

        return $threshold;
    }
}
