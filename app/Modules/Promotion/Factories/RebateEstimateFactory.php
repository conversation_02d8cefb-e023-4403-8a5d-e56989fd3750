<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Factories;

use App\Models\Clinic;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Models\RebateEstimate;
use Illuminate\Database\Eloquent\Factories\Factory;

final class RebateEstimateFactory extends Factory
{
    protected $model = RebateEstimate::class;

    public function definition(): array
    {
        return [
            'clinic_id' => Clinic::factory(),
            'promotion_id' => Promotion::factory(),
            'current_spend_amount' => 100,
            'current_rebate_percent' => 10,
            'next_tier_minimum_spend_amount_threshold' => 200,
            'next_tier_rebate_percent' => 15,
        ];
    }
}
