<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Factories;

use App\Models\Vendor;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

final class PromotionFactory extends Factory
{
    protected $model = Promotion::class;

    public function definition(): array
    {
        return [
            'name' => fake()->name,
            'type' => PromotionType::Rebate,
            'description' => fake()->text,
            'vendor_id' => Vendor::factory(),
            'promotionable_type' => GpoAccount::class,
            'promotionable_id' => GpoAccount::factory(),
            'priority' => 1,
            'started_at' => Carbon::today()->subDay(1),
            'ended_at' => Carbon::today()->addDay(1),
            'status' => PromotionStatus::Active,
        ];
    }
}
