<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class RemoveFromSavedItemsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->account->clinics->contains(fn ($clinic) => $clinic->id === $this->clinicId());
    }

    public function rules(): array
    {
        return [
            'saved_item_id' => 'required|string|uuid|exists:saved_items,id,clinic_id,'.$this->clinicId(),
        ];
    }

    public function messages(): array
    {
        return [
            'saved_item_id.required' => 'Saved item ID is required.',
            'saved_item_id.uuid' => 'Saved item ID must be a valid UUID.',
            'saved_item_id.exists' => 'The specified saved item does not exist.',
            'saved_item_id.clinic_id' => 'Unauthorized action.',
        ];
    }
}
