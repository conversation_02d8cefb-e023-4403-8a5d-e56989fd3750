<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Http\Requests;

use App\Models\CartItem;
use Illuminate\Foundation\Http\FormRequest;

final class AddToSavedItemsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->account->clinics->contains(fn ($clinic) => $clinic->id === $this->clinicId()) &&
            CartItem::find($this->input('cart_item_id'))->cart->clinic_id === $this->clinicId();
    }

    public function rules(): array
    {
        return [
            'cart_item_id' => 'required|string|uuid|exists:cart_items,id',
        ];
    }

    public function messages(): array
    {
        return [
            'cart_item_id.required' => 'Cart item ID is required.',
            'cart_item_id.uuid' => 'Cart item ID must be a valid UUID.',
            'cart_item_id.exists' => 'The specified cart item does not exist.',
        ];
    }
}
