<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Http\Controllers;

use App\Modules\SavedItems\Actions\AddToCartFromSavedItemsAction;
use App\Modules\SavedItems\Actions\AddToSavedItemsAction;
use App\Modules\SavedItems\Data\SavedItemData;
use App\Modules\SavedItems\Data\SavedItemsCollectionData;
use App\Modules\SavedItems\Http\Requests\AddToSavedItemsRequest;
use App\Modules\SavedItems\Http\Requests\GetSavedItemsRequest;
use App\Modules\SavedItems\Http\Requests\RemoveFromSavedItemsRequest;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;

final class SavedItemsController extends Controller
{
    public function __construct(
        private readonly AddToSavedItemsAction $addToSavedItemsAction,
        private readonly AddToCartFromSavedItemsAction $addToCartFromSavedItemsAction,
    ) {}

    public function addToSavedItems(AddToSavedItemsRequest $request): JsonResponse
    {
        $savedItem = $this->addToSavedItemsAction->handle($request->validated('cart_item_id'));

        return new JsonResponse(
            SavedItemData::from($savedItem->load(['productOffer', 'productOffer.vendor']))
        );
    }

    public function addToCartFromSavedItems(RemoveFromSavedItemsRequest $request): JsonResponse
    {
        $savedItem = $this->addToCartFromSavedItemsAction->handle($request->validated('saved_item_id'));

        return new JsonResponse(
            SavedItemData::from($savedItem->load(['productOffer', 'productOffer.vendor']))
        );
    }

    public function removeFromSavedItems(RemoveFromSavedItemsRequest $request): Response
    {
        $savedItem = SavedItem::findOrFail($request->validated('saved_item_id'));

        $savedItem->delete();

        return new Response(null, 204);
    }

    public function index(GetSavedItemsRequest $request): JsonResponse
    {
        $savedItems = SavedItem::where('clinic_id', $request->clinicId())
            ->with(['productOffer', 'productOffer.vendor', 'productOffer.product'])
            ->orderBy('created_at', 'desc')
            ->get();

        return new JsonResponse(
            SavedItemsCollectionData::fromModel($savedItems)
        );
    }
}
