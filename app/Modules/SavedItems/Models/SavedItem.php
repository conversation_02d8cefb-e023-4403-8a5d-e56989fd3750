<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Models;

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Modules\SavedItems\Factories\SavedItemFactory;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class SavedItem extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'saved_items';

    protected $fillable = [
        'clinic_id',
        'product_offer_id',
        'quantity',
    ];

    protected $casts = [
        'created_at' => 'datetime',
    ];

    public static function newFactory(): SavedItemFactory
    {
        return SavedItemFactory::new();
    }

    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    public function productOffer(): BelongsTo
    {
        return $this->belongsTo(ProductOffer::class);
    }

    public function belongsToClinic(string $clinicId): bool
    {
        return $this->clinic_id === $clinicId;
    }

    public function getDisplayName(): string
    {
        if ($this->productOffer && isset($this->productOffer->name)) {
            return $this->productOffer->name;
        }

        return 'Unnamed Item';
    }
}
