<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Factories;

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Modules\SavedItems\Models\SavedItem>
 */
final class SavedItemFactory extends Factory
{
    protected $model = SavedItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'clinic_id' => Clinic::factory(),
            'product_offer_id' => ProductOffer::factory(),
            'quantity' => $this->faker->numberBetween(1, 10),
        ];
    }

    /**
     * Create a saved item for a specific product offer.
     */
    public function forProductOffer(ProductOffer $productOffer, ?Clinic $clinic = null): static
    {
        return $this->state([
            'product_offer_id' => $productOffer->id,
            'clinic_id' => $clinic?->id ?? Clinic::factory(),
        ]);
    }

    /**
     * Create a saved item for a specific clinic.
     */
    public function forClinic(Clinic $clinic, ?ProductOffer $productOffer = null): static
    {
        return $this->state([
            'clinic_id' => $clinic->id,
            'product_offer_id' => $productOffer?->id ?? ProductOffer::factory(),
        ]);
    }
}
