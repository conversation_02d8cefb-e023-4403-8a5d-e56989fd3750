<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Actions;

use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\SavedItems\Models\SavedItem;

final class AddToCartFromSavedItemsAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction
    ) {}

    public function handle(string $savedItemId): SavedItem
    {
        $savedItem = SavedItem::with(['clinic.cart', 'productOffer'])->findOrFail($savedItemId);

        $clinic = $savedItem->clinic;
        $productOffer = $savedItem->productOffer;
        $quantity = $savedItem->quantity;

        $cart = $clinic->cart;
        if (! $cart) {
            $cart = $clinic->cart()->create();
        }

        $cartItem = $cart->items()->where('product_offer_id', $productOffer->id)->first();

        if ($cartItem) {
            $cart->updateItem($productOffer, ['quantity' => $cartItem->quantity + $quantity]);
        } else {
            $cart->addItem($productOffer, $quantity);
        }

        $savedItem->delete();

        // Clear cart cache to ensure fresh data is loaded
        $this->getCartAction->forgetCache($clinic);

        return $savedItem;
    }
}
