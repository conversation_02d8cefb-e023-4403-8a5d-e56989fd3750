<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Actions;

use App\Models\CartItem;
use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Support\Facades\Cache;

final class AddToSavedItemsAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction
    ) {}

    public function handle(string $cartItemId): SavedItem
    {
        $cartItem = CartItem::with(['cart.clinic'])->findOrFail($cartItemId);

        $clinic = $cartItem->cart->clinic;
        $productOffer = $cartItem->productOffer;
        $quantity = $cartItem->quantity;

        $savedItem = SavedItem::firstOrNew([
            'clinic_id' => $clinic->id,
            'product_offer_id' => $productOffer->id,
        ], [
            'quantity' => 0,
        ]);

        if ($savedItem->exists) {
            $savedItem->quantity += $quantity;
        } else {
            $savedItem->quantity = $quantity;
        }

        $savedItem->save();

        $cartItem->delete();

        // Clear cart cache to ensure fresh data is loaded
        $this->getCartAction->forgetCache($clinic);

        return $savedItem;
    }
}
