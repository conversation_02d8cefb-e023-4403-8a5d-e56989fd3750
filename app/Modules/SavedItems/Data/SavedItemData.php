<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Data;

use App\Modules\SavedItems\Models\SavedItem;
use Carbon\Carbon;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Casts\DateTimeInterfaceCast;
use <PERSON><PERSON>\LaravelData\Data;

final class SavedItemData extends Data
{
    public function __construct(
        public string $id,
        public string $clinic_id,
        public string $product_offer_id,
        public int $quantity,
        #[WithCast(DateTimeInterfaceCast::class)]
        public Carbon $created_at,
        public ProductOfferData $product_offer,
    ) {}

    public static function fromModel(SavedItem $savedItem): self
    {
        return new self(
            id: $savedItem->id,
            clinic_id: $savedItem->clinic_id,
            product_offer_id: $savedItem->product_offer_id,
            quantity: $savedItem->quantity,
            created_at: $savedItem->created_at,
            product_offer: ProductOfferData::fromModel($savedItem->productOffer, $savedItem->clinic),
        );
    }
}
