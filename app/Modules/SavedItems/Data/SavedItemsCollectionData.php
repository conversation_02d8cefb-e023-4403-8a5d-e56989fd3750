<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Data;

use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;

final class SavedItemsCollectionData extends Data
{
    public function __construct(
        #[DataCollectionOf(SavedItemData::class)]
        public Collection $savedItems,
    ) {}

    public static function fromModel(Collection $savedItems): self
    {
        return new self(
            savedItems: SavedItemData::collect($savedItems)
        );
    }
}
