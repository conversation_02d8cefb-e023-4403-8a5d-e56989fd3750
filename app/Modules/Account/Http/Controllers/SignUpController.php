<?php

declare(strict_types=1);

namespace App\Modules\Account\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Account\Actions\SignUpForClinicAccount;
use App\Modules\Account\Exceptions\GpoInvitationAcceptedException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\ValidationException;

final class SignUpController extends Controller
{
    use ValidatesGpoInvitationToken;

    public function __invoke(
        Request $request,
        SignUpForClinicAccount $signUpForClinicAccount,
    ): JsonResponse {
        $data = $request->validate([
            'account_type' => ['required', 'string', 'in:clinic_account'],
            'invitation_token' => ['string'],
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255', 'unique:users,email'],
            'password' => ['required', Password::defaults()],
        ]);

        if ($request->has('invitation_token')) {
            $data['invitation_id'] = $this->validateInvitationToken(base64_decode($data['invitation_token']), $data['email']);
        }

        try {
            $account = ($signUpForClinicAccount)($data);
        } catch (GpoInvitationAcceptedException $e) {
            throw ValidationException::withMessages([
                'invitation_token' => ['The invitation token has already been accepted.'],
            ]);
        }

        Auth::loginUsingId($account->id);

        return new JsonResponse($account, JsonResponse::HTTP_CREATED);
    }
}
