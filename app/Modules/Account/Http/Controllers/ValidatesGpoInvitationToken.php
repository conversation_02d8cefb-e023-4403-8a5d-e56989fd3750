<?php

declare(strict_types=1);

namespace App\Modules\Account\Http\Controllers;

use App\Support\Facades\Jwt;
use App\Support\Jwt\Exceptions\InvalidTokenException;
use Illuminate\Validation\ValidationException;

trait ValidatesGpoInvitationToken
{
    private function validateInvitationToken(string $invitationToken, string $subject): string
    {
        try {
            $token = Jwt::decode($invitationToken);
        } catch (InvalidTokenException $e) {
            throw ValidationException::withMessages([
                'invitation_token' => ['The invitation token is not valid or has expired.'],
            ]);
        }

        if (! Jwt::validate($token->toString(), [Jwt::constraintForSubject($subject)])) {
            throw ValidationException::withMessages([
                'invitation_token' => ['The invitation token is not valid or has expired.'],
            ]);
        }

        return $token->claims()->get('jti');
    }
}
