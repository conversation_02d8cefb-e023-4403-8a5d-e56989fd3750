<?php

declare(strict_types=1);

namespace App\Modules\Account\Http\Controllers;

use App\Modules\Account\Data\ClinicAccountData;
use App\Modules\Account\Exceptions\GpoInvitationAcceptedException;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

final class AcceptGpoInvitationController
{
    use AuthorizesRequests;
    use ValidatesGpoInvitationToken;

    public function __invoke(Request $request, ClinicAccount $account): JsonResponse
    {
        $this->authorize('update', $account);

        $data = $request->validate([
            'invitation_token' => ['required', 'string'],
        ]);

        $user = $request->user();

        $invitationId = $this->validateInvitationToken(base64_decode($data['invitation_token']), $user->email);

        try {
            $account->acceptGpoInvite($invitationId);
        } catch (GpoInvitationAcceptedException $e) {
            throw ValidationException::withMessages([
                'invitation_token' => ['The invitation token has already been accepted.'],
            ]);
        }

        return new JsonResponse(ClinicAccountData::from($account->fresh()));
    }
}
