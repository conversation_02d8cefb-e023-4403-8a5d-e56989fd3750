<?php

declare(strict_types=1);

namespace App\Modules\Account\Http\Controllers;

use App\Modules\Account\Actions\UpdateClinicAccount;
use App\Modules\Account\Data\ClinicAccountData;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class ClinicAccountController
{
    use AuthorizesRequests;

    public function show(ClinicAccount $account): JsonResponse
    {
        $this->authorize('view', $account);

        $account->load(['clinics', 'gpo.recommendedVendors']);

        return new JsonResponse(ClinicAccountData::from($account));
    }

    public function update(Request $request, ClinicAccount $account, UpdateClinicAccount $updateClinicAccount): JsonResponse
    {
        $this->authorize('update', $account);

        $data = $request->validate([
            'name' => ['string', 'max:255'],
            'ein' => ['string', 'max:10', 'regex:/^\d{2}-\d{7}$/'],
            'phone_number' => ['string', 'max:15'],
        ]);

        $account = $updateClinicAccount($account->id, $data);

        return new JsonResponse($account);
    }
}
