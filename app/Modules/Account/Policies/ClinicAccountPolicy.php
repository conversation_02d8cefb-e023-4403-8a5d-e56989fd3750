<?php

declare(strict_types=1);

namespace App\Modules\Account\Policies;

use App\Models\User;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Auth\Access\HandlesAuthorization;

final class ClinicAccountPolicy
{
    use HandlesAuthorization;

    public function view(User $user, ClinicAccount $account): bool
    {
        if ($user->can('viewNova')) {
            return true;
        }

        return $user->belongsToAccount($account->id);
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user, ClinicAccount $account): bool
    {
        if ($user->can('viewNova')) {
            return true;
        }

        return $user->hasRole([ClinicAccountRole::Owner, ClinicAccountRole::Admin]) && $user->belongsToAccount($account->id);
    }
}
