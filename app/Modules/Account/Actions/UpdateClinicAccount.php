<?php

declare(strict_types=1);

namespace App\Modules\Account\Actions;

use App\Modules\Account\Data\ClinicAccountData;
use App\Modules\Account\Models\ClinicAccount;

final class UpdateClinicAccount
{
    public function __invoke(string $accountId, array $data): ClinicAccountData
    {
        $account = ClinicAccount::query()->findOrFail($accountId);

        if (isset($data['name'])) {
            $account->update(['name' => $data['name']]);

            unset($data['name']);
        }

        if ($data !== []) {
            $account->details()->updateOrCreate([], $data);
        }

        return ClinicAccountData::from($account->fresh());
    }
}
