<?php

declare(strict_types=1);

namespace App\Modules\Account\Actions;

use App\Mail\UserRegistered;
use App\Models\User;
use App\Modules\Account\Data\UserData;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;

final class SignUpForClinicAccount
{
    public function __invoke(array $data): UserData
    {
        $user = DB::transaction(function () use ($data) {
            $account = ClinicAccount::bootstrap();

            if (isset($data['invitation_id'])) {
                $account->acceptGpoInvite($data['invitation_id']);
            }

            $user = User::query()
                ->create([
                    'account_id' => $account->id,
                    'name' => $data['name'],
                    'email' => $data['email'],
                    'password' => Hash::driver('bcrypt')->make($data['password']),
                ]);

            $user->assignRole(ClinicAccountRole::Owner);

            Mail::to($user->email)->send(new UserRegistered($user));

            return $user->fresh();
        });

        return UserData::from($user);
    }
}
