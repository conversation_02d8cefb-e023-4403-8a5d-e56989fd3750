<?php

declare(strict_types=1);

use App\Modules\Account\Http\Controllers\AcceptGpoInvitationController;
use App\Modules\Account\Http\Controllers\ClinicAccountController;
use App\Modules\Account\Http\Controllers\SignUpController;
use Illuminate\Support\Facades\Route;

Route::middleware('api')->prefix('api')->group(function () {
    Route::middleware('guest')->group(function () {
        Route::post('/accounts/sign-up', SignUpController::class);
    });

    Route::middleware('auth:sanctum')->group(function () {
        Route::resource('/accounts', ClinicAccountController::class)->only('show', 'update');
        Route::post('/accounts/{account}/accept-gpo-invitation', AcceptGpoInvitationController::class);
    });
});
