<?php

declare(strict_types=1);

namespace App\Modules\Account\Nova;

use App\Modules\Gpo\Nova\GpoAccount;
use App\Nova\Clinic;
use App\Nova\Resource;
use Laravel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\HasOne;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

final class ClinicAccount extends Resource
{
    public static $model = \App\Modules\Account\Models\ClinicAccount::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('GPO', 'gpo', GpoAccount::class)
                ->nullable()
                ->searchable(),

            Text::make('Name')
                ->rules('required', 'string', 'max:255'),

            HasOne::make('Extra Details', 'details', ClinicAccountDetails::class)
                ->required(),

            HasMany::make('Users', 'users', User::class),

            HasMany::make('Clinics', 'clinics', Clinic::class),
        ];
    }
}
