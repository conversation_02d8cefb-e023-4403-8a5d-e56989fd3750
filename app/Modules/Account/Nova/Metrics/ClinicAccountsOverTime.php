<?php

declare(strict_types=1);

namespace App\Modules\Account\Nova\Metrics;

use App\Modules\Account\Models\ClinicAccount;
use DateTimeInterface;
use Illuminate\Support\Carbon;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Value;
use <PERSON>vel\Nova\Metrics\ValueResult;
use Laravel\Nova\Nova;

final class ClinicAccountsOverTime extends Value
{
    public function calculate(NovaRequest $request): ValueResult
    {
        return $this->count($request, ClinicAccount::class);
    }

    public function ranges(): array
    {
        return [
            30 => Nova::__('30 Days'),
            60 => Nova::__('60 Days'),
            365 => Nova::__('365 Days'),
            'TODAY' => Nova::__('Today'),
            'MTD' => Nova::__('Month To Date'),
            'QTD' => Nova::__('Quarter To Date'),
            'YTD' => Nova::__('Year To Date'),
        ];
    }

    public function cacheFor(): DateTimeInterface
    {
        return Carbon::now()->addMinutes(5);
    }
}
