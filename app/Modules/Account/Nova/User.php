<?php

declare(strict_types=1);

namespace App\Modules\Account\Nova;

use App\Nova\Resource;
use Illuminate\Validation\Rules;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Password;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class User extends Resource
{
    public static $model = \App\Models\User::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
        'email',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Account', 'account', Account::class)
                ->nullable()
                ->searchable()
                ->hideFromIndex(fn (NovaRequest $request) => $request->viaRelationship()),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make('Email')
                ->sortable()
                ->rules('required', 'email', 'max:255')
                ->creationRules('unique:users,email')
                ->updateRules('unique:users,email,{{resourceId}}'),

            Password::make('Password')
                ->onlyOnForms()
                ->creationRules('required', Rules\Password::defaults())
                ->updateRules('nullable', Rules\Password::defaults()),
        ];
    }
}
