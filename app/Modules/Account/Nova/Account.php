<?php

declare(strict_types=1);

namespace App\Modules\Account\Nova;

use App\Nova\Resource;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

final class Account extends Resource
{
    public static $model = \App\Modules\Account\Models\Account::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name', 'name')->sortable(),
        ];
    }
}
