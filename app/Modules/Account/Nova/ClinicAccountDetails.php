<?php

declare(strict_types=1);

namespace App\Modules\Account\Nova;

use App\Nova\Resource;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

final class ClinicAccountDetails extends Resource
{
    public static $model = \App\Modules\Account\Models\ClinicAccountDetails::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'ein',
        'phone_number',
    ];

    public static function label(): string
    {
        return 'Clinic Account Extra Details';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('EIN')
                ->rules('required', 'string', 'max:10', 'regex:/^\d{2}-\d{7}$/'),

            Text::make('Phone Number')
                ->rules('required', 'string', 'max:15'),
        ];
    }
}
