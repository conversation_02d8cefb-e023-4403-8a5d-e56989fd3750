<?php

declare(strict_types=1);

namespace App\Modules\Account\Data;

use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Data\GpoAccountData;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Lazy;

final class ClinicAccountData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly ?string $name,
        public readonly ?string $ein,
        public readonly ?string $phoneNumber,
        public readonly ?GpoAccountData $gpo,
        #[DataCollectionOf(ClinicData::class)]
        public readonly Lazy|Collection $clinics,
    ) {}

    public static function fromModel(ClinicAccount $account): self
    {
        return new self(
            id: $account->id,
            name: $account->name,
            ein: $account->details?->ein,
            phoneNumber: $account->details?->phone_number,
            gpo: is_null($account->gpo) ? null : GpoAccountData::from($account->gpo),
            clinics: Lazy::whenLoaded('clinics', $account, fn () => ClinicData::collect($account->clinics, Collection::class)),
        );
    }

    public function with(): array
    {
        return [
            'hasVendorConnections' => $this->clinics->some(fn (ClinicData $clinic) => $clinic->hasVendorConnections()),
            /** @deprecated */
            'hasAnyVendorConnected' => $this->clinics->some(fn (ClinicData $clinic) => $clinic->hasVendorConnections()),
            /** @deprecated */
            'onboardingStatus' => 'DONE',
        ];
    }
}
