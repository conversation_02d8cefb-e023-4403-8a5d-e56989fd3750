<?php

declare(strict_types=1);

namespace App\Modules\Account\Data;

use App\Models\Clinic;
use Spatie\LaravelData\Data;

final class ClinicData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly int $vendorConnectionsCount,
    ) {}

    public static function fromModel(Clinic $clinic): self
    {
        return new self(
            id: $clinic->id,
            name: $clinic->name,
            vendorConnectionsCount: $clinic->vendors()->count(),
        );
    }

    public function with(): array
    {
        return [
            'hasVendorConnections' => $this->hasVendorConnections(),
            /** @deprecated */
            'hasAnyVendorConnected' => $this->hasVendorConnections(),
        ];
    }

    public function hasVendorConnections(): bool
    {
        return $this->vendorConnectionsCount > 0;
    }
}
