<?php

declare(strict_types=1);

namespace App\Modules\Account\Data;

use App\Models\User;
use Spatie\LaravelData\Data;

final class UserData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $accountId,
        public readonly string $name,
        public readonly string $email,
        public readonly array $roles,
    ) {}

    public static function fromModel(User $user): self
    {
        return new self(
            id: $user->id,
            accountId: $user->account_id,
            name: $user->name,
            email: $user->email,
            roles: $user->roles->pluck('name')->toArray(),
        );
    }
}
