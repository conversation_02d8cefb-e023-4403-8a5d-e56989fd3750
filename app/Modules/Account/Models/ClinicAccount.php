<?php

declare(strict_types=1);

namespace App\Modules\Account\Models;

use App\Models\Clinic;
use App\Modules\Account\Models\Factories\ClinicAccountFactory;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Models\GpoInvitation;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\DB;
use Parental\HasParent;

final class ClinicAccount extends Account
{
    use HasFactory;
    use HasParent;

    protected $with = [
        'gpo',
        'details',
    ];

    public static function bootstrap(): static
    {
        return DB::transaction(fn () => tap(
            self::query()->create(['name' => '']),
            fn ($account) => $account->details()->create()
        ));
    }

    public static function newFactory(): ClinicAccountFactory
    {
        return ClinicAccountFactory::new();
    }

    public function getForeignKey(): string
    {
        return 'clinic_account_id';
    }

    public function gpo(): BelongsTo
    {
        return $this->belongsTo(GpoAccount::class, 'gpo_account_id');
    }

    public function details(): HasOne
    {
        return $this->hasOne(ClinicAccountDetails::class);
    }

    public function clinics(): HasMany
    {
        return $this->hasMany(Clinic::class);
    }

    public function acceptGpoInvite(string $invitationId): bool
    {
        return DB::transaction(function () use ($invitationId) {
            $invitation = GpoInvitation::query()->findOrFail($invitationId);

            $invitation->accept($this->id);

            return $this->update([
                'gpo_account_id' => $invitation->gpo_account_id,
                'gpo_membership_number' => $invitation->gpo_membership_number,
            ]);
        });
    }
}
