<?php

declare(strict_types=1);

namespace App\Modules\Account\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Parental\HasChildren;

/**
 * @final
 */
class Account extends Model
{
    use HasChildren;
    use HasUuids;

    protected $guarded = [];

    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'account_id');
    }
}
