<?php

declare(strict_types=1);

namespace App\Modules\Account\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class ClinicAccountDetails extends Model
{
    use HasUuids;

    protected $guarded = [];

    public function account(): BelongsTo
    {
        return $this->belongsTo(ClinicAccount::class, 'clinic_account_id');
    }
}
