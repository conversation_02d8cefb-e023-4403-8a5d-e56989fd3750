<?php

declare(strict_types=1);

namespace App\Modules\Account\Providers;

use App\Modules\Account\Nova\Account;
use App\Modules\Account\Nova\ClinicAccount;
use App\Modules\Account\Nova\ClinicAccountDetails;
use App\Modules\Account\Nova\User;
use App\Modules\Gpo\Nova\GpoAccount;
use App\Modules\Gpo\Nova\GpoAccountDetails;
use App\Modules\Gpo\Nova\GpoInvitation;
use Illuminate\Support\ServiceProvider;
use Laravel\Nova\Nova;

final class AccountServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->registerRoutes();

        $this->registerNovaResources();
    }

    private function registerRoutes(): void
    {
        $this->loadRoutesFrom(__DIR__.'/../routes.php');
    }

    private function registerNovaResources(): void
    {
        Nova::resources([
            Account::class,
            ClinicAccount::class,
            ClinicAccountDetails::class,
            GpoAccount::class,
            GpoAccountDetails::class,
            GpoInvitation::class,
            User::class,
        ]);
    }
}
