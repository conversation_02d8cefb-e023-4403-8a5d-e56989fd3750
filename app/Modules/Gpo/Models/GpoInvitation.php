<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Models;

use App\Modules\Account\Exceptions\GpoInvitationAcceptedException;
use App\Modules\Account\Exceptions\GpoInvitationExpiredException;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\Factories\GpoInvitationFactory;
use App\Support\Facades\Jwt;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

final class GpoInvitation extends Model
{
    use HasFactory;
    use HasUuids;

    protected $guarded = [];

    protected $casts = [
        'accepted_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public static function factory(): GpoInvitationFactory
    {
        return GpoInvitationFactory::new();
    }

    public function gpo(): BelongsTo
    {
        return $this->belongsTo(GpoAccount::class, 'gpo_account_id');
    }

    public function acceptedBy(): BelongsTo
    {
        return $this->belongsTo(ClinicAccount::class, 'clinic_account_id');
    }

    public function accept(string $accountId): void
    {
        if ($this->expires_at->isPast()) {
            throw new GpoInvitationExpiredException();
        }

        if ($this->accepted_at) {
            throw new GpoInvitationAcceptedException();
        }

        $this->update([
            'clinic_account_id' => $accountId,
            'accepted_at' => Carbon::now(),
        ]);
    }

    public function token(): Attribute
    {
        return Attribute::get(
            fn () => $this->exists ? base64_encode(Jwt::issue(
                id: $this->id,
                subject: $this->email,
                claims: [
                    'gpo' => $this->gpo->name,
                    'img' => $this->gpo->details?->image_url,
                    'mno' => $this->gpo_membership_number,
                ],
                expiresAt: $this->expires_at->toDateTimeImmutable(),
            )) : null,
        );
    }

    public function signupUrl(): Attribute
    {
        return Attribute::get(
            fn () => config('app.frontend_url')."/sign-up?invitationToken={$this->token}",
        );
    }
}
