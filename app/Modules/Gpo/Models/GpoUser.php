<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Models;

use App\Modules\Gpo\Models\Factories\GpoUserFactory;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

final class GpoUser extends Authenticatable
{
    use HasApiTokens, HasFactory, HasUuids, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $with = [
        'account',
    ];

    /**
     * Get the GPO account that the user belongs to.
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(GpoAccount::class, 'account_id');
    }

    public function belongsToAccount(string $accountId): bool
    {
        return $this->account_id === $accountId;
    }

    protected static function newFactory(): GpoUserFactory
    {
        return GpoUserFactory::new();
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }
}
