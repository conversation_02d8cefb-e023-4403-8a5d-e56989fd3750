<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Models\Factories;

use App\Modules\Gpo\Models\GpoAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

final class GpoAccountFactory extends Factory
{
    protected $model = GpoAccount::class;

    public function definition(): array
    {
        return [
            'name' => fake()->randomElement(['TVC', 'VGP']),
        ];
    }
}
