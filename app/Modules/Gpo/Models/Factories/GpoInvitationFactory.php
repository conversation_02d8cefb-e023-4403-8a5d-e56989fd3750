<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Models\Factories;

use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Models\GpoInvitation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

final class GpoInvitationFactory extends Factory
{
    protected $model = GpoInvitation::class;

    public function definition(): array
    {
        return [
            'gpo_account_id' => GpoAccount::factory(),
            'email' => fake()->email,
            'expires_at' => Carbon::now()->addDays(7),
        ];
    }

    public function accepted(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'clinic_account_id' => ClinicAccount::factory(),
                'accepted_at' => Carbon::now(),
            ];
        });
    }

    public function expired(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'accepted_at' => null,
                'expires_at' => Carbon::now()->subDays(1),
            ];
        });
    }
}
