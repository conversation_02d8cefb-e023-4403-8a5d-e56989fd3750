<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Models\Factories;

use App\Models\Vendor;
use App\Modules\Gpo\Enums\GpoSettingKey;
use App\Modules\Gpo\Models\GpoAccountSettings;
use Illuminate\Database\Eloquent\Factories\Factory;

final class GpoAccountSettingsFactory extends Factory
{
    protected $model = GpoAccountSettings::class;

    public function definition(): array
    {
        $settingKey = fake()->randomElement(GpoSettingKey::cases());

        return [
            'gpo_account_id' => fake()->uuid(),
            'setting_key' => $settingKey->value,
            'value' => $this->getValueForSettingKey($settingKey),
        ];
    }

    private function getValueForSettingKey(GpoSettingKey $settingKey): array
    {
        return match ($settingKey) {
            GpoSettingKey::PerformanceThreshold => [
                'enabled' => fake()->boolean(),
                'threshold' => fake()->randomFloat(1, 50, 95),
                'notification_message' => fake()->sentence(),
            ],
            GpoSettingKey::VendorGoals => [
                'goals' => [
                    [
                        'enabled' => fake()->boolean(),
                        'goal_amount' => fake()->numberBetween(100000, 5000000),
                        'vendor_id' => Vendor::factory()->create()->id,
                    ],
                ],
            ],
        };
    }
}
