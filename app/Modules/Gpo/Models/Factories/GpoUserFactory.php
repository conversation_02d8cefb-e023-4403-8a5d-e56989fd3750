<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Models\Factories;

use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Models\GpoUser;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

final class GpoUserFactory extends Factory
{
    protected $model = GpoUser::class;

    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'remember_token' => Str::random(10),
            'account_id' => GpoAccount::factory(),
        ];
    }

    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
