<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

final class GpoAccountDetails extends Model
{
    use HasUuids;

    protected $guarded = [];

    public function account(): BelongsTo
    {
        return $this->belongsTo(GpoAccount::class, 'gpo_account_id');
    }

    protected function imageUrl(): Attribute
    {
        return Attribute::get(fn () => $this->image_path ? url(Storage::url($this->image_path)) : null);
    }
}
