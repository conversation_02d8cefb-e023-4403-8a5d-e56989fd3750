<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Vendor;
use App\Modules\Gpo\Actions\ExportVendorOrderItemsCsv;
use App\Modules\Gpo\Actions\GetVendorsOverview;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Symfony\Component\HttpFoundation\StreamedResponse;

final class VendorsOverviewController extends Controller
{
    public function __construct(
        private readonly GetVendorsOverview $getVendorsOverview,
        private readonly ExportVendorOrderItemsCsv $exportVendorOrderItemsCsv
    ) {}

    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'required|date|before_or_equal:date_to',
            'date_to' => 'required|date|after_or_equal:date_from',
        ]);

        $gpoAccount = $request->user('gpo')->account;

        $startDate = Carbon::parse($request->input('date_from'));
        $endDate = Carbon::parse($request->input('date_to'));

        $vendorsOverview = $this->getVendorsOverview->handle($gpoAccount->id, $startDate, $endDate);

        return response()->json($vendorsOverview);
    }

    public function export(Request $request): StreamedResponse
    {
        $request->validate([
            'date_from' => 'required|date|before_or_equal:date_to',
            'date_to' => 'required|date|after_or_equal:date_from',
            'vendor_id' => 'required|string',
        ]);

        $gpoAccount = $request->user('gpo')->account;
        $vendor = Vendor::findOrFail($request->input('vendor_id'));

        $startDate = Carbon::parse($request->input('date_from'));
        $endDate = Carbon::parse($request->input('date_to'));

        return $this->exportVendorOrderItemsCsv->handle($gpoAccount->id, $vendor->id, $startDate, $endDate);
    }
}
