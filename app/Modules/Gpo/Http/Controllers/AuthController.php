<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Resources\GpoUser as GpoUserResource;
use App\Modules\Gpo\Models\GpoUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

final class AuthController extends Controller
{
    public function login(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $user = GpoUser::with('account')->where('email', $request->email)->first();

        if (! $user || ! Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        $token = $user->createToken('gpo-auth-token')->plainTextToken;

        return response()->json([
            'access_token' => $token,
            'token_type' => 'Bearer',
            'user' => new GpoUserResource($user),
        ]);
    }

    public function logout(Request $request): JsonResponse
    {
        $user = $request->user('gpo');

        $user?->currentAccessToken()?->delete();

        return response()->json(['message' => 'Successfully logged out']);
    }

    public function user(Request $request): GpoUserResource
    {
        return new GpoUserResource($request->user('gpo')->load('account'));
    }
}
