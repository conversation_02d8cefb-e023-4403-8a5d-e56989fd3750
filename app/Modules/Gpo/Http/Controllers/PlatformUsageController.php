<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Gpo\Actions\ExportPlatformUsageCsv;
use App\Modules\Gpo\Actions\GetPlatformUsageSummary;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Symfony\Component\HttpFoundation\StreamedResponse;

final class PlatformUsageController extends Controller
{
    public function __construct(
        private readonly GetPlatformUsageSummary $getPlatformUsageSummary,
        private readonly ExportPlatformUsageCsv $exportPlatformUsageCsv
    ) {}

    public function summary(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'required|date|before_or_equal:date_to',
            'date_to' => 'required|date|after_or_equal:date_from',
        ]);

        $gpoAccount = $request->user('gpo')->account;

        $startDate = Carbon::parse($request->input('date_from'));
        $endDate = Carbon::parse($request->input('date_to'));

        $platformUsageSummary = $this->getPlatformUsageSummary->handle($gpoAccount->id, $startDate, $endDate);

        return response()->json($platformUsageSummary);
    }

    public function export(Request $request): StreamedResponse
    {
        $request->validate([
            'date_from' => 'required|date|before_or_equal:date_to',
            'date_to' => 'required|date|after_or_equal:date_from',
        ]);

        $gpoAccount = $request->user('gpo')->account;

        $startDate = Carbon::parse($request->input('date_from'));
        $endDate = Carbon::parse($request->input('date_to'));

        return $this->exportPlatformUsageCsv->handle($gpoAccount->id, $startDate, $endDate);
    }
}
