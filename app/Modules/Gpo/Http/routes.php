<?php

declare(strict_types=1);

use App\Modules\Gpo\Http\Controllers\AuthController;
use App\Modules\Gpo\Http\Controllers\PlatformUsageController;
use App\Modules\Gpo\Http\Controllers\SpendAnalysisController;
use App\Modules\Gpo\Http\Controllers\VendorsOverviewController;
use Illuminate\Support\Facades\Route;

Route::prefix('api/gpo')->group(function () {
    Route::post('login', [AuthController::class, 'login']);

    Route::middleware('auth:gpo')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('user', [AuthController::class, 'user']);

        Route::group(['prefix' => 'spend-analysis'], function () {
            Route::get('/', [SpendAnalysisController::class, 'index']);
            Route::get('/export', [SpendAnalysisController::class, 'export']);
            Route::get('/summary', [SpendAnalysisController::class, 'summary']);
        });

        Route::group(['prefix' => 'vendors-overview'], function () {
            Route::get('/', [VendorsOverviewController::class, 'index']);
            Route::get('/export', [VendorsOverviewController::class, 'export']);
        });

        Route::group(['prefix' => 'platform-usage'], function () {
            Route::get('/summary', [PlatformUsageController::class, 'summary']);
            Route::get('/export', [PlatformUsageController::class, 'export']);
        });
    });
});
