<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Casts;

use App\Modules\Gpo\Enums\GpoSettingKey;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Validator;
use InvalidArgumentException;
use ValueError;

final class GpoSettingValueCast implements CastsAttributes
{
    /**
     * Cast the given value.
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): array
    {
        if (empty($value)) {
            return [];
        }

        $decoded = is_string($value) ? json_decode($value, true) : $value;

        if (! is_array($decoded)) {
            return [];
        }

        return $decoded;
    }

    /**
     * Prepare the given value for storage.
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): string
    {
        if (! is_array($value)) {
            throw new InvalidArgumentException('Value must be an array');
        }

        // Get the setting key to validate against
        $settingKey = $attributes['setting_key'] ?? null;

        if ($settingKey) {
            $this->validateValueForSettingKey($settingKey, $value);
        }

        return json_encode($value);
    }

    /**
     * Validate the value based on the setting key.
     */
    private function validateValueForSettingKey(string $settingKey, array $value): void
    {
        try {
            $enumKey = GpoSettingKey::from($settingKey);
        } catch (ValueError) {
            throw new InvalidArgumentException("Invalid setting key: {$settingKey}");
        }

        $this->validateValueStructure($enumKey, $value);
    }

    /**
     * Validate the value structure based on the setting key type.
     */
    private function validateValueStructure(GpoSettingKey $settingKey, array $value): void
    {
        $rules = $this->getValidationRules($settingKey);

        $validator = Validator::make($value, $rules);

        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new InvalidArgumentException('Validation failed: '.implode(', ', $errors));
        }
    }

    /**
     * Get validation rules for the given setting key.
     */
    private function getValidationRules(GpoSettingKey $settingKey): array
    {
        return match ($settingKey) {
            GpoSettingKey::PerformanceThreshold => [
                'enabled' => 'required|boolean',
                'threshold' => 'numeric|min:0|max:100',
                'notification_message' => 'required|string|max:255',
            ],
            GpoSettingKey::VendorGoals => [
                'goals' => 'required|array',
                'goals.*.enabled' => 'required|boolean',
                'goals.*.vendor_id' => 'required|string|exists:vendors,id',
                'goals.*.goal_amount' => 'required|numeric|min:0',
            ],
        };
    }
}
