<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Data;

use App\Models\Vendor;
use Spatie\LaravelData\Data;

final class GpoPreferredVendorData extends Data
{
    public function __construct(
        public readonly string $vendorId,
        public readonly int $order,
    ) {}

    public static function fromModel(Vendor $vendor): self
    {
        return new self(
            vendorId: $vendor->id,
            order: $vendor->pivot->order ?? 0,
        );
    }
}
