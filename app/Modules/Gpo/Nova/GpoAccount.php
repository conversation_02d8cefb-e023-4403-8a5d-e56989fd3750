<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Nova;

use App\Modules\Account\Nova\ClinicAccount;
use App\Modules\Account\Nova\User;
use App\Nova\ProductOffer;
use App\Nova\Resource;
use App\Nova\Vendor;
use <PERSON>vel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\HasOne;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

final class GpoAccount extends Resource
{
    public static $model = \App\Modules\Gpo\Models\GpoAccount::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public static function label(): string
    {
        return 'GPO Accounts';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->rules('required', 'string', 'max:4'),

            HasOne::make('Extra Details', 'details', GpoAccountDetails::class)
                ->required(),

            HasMany::make('Users', 'users', User::class),

            HasMany::make('Members', 'members', ClinicAccount::class),

            HasMany::make('Invitations', 'invitations', GpoInvitation::class),

            HasMany::make('Settings', 'settings', GpoAccountSettings::class),

            BelongsToMany::make('Recommended Vendors', 'recommendedVendors', Vendor::class)
                ->searchable()
                ->fields(fn () => [
                    Number::make('Order', 'order')
                        ->min(0)
                        ->step(1)
                        ->sortable()
                        ->rules('required', 'integer', 'min:0'),
                ]),

            BelongsToMany::make('Recommended Products', 'recommendedProducts', ProductOffer::class)
                ->searchable(),
        ];
    }
}
