<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Nova;

use App\Modules\Account\Nova\ClinicAccount;
use App\Nova\Resource;
use Illuminate\Support\Carbon;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class GpoInvitation extends Resource
{
    public static $model = \App\Modules\Gpo\Models\GpoInvitation::class;

    public static $title = 'email';

    public static $search = [
        'id',
        'email',
    ];

    public static function label(): string
    {
        return 'GPO Invitations';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Email')
                ->rules('required', 'email', 'max:255'),

            Text::make('GPO Membership Number')
                ->nullable()
                ->rules('string', 'max:255'),

            BelongsTo::make('Accepted By', 'acceptedBy', ClinicAccount::class)
                ->readonly()
                ->nullable()
                ->sortable(),

            DateTime::make('Accepted At')
                ->readonly()
                ->nullable()
                ->sortable(),

            DateTime::make('Expires At')
                ->default(Carbon::now()->addDays(7))
                ->sortable(),

            Text::make('Signup URL')
                ->onlyOnDetail()
                ->copyable(),
        ];
    }
}
