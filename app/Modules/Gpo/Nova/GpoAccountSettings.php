<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Nova;

use App\Modules\Gpo\Enums\GpoSettingKey;
use App\Nova\Resource;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class GpoAccountSettings extends Resource
{
    public static $model = \App\Modules\Gpo\Models\GpoAccountSettings::class;

    public static $title = 'setting_key';

    public static $search = [
        'id',
        'setting_key',
    ];

    public static function label(): string
    {
        return 'GPO Account Settings';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('GPO', 'gpo', GpoAccount::class)
                ->required(),

            Select::make('Setting Key', 'setting_key')
                ->options(GpoSettingKey::class)
                ->displayUsingLabels()
                ->required(),

            Code::make('Value', 'value')
                ->json()
                ->nullable()
                ->help('Provide JSON that matches the selected setting key. The structure is validated on save.'),
        ];
    }
}
