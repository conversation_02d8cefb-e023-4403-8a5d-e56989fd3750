<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Nova;

use App\Modules\Gpo\Models\GpoAccountDetails as GpoAccountDetailsModel;
use App\Nova\Resource;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Image;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class GpoAccountDetails extends Resource
{
    public static $model = GpoAccountDetailsModel::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public static function label(): string
    {
        return 'GPO Account Extra Details';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Image::make('Image', 'image_path')
                ->acceptedTypes('image/*'),
        ];
    }
}
