<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Nova;

use App\Nova\Resource;
use Illuminate\Validation\Rules;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Password;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class GpoUser extends Resource
{
    public static $model = \App\Modules\Gpo\Models\GpoUser::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
        'email',
    ];

    public static $globalSearchResults = 10;

    public static $perPageViaRelationship = 10;

    public static function label(): string
    {
        return 'GPO Users';
    }

    public static function singularLabel(): string
    {
        return 'GPO User';
    }

    public function authorizedToForceDelete(\Illuminate\Http\Request $request): bool
    {
        return $request->user()->can('forceDelete', $this->resource);
    }

    public function authorizedToRestore(\Illuminate\Http\Request $request): bool
    {
        return $request->user()->can('restore', $this->resource);
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('GPO Account', 'account', GpoAccount::class)
                ->required()
                ->searchable()
                ->hideFromIndex(fn (NovaRequest $request) => $request->viaRelationship()),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'string', 'max:255'),

            Text::make('Email')
                ->sortable()
                ->rules('required', 'email', 'max:255')
                ->creationRules('unique:gpo_users,email')
                ->updateRules('unique:gpo_users,email,{{resourceId}}'),

            Password::make('Password')
                ->onlyOnForms()
                ->creationRules('required', Rules\Password::defaults())
                ->updateRules('nullable', Rules\Password::defaults()),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [];
    }

    public function filters(NovaRequest $request): array
    {
        return [];
    }

    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
