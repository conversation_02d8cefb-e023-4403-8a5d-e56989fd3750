<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Actions;

use App\Models\Clinic;
use App\Modules\Gpo\Services\PlatformUsageCsvExportService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\StreamedResponse;

final class ExportPlatformUsageCsv
{
    public function __construct(
        private readonly PlatformUsageCsvExportService $csvExportService
    ) {}

    /**
     * Export platform usage data for a GPO account.
     */
    public function handle(string $gpoAccountId, Carbon $startDate, Carbon $endDate, int $activeDaysThreshold = 30): StreamedResponse
    {

        // Get all clinics under this GPO account
        $clinics = $this->getGpoClinics($gpoAccountId, $startDate, $endDate);

        return $this->csvExportService->exportToCsv($clinics, $startDate, $endDate, $activeDaysThreshold);
    }

    /**
     * Get all clinics under a GPO account within the date range.
     */
    private function getGpoClinics(string $gpoAccountId, Carbon $startDate, Carbon $endDate): Collection
    {
        $cacheKey = 'gpo-clinics-'.$gpoAccountId.'-'.$startDate->toDateString().'-'.$endDate->toDateString();

        return Cache::remember($cacheKey, Carbon::now()->addHour(), function () use ($gpoAccountId, $startDate, $endDate) {
            return Clinic::query()
                ->join('accounts', 'accounts.id', '=', 'clinics.clinic_account_id')
                ->where('accounts.gpo_account_id', $gpoAccountId)
                ->whereBetween('clinics.created_at', [$startDate, $endDate])
                ->with([
                    'account.gpo.recommendedVendors',
                    'users',
                    'orders' => function ($query) {
                        $query->whereNull('import_order_history_task_id')
                            ->orderBy('created_at', 'desc')
                            ->with(['items.productOffer.vendor']);
                    },
                ])
                ->select('clinics.*')
                ->get();
        });
    }
}
