<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Actions;

use App\Enums\OrderItemStatus;
use App\Models\OrderItem;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Services\VendorCsvExportService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\StreamedResponse;

final class ExportVendorOrderItemsCsv
{
    public function __construct(
        private readonly VendorCsvExportService $csvExportService
    ) {}

    public function handle(string $gpoAccountId, string $vendorId, Carbon $startDate, Carbon $endDate): StreamedResponse
    {
        $gpoAccount = GpoAccount::findOrFail($gpoAccountId);

        // Get all clinic IDs that belong to this GPO
        $clinicIds = $gpoAccount->members()
            ->with('clinics')
            ->get()
            ->flatMap(fn ($clinicAccount) => $clinicAccount->clinics->pluck('id'))
            ->values()
            ->toArray();

        // Query order items for clinics in this GPO, filtered by vendor
        $orderItems = $this->getOrderItems($gpoAccount->id, $clinicIds, $vendorId, $startDate, $endDate);

        return $this->csvExportService->exportToCsv($orderItems);
    }

    /**
     * Get all order items for the given date range.
     *
     * @return Collection<int, OrderItem>
     */
    private function getOrderItems(string $gpoAccountId, array $clinicIds, string $vendorId, Carbon $startDate, Carbon $endDate): Collection
    {
        return Cache::remember('vendor-order-items-'.$gpoAccountId.'-'.$vendorId.'-'.$startDate->toDateString().'-'.$endDate->toDateString(), Carbon::now()->addDay(),
            function () use ($clinicIds, $vendorId, $startDate, $endDate) {
                return OrderItem::query()
                    ->with([
                        'order.clinic',
                        'productOffer',
                    ])
                    ->whereHas('order', function ($query) use ($clinicIds) {
                        $query->whereIn('clinic_id', $clinicIds);
                        $query->whereNull('import_order_history_task_id');
                    })
                    ->whereHas('productOffer', function ($query) use ($vendorId) {
                        $query->where('vendor_id', $vendorId);
                    })
                    ->whereNotIn('status', [
                        OrderItemStatus::Pending,
                        OrderItemStatus::PlacementFailed,
                        OrderItemStatus::Rejected,
                        OrderItemStatus::Cancelled,
                        OrderItemStatus::Returned,
                    ])
                    ->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])
                    ->orderBy('created_at', 'desc')
                    ->get();
            });
    }
}
