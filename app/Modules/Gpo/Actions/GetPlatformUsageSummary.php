<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Actions;

use App\Models\Clinic;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

final readonly class GetPlatformUsageSummary
{
    /**
     * Get platform usage summary for a GPO account.
     */
    public function handle(string $gpoAccountId, Carbon $startDate, Carbon $endDate, int $activeDaysThreshold = 30): array
    {
        // Get all clinics under this GPO account
        $gpoClinicIds = $this->getGpoClinicsIds($gpoAccountId, $startDate, $endDate);
        $gpoClinicCount = $gpoClinicIds->count();

        // Get active clinics (clinics with orders within the threshold)
        $activeClinicsCount = $this->getActiveClinicsCount($gpoClinicIds, $activeDaysThreshold);

        // Calculate active clinics percentage
        $activeClinicsPercentage = $gpoClinicCount > 0
            ? round(($activeClinicsCount / $gpoClinicCount) * 100, 2)
            : 0.0;

        return [
            'gpo_clinics' => $gpoClinicCount,
            'active_clinics' => $activeClinicsCount,
            'active_clinics_percentage' => $activeClinicsPercentage,
        ];
    }

    /**
     * Get all clinic IDs under a GPO account.
     */
    private function getGpoClinicsIds(string $gpoAccountId, Carbon $startDate, Carbon $endDate): Collection
    {
        return Clinic::query()
            ->join('accounts', 'accounts.id', '=', 'clinics.clinic_account_id')
            ->where('accounts.gpo_account_id', $gpoAccountId)
            ->whereBetween('clinics.created_at', [$startDate, $endDate])
            ->pluck('clinics.id');
    }

    /**
     * Get count of active clinics (with orders within the threshold period).
     */
    private function getActiveClinicsCount(Collection $gpoClinicIds, int $activeDaysThreshold): int
    {
        if ($gpoClinicIds->isEmpty()) {
            return 0;
        }

        $thresholdDate = Carbon::now()->subDays($activeDaysThreshold);

        return Clinic::query()
            ->whereIn('id', $gpoClinicIds)
            ->whereHas('orders', function ($query) use ($thresholdDate) {
                $query->where('created_at', '>=', $thresholdDate)
                    ->whereNull('import_order_history_task_id');
            })
            ->count();
    }
}
