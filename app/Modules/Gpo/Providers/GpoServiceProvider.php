<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Providers;

use App\Modules\Gpo\Nova\GpoAccount;
use App\Modules\Gpo\Nova\GpoAccountDetails;
use App\Modules\Gpo\Nova\GpoAccountSettings;
use App\Modules\Gpo\Nova\GpoInvitation;
use App\Modules\Gpo\Nova\GpoUser;
use Illuminate\Support\ServiceProvider;
use Laravel\Nova\Nova;

final class GpoServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->registerRoutes();
        $this->registerNovaResources();
    }

    private function registerNovaResources(): void
    {
        Nova::resources([
            GpoUser::class,
            GpoAccount::class,
            GpoAccountDetails::class,
            GpoAccountSettings::class,
            GpoInvitation::class,
        ]);
    }

    private function registerRoutes(): void
    {
        $this->loadRoutesFrom(__DIR__.'/../Http/routes.php');
    }
}
