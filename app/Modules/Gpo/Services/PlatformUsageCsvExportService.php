<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Services;

use App\Models\Clinic;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\StreamedResponse;

final class PlatformUsageCsvExportService
{
    /**
     * Export platform usage data to a CSV file.
     *
     * @param  Collection<int, Clinic>  $clinics
     */
    public function exportToCsv(Collection $clinics, Carbon $startDate, Carbon $endDate, int $activeDaysThreshold): StreamedResponse
    {
        $fileName = $this->generateFileName($startDate, $endDate);

        $callback = function () use ($clinics, $startDate, $endDate, $activeDaysThreshold) {
            $file = fopen('php://output', 'w');

            $this->writeCsvHeaders($file);
            $this->writeCsvData($file, $clinics, $startDate, $endDate, $activeDaysThreshold);

            fclose($file);
        };

        return response()->streamDownload($callback, $fileName, [
            'Content-Type' => 'text/csv; charset=UTF-8',
        ]);
    }

    private function generateFileName(Carbon $startDate, Carbon $endDate): string
    {
        return 'platform_usage_'.$startDate->format('Y-m-d').'_to_'.$endDate->format('Y-m-d').'_'.now()->format('Y-m-d_H-i-s').'.csv';
    }

    private function writeCsvHeaders($file): void
    {
        fputcsv($file, [
            'Clinic Name',
            'Unique Users',
            'Last Active Date',
            'Average Order Total ($)',
            'Avg # All Vendors',
            'Avg # Preferred Vendors',
            'Average Session Time (min)',
            'Status',
        ]);
    }

    private function writeCsvData($file, Collection $clinics, Carbon $startDate, Carbon $endDate, int $activeDaysThreshold): void
    {
        // Process data in chunks to avoid memory issues
        $clinics->chunk(100)->each(function (Collection $clinicChunk) use ($file, $startDate, $endDate, $activeDaysThreshold) {
            foreach ($clinicChunk as $clinic) {
                $this->writeCsvRow($file, $clinic, $startDate, $endDate, $activeDaysThreshold);
            }
        });
    }

    private function writeCsvRow($file, Clinic $clinic, Carbon $startDate, Carbon $endDate, int $activeDaysThreshold): void
    {
        // Load necessary relationships
        $clinic->loadMissing(['account.gpo', 'users', 'orders']);

        $clinicData = $this->calculateClinicMetrics($clinic, $startDate, $endDate, $activeDaysThreshold);

        fputcsv($file, [
            $clinic->name ?? 'N/A',
            $clinicData['unique_users'],
            $clinicData['last_active_date'],
            $clinicData['average_order_total'],
            $clinicData['avg_all_vendors'],
            $clinicData['avg_preferred_vendors'],
            $clinicData['average_session_time'],
            $clinicData['status'],
        ]);
    }

    private function calculateClinicMetrics(Clinic $clinic, Carbon $startDate, Carbon $endDate, int $activeDaysThreshold): array
    {
        // Get unique users count
        $uniqueUsers = $clinic->users()->count();

        // Get last active date (most recent order)
        $lastOrder = $clinic->orders()
            ->whereNull('import_order_history_task_id')
            ->orderBy('created_at', 'desc')
            ->first();

        $lastActiveDate = $lastOrder ? $lastOrder->created_at->format('Y-m-d') : 'Never';

        // Get orders within date range for calculations
        $orders = $clinic->orders()
            ->whereNull('import_order_history_task_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['items.productOffer.vendor'])
            ->get();

        // Calculate average order total
        $averageOrderTotal = $orders->count() > 0
            ? round($orders->sum('total_price') / 100 / $orders->count(), 2) // Convert cents to dollars
            : 0.00;

        // Calculate average number of vendors per order
        $vendorCounts = $orders->map(function ($order) {
            return $order->items->pluck('productOffer.vendor.id')->unique()->count();
        });

        $avgAllVendors = $vendorCounts->count() > 0
            ? round($vendorCounts->average(), 1)
            : 0.0;

        // Calculate average number of preferred vendors per order
        $gpo = $clinic->account?->gpo;
        $recommendedVendorIds = $gpo
            ? $gpo->recommendedVendors()->pluck('vendor_id')->toArray()
            : [];

        $preferredVendorCounts = $orders->map(function ($order) use ($recommendedVendorIds) {
            return $order->items
                ->pluck('productOffer.vendor.id')
                ->unique()
                ->intersect($recommendedVendorIds)
                ->count();
        });

        $avgPreferredVendors = $preferredVendorCounts->count() > 0
            ? round($preferredVendorCounts->average(), 1)
            : 0.0;

        // Calculate average session time (simplified - based on user activity)
        $averageSessionTime = $this->calculateAverageSessionTime($clinic, $startDate, $endDate);

        // Determine status based on activity threshold
        $thresholdDate = Carbon::now()->subDays($activeDaysThreshold);
        $isActive = $lastOrder && $lastOrder->created_at >= $thresholdDate;
        $status = $isActive ? 'Active' : 'Inactive';

        return [
            'unique_users' => $uniqueUsers,
            'last_active_date' => $lastActiveDate,
            'average_order_total' => number_format($averageOrderTotal, 2),
            'avg_all_vendors' => $avgAllVendors,
            'avg_preferred_vendors' => $avgPreferredVendors,
            'average_session_time' => $averageSessionTime,
            'status' => $status,
        ];
    }

    // TODO: Implement session time logging
    private function calculateAverageSessionTime(Clinic $clinic, Carbon $startDate, Carbon $endDate): string
    {
        return '0.0';
    }
}
