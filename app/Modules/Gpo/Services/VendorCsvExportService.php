<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Services;

use App\Models\OrderItem;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Symfony\Component\HttpFoundation\StreamedResponse;

final class VendorCsvExportService
{
    /**
     * Export order items to a CSV file.
     *
     * @param  Collection<int, OrderItem>  $orderItems
     */
    public function exportToCsv(Collection $orderItems): StreamedResponse
    {
        if ($orderItems->isEmpty()) {
            throw new Exception('No order items found');
        }

        $fileName = $this->generateFileName($orderItems->first()->productOffer->vendor->key);

        $callback = function () use ($orderItems) {
            $file = fopen('php://output', 'w');

            $this->writeCsvHeaders($file);
            $this->writeCsvData($file, $orderItems);

            fclose($file);
        };

        return response()->streamDownload($callback, $fileName, [
            'Content-Type' => 'text/csv',
        ]);
    }

    private function generateFileName(string $vendorKey): string
    {
        return 'vendor_orders_'.$vendorKey.'_'.now()->format('Y-m-d_H-i-s').'.csv';
    }

    private function writeCsvHeaders($file): void
    {
        fputcsv($file, [
            'Order Date',
            'Clinic Name',
            'Product Name',
            'Quantity',
            'Unit Price',
            'Order Total',
            'PO Number',
            'Vendor',
        ]);
    }

    private function writeCsvData($file, Collection $orderItems): void
    {
        // Process data in chunks to avoid memory issues
        $orderItems->chunk(1000)->each(function (Collection $items) use ($file) {
            foreach ($items as $orderItem) {
                $this->writeCsvRow($file, $orderItem);
            }
        });
    }

    private function writeCsvRow($file, $orderItem): void
    {
        // Load necessary relationships if not already loaded
        $orderItem->loadMissing([
            'order.clinic',
            'productOffer.product',
            'vendor',
        ]);

        fputcsv($file, [
            $orderItem->order->created_at->format('Y-m-d'),
            $orderItem->order->clinic->name ?? 'N/A',
            $orderItem->productOffer->product->name ?? 'N/A',
            $orderItem->quantity,
            number_format($orderItem->price / 100, 2), // Convert from cents to dollars
            number_format($orderItem->total_price / 100, 2), // Convert from cents to dollars
            $orderItem->order->order_number ?? 'N/A',
            $orderItem->vendor->key ?? 'N/A',
        ]);
    }
}
