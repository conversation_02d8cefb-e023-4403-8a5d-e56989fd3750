<?php

declare(strict_types=1);

namespace App\Modules\Dashboard\Http\Controllers;

use App\Modules\Order\Actions\GetTotalSpendByClinic;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class TotalSpendController
{
    public function __invoke(Request $request, GetTotalSpendByClinic $action): JsonResponse
    {
        return response()->json($action->handle($request->clinicId()));
    }
}
