<?php

declare(strict_types=1);

namespace App\Modules\Clinic\Http\Controllers;

use App\Actions\UpdateClinic;
use App\Enums\VendorType;
use App\Http\Resources\Clinic as ClinicResource;
use App\Models\Clinic;
use App\Modules\Clinic\Actions\CreateClinic;
use App\Modules\Clinic\Data\ClinicData;
use App\Modules\Clinic\Enums\PracticeType;
use App\Modules\Clinic\Enums\ShoppingPreference;
use App\Modules\Clinic\Enums\SpecieFocus;
use App\Modules\Clinic\Rules\ValidVendorType;
use App\Rules\Ein;
use App\Rules\PostalCode;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class ClinicController
{
    use AuthorizesRequests;

    public function store(Request $request, CreateClinic $createClinic): JsonResponse
    {
        $this->authorize('create', Clinic::class);

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'business_tax_id' => ['required', 'string', new Ein()],
            'phone_number' => ['required', 'string', 'max:255'],
            'address' => ['required', 'array'],
            'address.street' => ['required', 'string', 'max:255'],
            'address.city' => ['required', 'string', 'max:255'],
            'address.state' => ['required', 'string', 'max:255'],
            'address.postal_code' => ['required', 'string', new PostalCode()],
        ]);

        /** @var \App\Models\User $user */
        $user = $request->user();

        $clinic = $createClinic->handle($user->account_id, $data);

        return new JsonResponse(
            ClinicData::from($clinic),
            JsonResponse::HTTP_CREATED
        );
    }

    /**
     * Get the given clinic.
     */
    public function show(Clinic $clinic): JsonResponse
    {
        $this->authorize('view', $clinic);

        $clinic->load(['billingAddress', 'shippingAddress', 'vendors']);

        return ClinicResource::make($clinic)->response();
    }

    /**
     * Update the given clinic.
     */
    public function update(
        Request $request,
        UpdateClinic $action,
        Clinic $clinic,
    ): JsonResponse {
        $this->authorize('update', $clinic);

        $data = $request->validate([
            'name' => ['sometimes', 'string', 'max:255'],
            'business_tax_id' => ['sometimes', 'string', new Ein()],
            'phone_number' => ['sometimes', 'string', 'max:255'],
            'address' => ['sometimes', 'array'],
            'address.street' => ['sometimes', 'string', 'max:255', 'filled'],
            'address.city' => ['sometimes', 'string', 'max:255', 'filled'],
            'address.state' => ['sometimes', 'string', 'max:255', 'filled'],
            'address.postal_code' => ['sometimes', 'string', new PostalCode(), 'filled'],

            'fulltime_dvm_count' => ['sometimes', 'integer', 'min:1'],
            'exam_rooms_count' => ['sometimes', 'integer', 'min:1'],
            'species_focus' => ['sometimes', 'string', 'in:'.implode(',', SpecieFocus::values())],
            'practice_types' => ['sometimes', 'array'],
            'practice_types.*' => ['string', 'in:'.implode(',', PracticeType::values())],

            'primary_shopping_preference' => ['sometimes', 'string', 'in:'.implode(',', ShoppingPreference::values())],
            'secondary_shopping_preferences' => ['sometimes', 'array'],
            'secondary_shopping_preferences.*' => ['string', 'in:'.implode(',', ShoppingPreference::values())],
            'primary_distributor_id' => ['sometimes', 'nullable', 'uuid', new ValidVendorType(VendorType::Distributor->value)],
            'secondary_distributor_id' => ['sometimes', 'nullable', 'uuid',  new ValidVendorType(VendorType::Distributor->value)],
            'preferred_manufacturer_ids' => ['sometimes', 'nullable', 'array', new ValidVendorType(VendorType::Manufacturer->value)],

        ]);

        $clinic = $action->handle($clinic, $data);

        $clinic->load(['billingAddress', 'shippingAddress']);

        return ClinicResource::make($clinic)->response();
    }
}
