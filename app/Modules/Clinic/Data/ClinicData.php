<?php

declare(strict_types=1);

namespace App\Modules\Clinic\Data;

use App\Models\Clinic;
use Spatie\LaravelData\Data;

final class ClinicData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly string $businessTaxId,
        public readonly string $phoneNumber,
        public readonly AddressData $address,
    ) {}

    public static function fromModel(Clinic $clinic): self
    {
        return self::from([
            'id' => $clinic->id,
            'name' => $clinic->name,
            'businessTaxId' => $clinic->business_tax_id,
            'phoneNumber' => $clinic->phone_number,
            'address' => AddressData::from($clinic->billingAddress),
        ]);
    }
}
