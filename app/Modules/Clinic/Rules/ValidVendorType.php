<?php

declare(strict_types=1);

namespace App\Modules\Clinic\Rules;

use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Contracts\Validation\Rule;

final class ValidVendorType implements Rule
{
    private string $type;

    public function __construct(string $type)
    {
        $this->type = $type;
    }

    public function passes($attribute, $value)
    {
        if (is_array($value)) {
            return fn ($value) => $this->passes($attribute, $value);
        }

        $connection = IntegrationConnection::with('vendor')->where('vendor_id', $value)->first();
        if (! $connection) {
            return false;
        }

        return $connection && $connection->vendor && $connection->vendor->type->value === $this->type;
    }

    public function message()
    {
        return "The selected :attribute must be a valid {$this->type} entry.";
    }
}
