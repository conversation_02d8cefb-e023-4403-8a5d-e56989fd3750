<?php

declare(strict_types=1);

namespace App\Modules\Clinic\Actions;

use App\Enums\AddressType;
use App\Models\Clinic;
use App\Modules\Clinic\Data\ClinicData;
use Illuminate\Support\Facades\DB;

final class CreateClinic
{
    public function handle(string $accountId, array $data): ClinicData
    {
        return DB::transaction(function () use ($data, $accountId) {
            $address = $data['address'];
            unset($data['address']);

            $clinic = Clinic::query()->create([
                'clinic_account_id' => $accountId,
                ...$data,
            ]);

            $clinic->billingAddress()->create(['type' => AddressType::Billing, ...$address]);
            $clinic->shippingAddress()->create(['type' => AddressType::Shipping, ...$address]);

            return ClinicData::from($clinic);
        });
    }
}
