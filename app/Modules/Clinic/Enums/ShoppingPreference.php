<?php

declare(strict_types=1);

namespace App\Modules\Clinic\Enums;

use App\Enums\EnumMethods;

enum ShoppingPreference: string
{
    use EnumMethods;

    case BrandLoyalty = 'brand_loyalty';
    case BestValueForMoney = 'best_value_for_money';
    case ClinicalOutcomeFocused = 'clinical_outcome_focused';
    case CostFirstDecisionMaker = 'cost_first_decision_maker';
    case ConvenienceDriven = 'convenience_driven';
    case DistributorLoyalty = 'distributor_loyalty';
    case GpoProductsPreferred = 'gpo_products_preferred';
    case IncentiveMotivated = 'incentive_motivated';
    case Innovation = 'innovation';
    case ProductFamiliarity = 'product_familiarity';
    case RelationshipBasedPurchasing = 'relationship_based_purchasing';
    case WillingToTryNewSuppliers = 'willing_to_try_new_suppliers';
    case CorporatePurchasingGuidelines = 'corporate_purchasing_guidelines';

    public static function toOptionsWithLabels(): array
    {
        return array_combine(
            array_map(fn (self $point) => $point->value, self::cases()),
            array_map(fn (self $point) => $point->label(), self::cases())
        );
    }

    public function label(): string
    {
        return match ($this) {
            self::BrandLoyalty => 'Brand Loyalty',
            self::BestValueForMoney => 'Best Value for Money',
            self::ClinicalOutcomeFocused => 'Clinical Outcome Focused',
            self::CostFirstDecisionMaker => 'Cost First Decision Maker',
            self::ConvenienceDriven => 'Convenience Driven',
            self::DistributorLoyalty => 'Distributor Loyalty',
            self::GpoProductsPreferred => 'GPO Products Preferred',
            self::IncentiveMotivated => 'Incentive Motivated',
            self::Innovation => 'Innovation',
            self::ProductFamiliarity => 'Product Familiarity',
            self::RelationshipBasedPurchasing => 'Relationship Based Purchasing',
            self::WillingToTryNewSuppliers => 'Willing to Try New Suppliers',
            self::CorporatePurchasingGuidelines => 'Corporate Purchasing Guidelines',
        };
    }
}
