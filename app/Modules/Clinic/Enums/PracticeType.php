<?php

declare(strict_types=1);

namespace App\Modules\Clinic\Enums;

use App\Enums\EnumMethods;

enum PracticeType: string
{
    use EnumMethods;

    case General = 'general';
    case Mobile = 'mobile';
    case EmergencyAndUrgentCare = 'emergency_and_urgent_care';
    case SpecialtyReferral = 'specialty_referral';
    case LargeAnimal = 'large_animal';
    case MixedAnimal = 'mixed_animal';
    case ExoticAvianAndZoo = 'exotic_avian_and_zoo';
    case Academic = 'academic';
    case Laboratory = 'laboratory';
    case HolisticAndIntegrative = 'holistic_and_integrative';

    public static function toOptionsWithLabels(): array
    {
        return array_combine(
            array_map(fn (self $point) => $point->value, self::cases()),
            array_map(fn (self $point) => $point->label(), self::cases())
        );
    }

    public function label(): string
    {
        return match ($this) {
            self::General => 'General',
            self::Mobile => 'Mobile',
            self::EmergencyAndUrgentCare => 'Emergency and Urgent Care',
            self::SpecialtyReferral => 'Specialty Referral',
            self::LargeAnimal => 'Large Animal',
            self::MixedAnimal => 'Mixed Animal',
            self::ExoticAvianAndZoo => 'Exotic, Avian and Zoo',
            self::Academic => 'Academic',
            self::Laboratory => 'Laboratory',
            self::HolisticAndIntegrative => 'Holistic and Integrative',
        };
    }
}
