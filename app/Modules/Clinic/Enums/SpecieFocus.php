<?php

declare(strict_types=1);

namespace App\Modules\Clinic\Enums;

use App\Enums\EnumMethods;

enum SpecieFocus: string
{
    use EnumMethods;

    case Canine = 'canine';
    case Feline = 'feline';
    case Equine = 'equine';
    case ProductionOrLargeAnimal = 'production_or_large_animal';
    case Exotics = 'exotics';
    case Avian = 'avian';
    case Marine = 'marine';

    public static function toOptionsWithLabels(): array
    {
        return array_combine(
            array_map(fn (self $point) => $point->value, self::cases()),
            array_map(fn (self $point) => $point->label(), self::cases())
        );
    }

    public function label(): string
    {
        return match ($this) {
            self::Canine => 'Canine',
            self::Feline => 'Feline',
            self::Equine => 'Equine',
            self::ProductionOrLargeAnimal => 'Production or Large Animal',
            self::Exotics => 'Exotics',
            self::Avian => 'Avian',
            self::Marine => 'Marine',
        };
    }
}
