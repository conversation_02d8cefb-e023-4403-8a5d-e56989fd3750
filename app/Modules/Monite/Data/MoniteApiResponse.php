<?php

declare(strict_types=1);

namespace App\Modules\Monite\Data;

use Illuminate\Http\Client\Response;

final class MoniteApiResponse
{
    public function __construct(
        public readonly array $data,
        public readonly array $meta,
        public readonly int $statusCode,
        public readonly array $headers = []
    ) {}

    public static function fromResponse(Response $response): self
    {
        $json = $response->json();

        // Extract pagination tokens from Monite API response (at root level)
        $meta = [];
        if (isset($json['next_pagination_token'])) {
            $meta['next_pagination_token'] = $json['next_pagination_token'];
        }
        if (isset($json['prev_pagination_token'])) {
            $meta['prev_pagination_token'] = $json['prev_pagination_token'];
        }

        return new self(
            data: $json['data'] ?? $json,
            meta: $meta,
            statusCode: $response->status(),
            headers: $response->headers()
        );
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function getMeta(): array
    {
        return $this->meta;
    }

    public function getPagination(): ?array
    {
        return [
            'next_pagination_token' => $this->meta['next_pagination_token'] ?? null,
            'prev_pagination_token' => $this->meta['prev_pagination_token'] ?? null,
        ];
    }

    public function hasNextPage(): bool
    {
        return ! empty($this->meta['next_pagination_token']);
    }

    public function hasPrevPage(): bool
    {
        return ! empty($this->meta['prev_pagination_token']);
    }

    public function getNextPaginationToken(): ?string
    {
        return $this->meta['next_pagination_token'] ?? null;
    }

    public function getPrevPaginationToken(): ?string
    {
        return $this->meta['prev_pagination_token'] ?? null;
    }

    public function isSuccessful(): bool
    {
        return $this->statusCode >= 200 && $this->statusCode < 300;
    }
}
