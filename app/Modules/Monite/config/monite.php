<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | Monite API Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Monite API integration including environments,
    | authentication settings, and API endpoints.
    |
    */

    'environment' => env('MONITE_ENVIRONMENT', 'sandbox'),

    'environments' => [
        'sandbox' => [
            'partner_portal_url' => 'https://portal.sandbox.monite.com',
            'api_base_url' => 'https://api.sandbox.monite.com',
        ],
        'production' => [
            'partner_portal_url' => 'https://us.portal.monite.com',
            'api_base_url' => 'https://us.api.monite.com',
        ],
    ],

    'api_version' => env('MONITE_API_VERSION', '2023-09-01'),

    'credentials' => [
        'client_id' => env('MONITE_CLIENT_ID'),
        'client_secret' => env('MONITE_CLIENT_SECRET'),
    ],

    'token' => [
        'cache_key' => 'monite_access_token',
        'cache_ttl' => 1800, // 30 minutes in seconds
        'refresh_threshold' => 300, // 5 minutes before expiry
    ],

    'rate_limiting' => [
        'requests_per_minute' => env('MONITE_RATE_LIMIT_RPM', 60),
        'burst_limit' => env('MONITE_RATE_LIMIT_BURST', 100),
    ],

    'timeout' => env('MONITE_API_TIMEOUT', 30),

    'retry' => [
        'max_attempts' => env('MONITE_RETRY_MAX_ATTEMPTS', 3),
        'delay' => env('MONITE_RETRY_DELAY', 1000), // milliseconds
        'backoff_multiplier' => env('MONITE_RETRY_BACKOFF', 2),
    ],

];
