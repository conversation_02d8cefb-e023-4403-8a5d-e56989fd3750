<?php

declare(strict_types=1);

namespace App\Modules\Monite\Contracts;

use App\Modules\Monite\Enums\MoniteGrantType;
use Illuminate\Http\Client\Response;

interface MoniteApiClientInterface
{
    public function get(string $endpoint, array $query = []): Response;

    public function post(string $endpoint, array $data = []): Response;

    public function put(string $endpoint, array $data = []): Response;

    public function patch(string $endpoint, array $data = []): Response;

    public function delete(string $endpoint): Response;

    public function withEntityId(string $entityId): self;

    public function withGrantType(MoniteGrantType $grantType): self;
}
