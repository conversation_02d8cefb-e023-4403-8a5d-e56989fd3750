<?php

declare(strict_types=1);

namespace App\Modules\Monite\Enums;

enum MoniteGrantType: string
{
    case ClientCredentials = 'client_credentials';
    case EntityUser = 'entity_user';

    public function getDescription(): string
    {
        return match ($this) {
            self::ClientCredentials => 'Partner-level token for backend-to-backend communication',
            self::EntityUser => 'Entity-user token for user-specific operations',
        };
    }
}
