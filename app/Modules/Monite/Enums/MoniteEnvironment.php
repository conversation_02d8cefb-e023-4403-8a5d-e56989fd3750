<?php

declare(strict_types=1);

namespace App\Modules\Monite\Enums;

enum MoniteEnvironment: string
{
    case Sandbox = 'sandbox';
    case Production = 'production';

    public function getApiBaseUrl(): string
    {
        return match ($this) {
            self::Sandbox => 'https://api.sandbox.monite.com',
            self::Production => 'https://us.api.monite.com',
        };
    }

    public function getPartnerPortalUrl(): string
    {
        return match ($this) {
            self::Sandbox => 'https://portal.sandbox.monite.com',
            self::Production => 'https://us.portal.monite.com',
        };
    }

    public function isProduction(): bool
    {
        return $this === self::Production;
    }
}
