<?php

declare(strict_types=1);

namespace App\Modules\Monite\Providers;

use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Services\MoniteApiClient;
use App\Modules\Monite\Services\MoniteTokenManager;
use Exception;
use Illuminate\Support\ServiceProvider;

final class MoniteServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->mergeConfigFrom(__DIR__.'/../config/monite.php', 'monite');

        // Register token manager
        $this->app->singleton(MoniteTokenManager::class, function ($app) {
            $config = config('monite');
            $environment = $config['environment'];
            $environments = $config['environments'][$environment];

            $clientId = $config['credentials']['client_id'];
            $clientSecret = $config['credentials']['client_secret'];

            if (! $clientId || ! $clientSecret) {
                throw new Exception(
                    'Monite credentials not configured. Please set MONITE_CLIENT_ID and MONITE_CLIENT_SECRET in your .env file.'
                );
            }

            return new MoniteTokenManager(
                clientId: $clientId,
                clientSecret: $clientSecret,
                apiBaseUrl: $environments['api_base_url'],
                apiVersion: $config['api_version']
            );
        });

        // Register API client
        $this->app->singleton(MoniteApiClient::class, function ($app) {
            $config = config('monite');
            $environment = $config['environment'];
            $environments = $config['environments'][$environment];

            return new MoniteApiClient(
                tokenManager: $app->make(MoniteTokenManager::class),
                apiBaseUrl: $environments['api_base_url'],
                apiVersion: $config['api_version']
            );
        });

        // Bind interface to implementation
        $this->app->bind(MoniteApiClientInterface::class, MoniteApiClient::class);
    }

    public function boot(): void {}
}
