<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Enums\MoniteGrantType;
use App\Modules\Monite\Exceptions\MoniteApiException;
use Exception;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

final class MoniteApiClient implements MoniteApiClientInterface
{
    private const MAX_RETRIES = 3;

    private const RETRY_DELAY = 1000; // milliseconds

    private const BACKOFF_MULTIPLIER = 2;

    public function __construct(
        private readonly MoniteTokenManager $tokenManager,
        private readonly string $apiBaseUrl,
        private readonly string $apiVersion,
        private readonly ?string $entityId = null
    ) {}

    public function get(string $endpoint, array $query = []): Response
    {
        return $this->request('GET', $endpoint, query: $query);
    }

    public function post(string $endpoint, array $data = []): Response
    {
        return $this->request('POST', $endpoint, data: $data);
    }

    public function put(string $endpoint, array $data = []): Response
    {
        return $this->request('PUT', $endpoint, data: $data);
    }

    public function patch(string $endpoint, array $data = []): Response
    {
        return $this->request('PATCH', $endpoint, data: $data);
    }

    public function delete(string $endpoint): Response
    {
        return $this->request('DELETE', $endpoint);
    }

    public function withEntityId(string $entityId): self
    {
        return new self($this->tokenManager, $this->apiBaseUrl, $this->apiVersion, $entityId);
    }

    public function withGrantType(MoniteGrantType $grantType): self
    {
        // Create a new instance with the same configuration but different grant type
        return new self($this->tokenManager, $this->apiBaseUrl, $this->apiVersion, $this->entityId);
    }

    private function request(
        string $method,
        string $endpoint,
        array $data = [],
        array $query = [],
        int $retryCount = 0
    ): Response {
        $url = $this->buildUrl($endpoint, $query);

        try {
            $response = $this->createHttpClient()
                ->$method($url, $data);

            // Handle rate limiting
            if ($response->status() === 429 && $retryCount < self::MAX_RETRIES) {
                $delay = $this->calculateRetryDelay($retryCount);
                Log::warning('Monite API rate limit hit, retrying', [
                    'retry_count' => $retryCount + 1,
                    'delay_ms' => $delay,
                    'endpoint' => $endpoint,
                ]);

                usleep($delay * 1000); // Convert to microseconds

                return $this->request($method, $endpoint, $data, $query, $retryCount + 1);
            }

            // Handle authentication errors
            if ($response->status() === 401) {
                // Clear cached tokens and retry once
                if ($retryCount === 0) {
                    $this->tokenManager->clearCachedTokens();
                    Log::info('Monite API authentication failed, clearing tokens and retrying');

                    return $this->request($method, $endpoint, $data, $query, $retryCount + 1);
                }

                throw MoniteApiException::authenticationFailed();
            }

            // Handle other errors
            if (! $response->successful()) {
                throw MoniteApiException::fromResponse($response);
            }

            return $response;

        } catch (MoniteApiException $e) {
            throw $e;
        } catch (Exception $e) {
            Log::error('Monite API request failed', [
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
                'retry_count' => $retryCount,
            ]);

            // Retry on network errors
            if ($retryCount < self::MAX_RETRIES && $this->isRetryableError($e)) {
                $delay = $this->calculateRetryDelay($retryCount);
                usleep($delay * 1000);

                return $this->request($method, $endpoint, $data, $query, $retryCount + 1);
            }

            throw new MoniteApiException(
                "Request failed: {$e->getMessage()}",
                0,
                $e
            );
        }
    }

    private function createHttpClient(): PendingRequest
    {
        $headers = [
            'X-Monite-Version' => $this->apiVersion,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];

        // Add entity ID header if provided
        if ($this->entityId) {
            $headers['x-monite-entity-id'] = $this->entityId;
        }

        return Http::withHeaders($headers)
            ->withToken($this->tokenManager->getAccessToken())
            ->timeout(config('monite.timeout', 30))
            ->retry(
                config('monite.retry.max_attempts', 3),
                config('monite.retry.delay', 1000)
            );
    }

    private function buildUrl(string $endpoint, array $query = []): string
    {
        $url = "{$this->apiBaseUrl}/v1{$endpoint}";

        if (! empty($query)) {
            $url .= '?'.http_build_query($query);
        }

        return $url;
    }

    private function calculateRetryDelay(int $retryCount): int
    {
        return self::RETRY_DELAY * (self::BACKOFF_MULTIPLIER ** $retryCount);
    }

    private function isRetryableError(Exception $e): bool
    {
        $retryableErrors = [
            'cURL error 28', // Timeout
            'cURL error 6',  // Could not resolve host
            'cURL error 7',  // Failed to connect
            'cURL error 35', // SSL connect error
        ];

        foreach ($retryableErrors as $error) {
            if (str_contains($e->getMessage(), $error)) {
                return true;
            }
        }

        return false;
    }
}
