<?php

declare(strict_types=1);

namespace App\Modules\Product\Nova;

use App\Nova\Resource;
use Laravel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class ProductBrand extends Resource
{
    public static $model = \App\Modules\Product\Models\ProductBrand::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Manufacturer', 'manufacturer', ProductManufacturer::class)
                ->nullable()
                ->searchable(),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            HasMany::make('Mappings', 'mappings', ProductBrandMapping::class),
        ];
    }
}
