<?php

declare(strict_types=1);

namespace App\Modules\Product\Nova;

use App\Nova\Resource;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

final class ProductManufacturerMapping extends Resource
{
    public static $model = \App\Modules\Product\Models\ProductManufacturerMapping::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Manufacturer', 'manufacturer', ProductManufacturer::class)
                ->searchable()
                ->rules('required'),

            Text::make('Name')
                ->rules('required', 'max:255'),
        ];
    }
}
