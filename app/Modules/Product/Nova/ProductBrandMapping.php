<?php

declare(strict_types=1);

namespace App\Modules\Product\Nova;

use App\Nova\Resource;
use Laravel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

final class ProductBrandMapping extends Resource
{
    public static $model = \App\Modules\Product\Models\ProductBrandMapping::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Brand', 'brand', ProductBrand::class)
                ->searchable(),

            Text::make('Name')
                ->rules('required', 'max:255'),
        ];
    }
}
