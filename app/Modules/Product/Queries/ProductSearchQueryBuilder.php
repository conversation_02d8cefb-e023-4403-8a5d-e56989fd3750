<?php

declare(strict_types=1);

namespace App\Modules\Product\Queries;

use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\AllowedSort;
use <PERSON>tie\QueryBuilder\QueryBuilder;

final class ProductSearchQueryBuilder extends QueryBuilder
{
    public function __construct(Builder $query, array $productIds)
    {
        parent::__construct($query);

        $this->allowedSorts([
            AllowedSort::callback('relevancy', function ($query) use ($productIds) {
                return $query->orderByRaw('ARRAY_POSITION(ARRAY['.implode(',', array_map(fn ($id) => "'$id'", $productIds)).']::uuid[], id::uuid)');
            }),
        ]);

        $this->defaultSort(
            AllowedSort::callback('relevancy', function ($query) use ($productIds) {
                return $query->orderByRaw('ARRAY_POSITION(ARRAY['.implode(',', array_map(fn ($id) => "'$id'", $productIds)).']::uuid[], id::uuid)');
            }),
        );
    }
}
