<?php

declare(strict_types=1);

namespace App\Modules\Product\Commands;

use App\Modules\Product\Actions\CleanUpStaleProducts;
use Illuminate\Console\Command;

final class CleanUpStale extends Command
{
    protected $signature = 'product:clean-up-stale {--days=7} {--force}';

    protected $description = 'Clean up stale products';

    public function handle(CleanUpStaleProducts $action): void
    {
        $this->info('Cleaning up stale products');

        $count = $action->handle((int) $this->option('days'), (bool) $this->option('force'));

        $this->info("{$count} stale products have been deactivated");
    }
}
