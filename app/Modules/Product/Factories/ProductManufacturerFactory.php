<?php

declare(strict_types=1);

namespace App\Modules\Product\Factories;

use App\Modules\Product\Models\ProductManufacturer;
use Illuminate\Database\Eloquent\Factories\Factory;

final class ProductManufacturerFactory extends Factory
{
    protected $model = ProductManufacturer::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
        ];
    }
}
