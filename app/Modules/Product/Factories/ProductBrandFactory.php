<?php

declare(strict_types=1);

namespace App\Modules\Product\Factories;

use App\Modules\Product\Models\ProductBrand;
use App\Modules\Product\Models\ProductManufacturer;
use Illuminate\Database\Eloquent\Factories\Factory;

final class ProductBrandFactory extends Factory
{
    protected $model = ProductBrand::class;

    public function definition(): array
    {
        return [
            'product_manufacturer_id' => ProductManufacturer::factory(),
            'name' => $this->faker->company(),
        ];
    }
}
