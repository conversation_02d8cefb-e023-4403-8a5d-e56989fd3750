<?php

declare(strict_types=1);

namespace App\Modules\Product\Providers;

use App\Modules\Product\Commands\CleanUpStale;
use App\Modules\Product\Nova\ProductBrand;
use App\Modules\Product\Nova\ProductBrandMapping;
use App\Modules\Product\Nova\ProductManufacturer;
use App\Modules\Product\Nova\ProductManufacturerMapping;
use Illuminate\Support\ServiceProvider;
use Laravel\Nova\Nova;

final class ProductServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->registerNovaResources();

        $this->registerCommands();
    }

    private function registerNovaResources(): void
    {
        Nova::resources([
            ProductBrand::class,
            ProductBrandMapping::class,
            ProductManufacturer::class,
            ProductManufacturerMapping::class,
        ]);
    }

    private function registerCommands(): void
    {
        $this->commands([
            CleanUpStale::class,
        ]);
    }
}
