<?php

declare(strict_types=1);

namespace App\Modules\Product\Actions;

use App\Enums\ProductStockStatus;
use App\Models\ProductOffer;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Spatie\SlackAlerts\Facades\SlackAlert;

final class CleanUpStaleProducts
{
    public function __construct(private int $alertThreshold = 1000) {}

    public function handle(int $days, bool $force = false): int
    {
        $query = ProductOffer::query()->stale($days);
        $count = $query->count();

        if ($force === false && $count > $this->alertThreshold) {
            SlackAlert::message("Found *{$count} stale products* - operation skipped for safety. To proceed anyway, re-run it manually with the `--force` flag.");

            return 0;
        }

        DB::table('clinic_product_offer')->whereIn('product_offer_id', $query->pluck('id'))->delete();

        return $query->update([
            'deactivated_at' => Carbon::now(),
            'stock_status' => ProductStockStatus::Discontinued,
        ]);
    }
}
