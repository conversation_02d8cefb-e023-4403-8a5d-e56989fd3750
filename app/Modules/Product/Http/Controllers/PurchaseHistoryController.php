<?php

declare(strict_types=1);

namespace App\Modules\Product\Http\Controllers;

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Modules\Order\Actions\GetProductPurchaseHistory;
use Illuminate\Http\JsonResponse;

final class PurchaseHistoryController
{
    public function show(
        Clinic $clinic,
        ProductOffer $productOffer,
        GetProductPurchaseHistory $getProductPurchaseHistory,
    ): JsonResponse {
        $data = $getProductPurchaseHistory($clinic->id, $productOffer->id);

        return response()->json($data);
    }
}
