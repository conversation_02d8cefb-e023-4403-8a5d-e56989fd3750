<?php

declare(strict_types=1);

namespace App\Modules\Product\Models;

use App\Models\Product;
use App\Modules\Product\Factories\ProductManufacturerFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class ProductManufacturer extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = [];

    public static function boot(): void
    {
        parent::boot();

        self::created(function (ProductManufacturer $manufacturer) {
            $manufacturer->mappings()->create(['name' => $manufacturer->name]);
        });
    }

    public static function newFactory(): ProductManufacturerFactory
    {
        return ProductManufacturerFactory::new();
    }

    public function mappings(): HasMany
    {
        return $this->hasMany(ProductManufacturerMapping::class);
    }

    public function brands(): HasMany
    {
        return $this->hasMany(ProductBrand::class, 'product_manufacturer_id');
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }
}
