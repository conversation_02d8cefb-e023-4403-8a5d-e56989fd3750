<?php

declare(strict_types=1);

namespace App\Modules\Product\Models;

use App\Models\Product;
use App\Modules\Product\Factories\ProductBrandFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class ProductBrand extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = [];

    public static function boot(): void
    {
        parent::boot();

        self::created(function (ProductBrand $brand) {
            $brand->mappings()->create(['name' => $brand->name]);
        });
    }

    public static function newFactory(): ProductBrandFactory
    {
        return ProductBrandFactory::new();
    }

    public function manufacturer(): BelongsTo
    {
        return $this->belongsTo(ProductManufacturer::class, 'product_manufacturer_id');
    }

    public function mappings(): HasMany
    {
        return $this->hasMany(ProductBrandMapping::class);
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'product_brand_id');
    }
}
