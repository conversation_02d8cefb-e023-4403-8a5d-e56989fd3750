<?php

declare(strict_types=1);

namespace App\Modules\Product\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class ProductManufacturerMapping extends Model
{
    use HasUuids;

    protected $guarded = [];

    public function manufacturer(): BelongsTo
    {
        return $this->belongsTo(ProductManufacturer::class, 'product_manufacturer_id');
    }
}
