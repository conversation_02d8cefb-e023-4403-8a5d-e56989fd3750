<?php

declare(strict_types=1);

namespace App\Modules\Cart\Data;

use <PERSON><PERSON>\LaravelData\Data;

final class CartItemData extends Data
{
    public function __construct(
        public string $product_offer_id,
        public int $quantity,
        public ?string $notes = null
    ) {}

    public static function fromArray(array $data): self
    {
        return new self(
            product_offer_id: $data['product_offer_id'],
            quantity: $data['quantity'],
            notes: $data['notes'] ?? null
        );
    }

    public function toArray(): array
    {
        return [
            'product_offer_id' => $this->product_offer_id,
            'quantity' => $this->quantity,
            'notes' => $this->notes,
        ];
    }
}
