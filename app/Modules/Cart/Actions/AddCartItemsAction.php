<?php

declare(strict_types=1);

namespace App\Modules\Cart\Actions;

use App\Models\BudgetSummary;
use App\Models\Clinic;
use App\Modules\Cart\Data\CartItemData;
use App\Modules\Cart\Services\ProductOfferQueryService;
use Illuminate\Support\Facades\DB;

final class AddCartItemsAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction,
        private readonly ProductOfferQueryService $productOfferQueryService
    ) {}

    public function handle(Clinic $clinic, array $items): void
    {
        // Convert array items to CartItemData objects for better type safety
        $cartItems = collect($items)->map(fn (array $item) => CartItemData::fromArray($item));

        // Pre-load all product offers in a single optimized query
        $productOfferIds = $cartItems->pluck('product_offer_id')->unique()->values()->toArray();
        $productOffers = $this->productOfferQueryService->getProductOffersForCart($clinic, $productOfferIds);

        DB::transaction(function () use ($cartItems, $clinic, $productOffers, $productOfferIds) {
            $cart = $this->getCartAction->handle($clinic);

            foreach ($cartItems as $cartItem) {
                $productOffer = $productOffers->get($cartItem->product_offer_id);

                if (! $productOffer) {
                    continue; // Skip if product offer not found or not accessible
                }

                if ($cartItem->quantity === 0) {
                    $cart->items()->where('product_offer_id', $productOffer->id)->delete();

                    continue;
                }

                $cart->addItem($productOffer, $cartItem->quantity, null, $cartItem->notes);
            }

            // Clear caches after modifications
            $this->getCartAction->forgetCache($clinic);
            $this->productOfferQueryService->clearProductOfferCache($clinic, $productOfferIds);
            BudgetSummary::clearCache($clinic);
        });
    }
}
