<?php

declare(strict_types=1);

namespace App\Modules\Cart\Actions;

use App\Models\Cart;
use App\Models\Clinic;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;

final class GetCartAction
{
    private const CACHE_TTL = 300; // 5 minutes

    public function handle(Clinic $clinic): Cart
    {
        $cacheKey = "clinic_cart_{$clinic->id}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($clinic) {
            $applyProductOfferConditions = function ($query) use ($clinic) {
                return $query->withVendorConnected([$clinic->id])
                    ->withClinic($clinic->id)
                    ->withLastClinicOrderItem($clinic->id)
                    ->withClinicCartItem($clinic->id)
                    ->whereVendorIdIn($clinic->vendors->pluck('id')->unique()->values()->toArray())
                    ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id));
            };

            return $clinic->cart()
                ->with([
                    'items.productOffer' => function (BelongsTo $query) use ($applyProductOfferConditions) {
                        return $query->with([
                            'vendor',
                            'product' => fn ($query) => $query->with([
                                'productOffers' => $applyProductOfferConditions,
                            ]),
                        ])->tap($applyProductOfferConditions);
                    },
                    'items.vendor',
                ])
                ->firstOrCreate();
        });
    }

    /**
     * Get cart with minimal data for lightweight responses.
     */
    public function handleLightweight(Clinic $clinic): Cart
    {
        $cacheKey = "clinic_cart_lightweight_{$clinic->id}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($clinic) {
            return $clinic->cart()
                ->with(['items' => fn ($query) => $query->select(['id', 'cart_id', 'quantity', 'subtotal', 'notes'])])
                ->firstOrCreate();
        });
    }

    public function forgetCache(Clinic $clinic): void
    {
        Cache::forget("clinic_cart_{$clinic->id}");
        Cache::forget("clinic_cart_lightweight_{$clinic->id}");
    }
}
