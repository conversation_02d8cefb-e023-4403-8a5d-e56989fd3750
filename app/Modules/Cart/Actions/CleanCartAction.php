<?php

declare(strict_types=1);

namespace App\Modules\Cart\Actions;

use App\Models\BudgetSummary;
use App\Models\Clinic;
use Illuminate\Support\Facades\DB;

final class CleanCartAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction
    ) {}

    public function handle(Clinic $clinic): void
    {
        DB::transaction(function () use ($clinic) {
            $cart = $this->getCartAction->handle($clinic);
            $cart->clean();

            // Clear caches after cleaning
            $this->getCartAction->forgetCache($clinic);
            BudgetSummary::clearCache($clinic);
        });
    }
}
