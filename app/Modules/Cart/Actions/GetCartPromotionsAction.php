<?php

declare(strict_types=1);

namespace App\Modules\Cart\Actions;

use App\Models\Clinic;
use App\Modules\Promotion\Services\CartPromotionEligibilityService;
use Illuminate\Support\Facades\Cache;

final class GetCartPromotionsAction
{
    private const CACHE_TTL = 60; // 1 minute for promotions

    public function __construct(
        private readonly GetCartAction $getCartAction,
        private readonly CartPromotionEligibilityService $promotionEligibilityService
    ) {}

    public function handle(Clinic $clinic): array
    {
        $cacheKey = "clinic_cart_promotions_{$clinic->id}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($clinic) {
            $cart = $this->getCartAction->handle($clinic);

            return $this->promotionEligibilityService->checkCartEligibility($cart);
        });
    }

    public function forgetCache(Clinic $clinic): void
    {
        Cache::forget("clinic_cart_promotions_{$clinic->id}");
    }
}
