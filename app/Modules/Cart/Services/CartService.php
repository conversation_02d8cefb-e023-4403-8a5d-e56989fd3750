<?php

declare(strict_types=1);

namespace App\Modules\Cart\Services;

use App\Models\BudgetSummary;
use App\Models\Cart;
use App\Models\Clinic;
use App\Modules\Cart\Actions\AddCartItemsAction;
use App\Modules\Cart\Actions\CleanCartAction;
use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\Cart\Actions\GetCartPromotionsAction;
use App\Modules\Cart\Data\CartItemData;

final class CartService
{
    public function __construct(
        private readonly GetCartAction $getCartAction,
        private readonly AddCartItemsAction $addCartItemsAction,
        private readonly CleanCartAction $cleanCartAction,
        private readonly GetCartPromotionsAction $getCartPromotionsAction,
        private readonly ProductOfferQueryService $productOfferQueryService
    ) {}

    /**
     * Get the clinic's cart with optimized loading.
     */
    public function getCart(Clinic $clinic): Cart
    {
        return $this->getCartAction->handle($clinic);
    }

    /**
     * Add items to the clinic's cart with lightweight response.
     */
    public function addItems(Clinic $clinic, array $items): Cart
    {
        $this->addCartItemsAction->handle($clinic, $items);

        return $this->getCartAction->handleLightweight($clinic);
    }

    /**
     * Update an item in the clinic's cart.
     */
    public function updateItem(Clinic $clinic, string $productOfferId, int $quantity, ?string $notes = null): Cart
    {
        $cartItem = new CartItemData($productOfferId, $quantity, $notes);
        $this->addCartItemsAction->handle($clinic, [$cartItem->toArray()]);

        return $this->getCartAction->handleLightweight($clinic);
    }

    /**
     * Remove an item from the clinic's cart.
     */
    public function removeItem(Clinic $clinic, string $productOfferId): Cart
    {
        $cartItem = new CartItemData($productOfferId, 0);
        $this->addCartItemsAction->handle($clinic, [$cartItem->toArray()]);

        return $this->getCartAction->handleLightweight($clinic);
    }

    /**
     * Clean the clinic's cart.
     */
    public function cleanCart(Clinic $clinic): Cart
    {
        $this->cleanCartAction->handle($clinic);

        return $this->getCartAction->handleLightweight($clinic);
    }

    /**
     * Get promotion eligibility for the clinic's cart.
     */
    public function getPromotions(Clinic $clinic): array
    {
        return $this->getCartPromotionsAction->handle($clinic);
    }

    /**
     * Get cart summary with optimized queries.
     */
    public function getCartSummary(Clinic $clinic): array
    {
        $cart = $this->getCart($clinic);

        return [
            'total_items' => $cart->total_items,
            'total_price' => $cart->total_price,
            'unique_items' => $cart->unique_items_count,
            'prices_have_changed' => $cart->prices_have_changed,
            'items_count' => $cart->items->count(),
        ];
    }

    /**
     * Clear all caches related to the clinic's cart.
     */
    public function clearAllCaches(Clinic $clinic): void
    {
        $this->getCartAction->forgetCache($clinic);
        $this->getCartPromotionsAction->forgetCache($clinic);
        BudgetSummary::clearCache($clinic);
    }
}
