<?php

declare(strict_types=1);

namespace App\Modules\Cart\Services;

use App\Models\Clinic;
use App\Models\ProductOffer;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

final class ProductOfferQueryService
{
    private const CACHE_TTL = 300; // 5 minutes

    /**
     * Get multiple product offers with optimized loading for cart operations.
     */
    public function getProductOffersForCart(Clinic $clinic, array $productOfferIds): Collection
    {
        $cacheKey = "clinic_product_offers_{$clinic->id}_".md5(implode(',', $productOfferIds));

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($clinic, $productOfferIds) {
            $vendorIds = $clinic->vendors->pluck('id')->unique()->values()->toArray();

            return ProductOffer::with('vendor')
                ->withVendorConnected([$clinic->id])
                ->withClinic($clinic->id)
                ->withLastClinicOrderItem($clinic->id)
                ->withClinicCartItem($clinic->id)
                ->whereVendorIdIn($vendorIds)
                ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id))
                ->whereIn('id', $productOfferIds)
                ->get()
                ->keyBy('id');
        });
    }

    /**
     * Get a single product offer with optimized loading.
     */
    public function getProductOfferForCart(Clinic $clinic, string $productOfferId): ?ProductOffer
    {
        $cacheKey = "clinic_product_offer_{$clinic->id}_{$productOfferId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($clinic, $productOfferId) {
            $vendorIds = $clinic->vendors->pluck('id')->unique()->values()->toArray();

            return ProductOffer::with('vendor')
                ->withVendorConnected([$clinic->id])
                ->withClinic($clinic->id)
                ->withLastClinicOrderItem($clinic->id)
                ->withClinicCartItem($clinic->id)
                ->whereVendorIdIn($vendorIds)
                ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id))
                ->find($productOfferId);
        });
    }

    /**
     * Clear cache for specific product offers.
     */
    public function clearProductOfferCache(Clinic $clinic, array $productOfferIds = []): void
    {
        if (empty($productOfferIds)) {
            // For now, we'll rely on cache expiration for clearing all caches
            // In a production environment, you might want to implement pattern-based clearing
            return;
        }

        // Clear specific product offer caches
        foreach ($productOfferIds as $productOfferId) {
            Cache::forget("clinic_product_offer_{$clinic->id}_{$productOfferId}");
        }

        // Clear batch cache
        $cacheKey = "clinic_product_offers_{$clinic->id}_".md5(implode(',', $productOfferIds));
        Cache::forget($cacheKey);
    }
}
