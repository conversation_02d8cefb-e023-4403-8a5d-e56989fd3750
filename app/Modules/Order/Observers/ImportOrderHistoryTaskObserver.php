<?php

declare(strict_types=1);

namespace App\Modules\Order\Observers;

use App\Modules\Order\Jobs\Import\ProcessImportedOrderHistoryCsvFile;
use App\Modules\Order\Models\ImportOrderHistoryTask;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

final class ImportOrderHistoryTaskObserver
{
    public function created(ImportOrderHistoryTask $task): void
    {
        if ($task->isPending()) {
            Log::info('Dispatching import order history task job', [
                'task_id' => $task->id,
            ]);

            ProcessImportedOrderHistoryCsvFile::dispatch($task);
        }
    }

    public function deleted(ImportOrderHistoryTask $task): void
    {
        try {
            Storage::disk('local')->delete($task->file_path);

            Log::info('Cleaned up CSV file after task deletion', [
                'task_id' => $task->id,
                'file_path' => $task->file_path,
            ]);
        } catch (Exception $e) {
            Log::warning('Failed to clean up CSV file after task deletion', [
                'task_id' => $task->id,
                'file_path' => $task->file_path,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
