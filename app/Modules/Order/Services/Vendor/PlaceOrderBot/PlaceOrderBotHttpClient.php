<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\PlaceOrderBot;

use App\Modules\Order\Services\Vendor\Http\BaseHttpClient;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

final class PlaceOrderBotHttpClient extends BaseHttpClient
{
    public function __construct(

    ) {
        parent::__construct();
        $this->configureClient();
    }

    protected function configureClient(): void
    {
        $this->client = Http::withBasicAuth(config('highfive.place_order_bot.username'), config('highfive.place_order_bot.password'))->withOptions(
            [
                'base_uri' => config('highfive.place_order_bot.base_uri'),
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
            ]
        )
            ->retry(3, function (int $attempt, Exception $exception) {
                Log::error(
                    'PlaceOrderBotHttpClient retry attempt',
                    [
                        'attempt' => $attempt,
                        'exception' => $exception->getMessage(),
                    ]
                );

                return $attempt * 1000;
            });
    }
}
