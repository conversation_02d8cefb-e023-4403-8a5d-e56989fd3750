<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Patterson;

use App\Models\SubOrder;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Enums\IntegrationSessionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Order\Services\Vendor\Contracts\OrderProcessor;
use App\Modules\Order\Services\Vendor\Contracts\OrderReconciler;
use App\Modules\Order\Services\Vendor\Contracts\OrderSynchronizer;
use App\Modules\Order\Services\Vendor\Contracts\ShipmentSynchronizer;
use Exception;
use Illuminate\Support\Facades\Log;

final class PattersonService implements OrderProcessor, OrderReconciler, OrderSynchronizer, ShipmentSynchronizer
{
    private PattersonHttpClient $client;

    public function __construct()
    {
        $this->client = new PattersonHttpClient(
            config('highfive.patterson.username'),
            config('highfive.patterson.password'),
        );
    }

    public function processOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::PlaceOrders,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);
        try {
            $addressID = $integrationConnection->credentials['address_id'];
            $sharedSecret = $integrationConnection->credentials['shared_secret'];

            $order = $suborder->order;
            $clinic = $order->clinic;
            $shippingAddress = $order->shippingAddress;
            $orderNumber = $order->order_number;
            $orderDate = now()->toIso8601String();
            $total = number_format($suborder->items->sum(fn ($item) => $item->total_price / 100), 2, '.', '');
            $clinicName = $clinic->name;

            $itemsXml = $suborder->items->map(function ($item, $idx) {
                $lineNumber = $idx + 1;
                $quantity = $item->quantity;
                $vendorId = $item->productOffer->external_id;
                $vendorSku = $item->productOffer->vendor_sku;
                $title = $item->productOffer->name;
                $unitPrice = number_format($item->price / 100, 2, '.', '');

                // @TODO: after testing we can check which fields are required and remove the ones that are not (eg. Classification, UnitOfMeasure, etc.)
                return <<<XML
                <ItemOut quantity="{$quantity}" lineNumber="{$lineNumber}">
                    <ItemID>
                        <SupplierPartID>{$vendorId}</SupplierPartID>
                    </ItemID>
                    <ItemDetail>
                        <UnitPrice>
                            <Money currency="USD">{$unitPrice}</Money>
                        </UnitPrice>
                        <Description xml:lang="en">{$title}</Description>
                        <UnitOfMeasure>EA</UnitOfMeasure>
                        <Classification domain="UNSPSC">{$vendorSku}</Classification>
                    </ItemDetail>
                </ItemOut>
                XML;
            })->implode("\n");

            $cxml = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<cXML payloadID="{$orderNumber}@highfive.vet" timestamp="{$orderDate}" version="1.2.008">
    <Header>
        <From>
            <Credential domain="NetworkID">
                <Identity>CURO</Identity>
            </Credential>
        </From>
        <To>
            <Credential domain="NetworkID">
                <Identity>PATTERSONVET</Identity>
            </Credential>
        </To>
        <Sender>
            <Credential domain="NetworkID">
                <Identity>HIGHFIVE</Identity>
                <SharedSecret>{$sharedSecret}</SharedSecret>
            </Credential>
            <UserAgent>HighFive 1.0</UserAgent>
        </Sender>
    </Header>
    <Request>
        <OrderRequest>
            <OrderRequestHeader orderID="{$orderNumber}" orderDate="{$orderDate}" type="new">
                <Total>
                    <Money currency="USD">{$total}</Money>
                </Total>
                <ShipTo>
                    <Address addressID="{$addressID}">
                        <Name xml:lang="en">{$clinicName}</Name>
                        <PostalAddress>
                            <DeliverTo>{$clinicName}</DeliverTo>
                            <Street>{$shippingAddress->street}</Street>
                            <City>{$shippingAddress->city}</City>
                            <State>{$shippingAddress->state}</State>
                            <PostalCode>{$shippingAddress->postal_code}</PostalCode>
                            <Country isoCountryCode="US">US</Country>
                        </PostalAddress>
                    </Address>
                </ShipTo>
            </OrderRequestHeader>
            {$itemsXml}
        </OrderRequest>
    </Request>
</cXML>
XML;

            Log::info($cxml);

            $response = $this->client->placeOrder($cxml, $session, 'place_order', $suborder);
            $session->success();
        } catch (Exception $e) {
            $session->failed();
            throw $e;
        }
    }

    public function syncOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        // TODO: Implement Patterson order sync
    }

    public function reconcileOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        // TODO: Implement Patterson order reconciliation
    }

    public function syncShipment(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        // TODO: Implement Patterson shipment sync
    }

    public function validateOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        // TODO: Implement Patterson order validation
    }

    // Future: Implement processOrder, syncOrder, syncShipment, reconcileOrder, etc.
}
