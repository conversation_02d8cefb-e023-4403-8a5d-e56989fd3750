<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Patterson;

use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Services\Vendor\Http\BaseHttpClient;
use App\Modules\Order\Services\Vendor\Responses\VendorResponse;
use Exception;
use Illuminate\Support\Facades\Http;
use SensitiveParameter;

final class PattersonHttpClient extends BaseHttpClient
{
    public function __construct(
        private readonly string $username,
        #[SensitiveParameter]
        private readonly string $password,
    ) {
        parent::__construct();
        $this->configureClient();
    }

    public function placeOrder(string $cxml,
        IntegrationSession $session,
        string $integrationEventAction,
        $subject
    ): VendorResponse {
        return $this->post(
            '/',
            $cxml,
            $session,
            $integrationEventAction,
            $subject
        );
    }

    protected function configureClient(): void
    {
        $baseUri = config('highfive.patterson.base_uri');

        $this->client = Http::withBasicAuth($this->username, $this->password)
            ->withOptions([
                'base_uri' => $baseUri,
                'headers' => [
                    'Accept' => 'application/xml',
                    'Content-Type' => 'application/xml',
                ],
            ])
            ->retry(3, function (int $attempt, Exception $exception) {
                return $attempt * 1000;
            });
    }

    // Future: Add methods for sending cXML OrderRequest, etc.
}
