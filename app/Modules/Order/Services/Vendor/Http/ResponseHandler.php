<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Http;

use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Services\Vendor\Exceptions\ApiAuthenticationException;
use App\Modules\Order\Services\Vendor\Exceptions\ApiException;
use App\Modules\Order\Services\Vendor\Exceptions\ApiValidationException;
use App\Modules\Order\Services\Vendor\Responses\VendorResponse;
use Illuminate\Http\Client\Response;
use Throwable;

final class ResponseHandler
{
    /**
     * Handle the vendor API response and log events accordingly.
     */
    public function handle(
        Response $response,
        IntegrationSession $session,
        string $integrationEventType,
        $subject,
    ): VendorResponse {
        $url = $response->transferStats?->getRequest()?->getUri();
        $apiPath = $url ? $url->getPath() : null;
        $method = $response->transferStats?->getRequest()?->getMethod() ?? null;
        $errorMessage = "$integrationEventType $method to {$apiPath} failed";

        // If the response is successful, log the event and return the data
        if ($response->successful()) {
            $data = $this->extractData($response);
            $payloadResponse = $this->buildPayloadResponse($response, $data);
            $session->logSuccessEvent(
                $integrationEventType,
                $subject,
                [
                    'endpoint' => $apiPath,
                    'method' => $method,
                    'response' => $payloadResponse,
                ],
            );

            return VendorResponse::success($data);
        }

        // If the response is not successful, log the error event and throw an exception
        $errorContext = $this->buildErrorContext($response);
        $session->logErrorEvent(
            $integrationEventType,
            $subject,
            [
                'endpoint' => $apiPath,
                'method' => $method,
                'response' => $errorContext,
            ],
        );

        // Throw the appropriate exception based on the response status
        throw match ($response->status()) {
            401 => new ApiAuthenticationException($errorMessage, $errorContext),
            400, 422 => new ApiValidationException($errorMessage, $errorContext),
            default => new ApiException($errorMessage, $errorContext),
        };
    }

    /**
     * Handle transport/runtime exceptions that occur before an HTTP Response is available.
     */
    public function handleException(
        Throwable $exception,
        IntegrationSession $session,
        string $integrationEventType,
        $subject,
        ?string $endpoint = null,
        ?string $method = null,
    ): never {
        $errorMessage = "$integrationEventType $method to {$endpoint} failed";

        $errorContext = [
            'exception' => get_class($exception),
            'message' => $exception->getMessage(),
        ];

        $session->logErrorEvent(
            $integrationEventType,
            $subject,
            [
                'endpoint' => $endpoint,
                'method' => $method,
                'response' => $errorContext,
            ],
        );

        throw new ApiException($errorMessage, $errorContext);
    }

    /**
     * Execute the HTTP callable and route the outcome through the normal handlers.
     */
    public function handleCall(
        callable $invoke,
        IntegrationSession $session,
        string $integrationEventType,
        $subject,
        ?string $endpoint = null,
        ?string $method = null,
    ): VendorResponse {
        try {
            $response = $invoke();

            return $this->handle($response, $session, $integrationEventType, $subject);
        } catch (Throwable $e) {
            $this->handleException($e, $session, $integrationEventType, $subject, $endpoint, $method);
        }
    }

    /**
     * Extract data from the response based on content type.
     */
    private function extractData(Response $response): mixed
    {
        $contentType = $response->getHeader('content-type')[0] ?? '';

        return str_contains($contentType, 'application/json') ? $response->json() : $response->body();
    }

    /**
     * Build a payload response for logging.
     */
    private function buildPayloadResponse(Response $response, mixed $data): ?array
    {
        $contentType = $response->getHeader('content-type')[0] ?? '';
        if (str_contains($contentType, 'application/json') || str_contains($contentType, 'xml')) {
            return is_array($data) ? $data : ['data' => $data];
        }

        return [
            'content_type' => $contentType,
            'length' => is_string($data) ? mb_strlen($data) : null,
            'note' => 'Binary data omitted from log',
        ];
    }

    /**
     * Build error context for logging and exception.
     */
    private function buildErrorContext(Response $response): array
    {
        return [
            'status' => $response->status(),
            'reason' => $response->reason(),
            'body' => $response->json() ?: $response->body(),
        ];
    }
}
