<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Http;

use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Services\Vendor\Responses\VendorResponse;
use Illuminate\Http\Client\PendingRequest;

abstract class BaseHttpClient
{
    protected PendingRequest $client;

    protected ResponseHandler $responseHandler;

    public function __construct()
    {
        $this->responseHandler = app(ResponseHandler::class);
    }

    abstract protected function configureClient(): void;

    final public function post(
        string $endpoint,
        mixed $data,
        IntegrationSession $session,
        string $integrationEventAction,
        $subject
    ): VendorResponse {
        return $this->send('POST', $endpoint, $data, $session, $integrationEventAction, $subject, fn () => $this->client->post($endpoint, $data));
    }

    final public function get(
        string $endpoint,
        mixed $query,
        IntegrationSession $session,
        string $integrationEventAction,
        $subject
    ): VendorResponse {
        return $this->send('GET', $endpoint, $query, $session, $integrationEventAction, $subject, fn () => $this->client->get($endpoint, $query));
    }

    final public function put(
        string $endpoint,
        mixed $data,
        IntegrationSession $session,
        string $integrationEventAction,
        $subject
    ): VendorResponse {
        return $this->send('PUT', $endpoint, $data, $session, $integrationEventAction, $subject, fn () => $this->client->put($endpoint, $data));
    }

    final public function delete(
        string $endpoint,
        mixed $data,
        IntegrationSession $session,
        string $integrationEventAction,
        $subject
    ): VendorResponse {
        return $this->send('DELETE', $endpoint, $data, $session, $integrationEventAction, $subject, fn () => $this->client->delete($endpoint, $data));
    }

    final public function patch(
        string $endpoint,
        mixed $data,
        IntegrationSession $session,
        string $integrationEventAction,
        $subject
    ): VendorResponse {
        return $this->send('PATCH', $endpoint, $data, $session, $integrationEventAction, $subject, fn () => $this->client->patch($endpoint, $data));
    }

    final public function asJson(): self
    {
        $this->client->asJson();

        return $this;
    }

    final public function contentType(string $contentType): self
    {
        $this->client->contentType($contentType);

        return $this;
    }

    final public function accept(string $contentType): self
    {
        $this->client->accept($contentType);

        return $this;
    }

    final public function headers(array $headers): self
    {
        $this->client->withHeaders($headers);

        return $this;
    }

    /**
     * Execute an HTTP call, ensuring consistent logging and error handling.
     */
    private function send(
        string $method,
        string $endpoint,
        mixed $payload,
        IntegrationSession $session,
        string $integrationEventAction,
        $subject,
        callable $invoke
    ): VendorResponse {
        $session->logSuccessEvent(
            $integrationEventAction.'_initiated',
            $subject,
            [
                'endpoint' => $endpoint,
                'method' => $method,
                'body' => $payload,
            ]
        );

        return $this->responseHandler->handleCall(
            $invoke,
            $session,
            $integrationEventAction,
            $subject,
            $endpoint,
            $method,
        );
    }
}
