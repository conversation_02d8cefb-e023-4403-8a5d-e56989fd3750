<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor;

use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Order\Services\Vendor\Amazon\AmazonService;
use App\Modules\Order\Services\Vendor\GerVetUSA\GerVetUSAService;
use App\Modules\Order\Services\Vendor\Mwi\MwiService;
use App\Modules\Order\Services\Vendor\PlaceOrderBot\PlaceOrderBotService;

final class Factory
{
    /**
     * Make a vendor service instance.
     */
    public static function make(IntegrationConnection $connection)
    {
        return match ($connection->vendor->key) {
            'ZOET' => app(PlaceOrderBotService::class),
            'PATN' => app(PlaceOrderBotService::class),
            'COVR' => app(PlaceOrderBotService::class),
            'RYCN' => app(PlaceOrderBotService::class),
            'RYCN' => app(PlaceOrderBotService::class),
            'HPNU' => app(PlaceOrderBotService::class),
            'BOIH' => app(PlaceOrderBotService::class),

            'AMZN' => app(AmazonService::class, [
                'refreshToken' => $connection->credentials['refresh_token'],
                'buyingGroupId' => $connection->credentials['buying_group_id'],
            ]),

            'MWIX' => app(MwiService::class, [
                'username' => $connection->credentials['username'],
                'password' => $connection->credentials['password'],
            ]),

            'GERVET' => app(GerVetUSAService::class, [
                'apiKey' => $connection->credentials['api_key'],
            ]),

            // @TODO: Uncomment this when integration is ready to use on production
            // 'PATN' => app(PattersonService::class),

            default => app(Fallback::class),
        };
    }
}
