<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Mwi;

use App\Enums\OrderItemStatus;
use App\Exceptions\VendorServiceError;
use App\Models\OrderItem;
use App\Models\SubOrder;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Enums\IntegrationSessionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Models\ExternalOrder;
use App\Modules\Order\Services\Vendor\Contracts\InvoiceSynchronizer;
use App\Modules\Order\Services\Vendor\Contracts\OrderProcessor;
use App\Modules\Order\Services\Vendor\Contracts\OrderReconciler;
use App\Modules\Order\Services\Vendor\Contracts\OrderSynchronizer;
use App\Modules\Order\Services\Vendor\Contracts\ShipmentSynchronizer;
use App\Modules\Order\Services\Vendor\Exceptions\OrderValidationException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use SensitiveParameter;
use Throwable;

final class MwiService implements InvoiceSynchronizer, OrderProcessor, OrderReconciler, OrderSynchronizer, ShipmentSynchronizer
{
    private MwiHttpClient $client;

    /**
     * Create a new MWI instance.
     */
    public function __construct(
        private readonly string $username,
        #[SensitiveParameter]
        private readonly string $password,
    ) {
        $this->client = new MwiHttpClient(
            $this->username,
            $this->password,
        );
    }

    /**
     * Place an order with the vendor.
     *
     * @throws VendorServiceError
     */
    public function processOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::PlaceOrders,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);
        try {
            $orderNumber = $suborder->order->order_number;

            $mwiCartId = $this->createNewCart($suborder, $session);

            $suborder->items->each(fn (OrderItem $item, int $index) => $this->addItemToCart($mwiCartId, $index + 1, $item, $suborder, $session));

            $this->updateCart($mwiCartId, $orderNumber, $suborder, $session);

            // Skip order validation for MWI - ignore any exceptions
            try {
                $this->validateCart($mwiCartId, $suborder, $session);
            } catch (Throwable $e) {
                // Ignore validation errors and continue with order placement
                Log::info('MWI order validation skipped', [
                    'suborder_id' => $suborder->id,
                    'cart_id' => $mwiCartId,
                    'validation_error' => $e->getMessage(),
                ]);
            }

            $this->recalculateShipmentGroup($mwiCartId, $suborder, $session);

            $this->getAvailableShipmentGroup($mwiCartId, $suborder, $session);

            $this->submitCart($mwiCartId, $suborder, $session);

            // update the suborder status to placed
            $suborder->update([
                'error_code' => null,
                'error_message' => null,
            ]);

            // update the items status to placed
            $suborder->items()->update([
                'status' => OrderItemStatus::Accepted,
                'error_message' => null,
            ]);

            // update the session status to succeeded
            $session->success();

        } catch (Throwable $e) {
            // update the session status to failed
            $session->failed();
            throw $e;
        }
    }

    public function syncOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::SyncOrderStatus,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);
        try {
            $salesOrders = $this->getSalesOrdersByOrderNumber($suborder->order->order_number, $suborder, $session);

            foreach ($salesOrders as $salesOrder) {
                $salesOrderDetails = $this->getSalesOrderDetails($salesOrder['salesOrderNumber'], $suborder, $session);
                $shippingFee = isset($salesOrderDetails['freightAmount'])
                    ? (int) round(((float) $salesOrderDetails['freightAmount']) * 100)
                    : null;

                $suborder->externalOrders()->updateOrCreate([
                    'external_order_id' => $salesOrderDetails['salesOrderNumber'],
                ], [
                    'status' => $salesOrderDetails['status'] ?? 'PENDING',
                    'shipping_fee' => $shippingFee,
                ]
                );

                foreach ($salesOrderDetails['salesOrderLines'] as $salesOrderLine) {
                    $status = match ($salesOrderLine['status']) {
                        '20' => OrderItemStatus::Accepted,
                        '30', '45' => OrderItemStatus::Accepted,
                        '60' => OrderItemStatus::Delivered,
                        '0' => OrderItemStatus::Backordered,
                        default => null,
                    };

                    if (is_null($status)) {
                        Log::warning('Unknown MWI order status', [
                            'status' => $salesOrderLine['status'],
                            'sales_order_number' => $salesOrder['salesOrderNumber'],
                            'suborder_id' => $suborder->id,
                        ]);

                        continue;
                    }

                    $suborder->items()
                        ->whereRelation('productOffer', 'external_id', $salesOrderLine['itemId'])
                        ->update(['status' => $status->value]);
                }
            }
            // update the session status to succeeded
            $session->success();
        } catch (Throwable $e) {
            // update the session status to failed
            $session->failed();
            throw $e;
        }
    }

    public function syncShipment(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::SyncOrderShipments,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);

        try {
            $invoices = $this->getInvoicesByOrderNumber($suborder->order->order_number, $suborder, $session);
            foreach ($invoices as $invoice) {
                $invoiceDetails = $this->getInvoiceDetails($invoice['invoiceNumber'], $suborder, $session);
                foreach ($invoiceDetails['packages'] as $package) {
                    $shipment = $suborder->shipments()->updateOrCreate(
                        [
                            'tracking_number' => $package['trackingNumber'],
                        ],
                        [
                            'carrier' => $package['carrier'],
                        ]
                    );

                    $items = collect($package['packageLines'])->map(function ($packageLine) use ($suborder) {
                        return $suborder->items()->whereRelation('productOffer', 'external_id', $packageLine['itemId'])->first();
                    })->filter();

                    $shipment->items()->sync($items);
                }
            }
            // update the session status to succeeded
            $session->success();
        } catch (Throwable $e) {
            // update the session status to failed
            $session->failed();
            throw $e;
        }
    }

    /**
     * Sync invoice data from MWI API.
     */
    public function syncInvoice(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::SyncOrderInvoices,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);
        try {
            $invoices = $this->getInvoicesByOrderNumber($suborder->order->order_number, $suborder, $session);

            foreach ($invoices as $invoice) {
                $invoiceDetails = $this->getInvoiceDetails($invoice['invoiceNumber'], $suborder, $session);

                // Find the external order for this invoice
                $externalOrder = $suborder->externalOrders()
                    ->where('external_order_id', $invoiceDetails['salesOrderNumber'])
                    ->first();

                if (! $externalOrder) {
                    continue;
                }

                // Download and save the invoice PDF
                $this->downloadInvoicePdf($externalOrder, $invoiceDetails['invoiceNumber'], $session);
            }
            $session->success();
        } catch (Throwable $e) {
            $session->failed();
            throw $e;
        }
    }

    public function reconcileOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::ReconcileOrderLines,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);
        try {
            $salesOrders = $this->getSalesOrdersByOrderNumber($suborder->order->order_number, $suborder, $session);

            foreach ($salesOrders as $salesOrder) {
                $salesOrderDetails = $this->getSalesOrderDetails($salesOrder['salesOrderNumber'], $suborder, $session);

                foreach ($salesOrderDetails['salesOrderLines'] as $lineItem) {
                    $item = $suborder->items->firstWhere('productOffer.external_id', $lineItem['itemId']);

                    if (! $item) {
                        Log::warning('Item not found in suborder', [
                            'sales_order_number' => $salesOrder['salesOrderNumber'],
                            'suborder_id' => $suborder->id,
                            'sku' => $lineItem['mwiSku'],
                            'external_id' => $lineItem['itemId'],
                        ]);

                        continue;
                    }

                    $item->update([
                        'price' => (int) round((float) $lineItem['unitPrice'] * 100),
                        'tax_fee' => (int) round((float) $lineItem['lineTaxAmount'] * 100),
                        'quantity' => (int) $lineItem['quantityOrdered'],
                    ]);
                }
            }
            $session->success();
        } catch (Throwable $e) {
            $session->failed();
            throw $e;
        }
    }

    public function validateOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        foreach ($suborder->items as $item) {
            if (empty($item->productOffer?->external_id)) {
                throw new OrderValidationException(
                    'Order item missing external_id',
                    [
                        'item_id' => $item->id,
                        'product_offer_id' => $item->product_offer_id ?? null,
                    ]
                );
            }
        }
    }

    /**
     * Download an invoice PDF and save it to storage.
     */
    private function downloadInvoicePdf(ExternalOrder $externalOrder, int $invoiceNumber, IntegrationSession $session): string
    {
        $response = $this->client
            ->accept('text/plain')
            ->get(
                "Invoice/{$invoiceNumber}/PDF",
                [],
                $session,
                'get_invoice_details',
                $externalOrder->subOrder
            );

        if (is_null($response->data)) {
            throw new VendorServiceError(
                'MWI response is missing invoice PDF',
                context: [
                    'invoice_number' => $invoiceNumber,
                    'external_order_id' => $externalOrder->id,
                ],
            );
        }

        $pdfContent = $response->data;
        $clinicId = $externalOrder->subOrder->order->clinic_id;
        $filePath = "{$clinicId}/invoices/{$externalOrder->id}.pdf";

        Storage::disk(self::INVOICE_STORAGE_DISK)->put($filePath, $pdfContent);

        $externalOrder->update([
            'invoice_file_path' => $filePath,
        ]);

        return $filePath;
    }

    private function getSalesOrderDetails(int $orderNumber, SubOrder $subOrder, IntegrationSession $session): array
    {
        return $this->client->get(
            "SalesOrder/{$orderNumber}",
            [],
            $session,
            'get_sales_order_details',
            $subOrder
        )->data;
    }

    /**
     * Create a new cart object.
     */
    private function createNewCart(SubOrder $subOrder, IntegrationSession $session): string
    {
        $accountId = $this->client->getAccountId($session, 'create_new_cart', $subOrder);
        $response = $this->client->put(
            'Cart',
            [
                'accountId' => $accountId,
            ],
            $session,
            'create_new_cart',
            $subOrder
        );

        if (is_null($response->data)) {
            throw new VendorServiceError(
                'MWI response is missing cart identifier',
                context: [
                    'request' => [
                        'accountId' => $accountId,
                    ],
                    'response' => $response,
                ],
            );
        }

        return $response->data;
    }

    /**
     * Add an item to the cart.
     */
    private function addItemToCart(string $mwiCartId, int $lineNumber, OrderItem $item, SubOrder $subOrder, IntegrationSession $session): void
    {
        $accountId = $this->client->getAccountId($session, 'add_item_to_cart', $subOrder);
        $this->client->put(
            'Cart/CartLine',
            [
                'cartId' => $mwiCartId,
                'itemId' => $item->productOffer->external_id,
                'quantity' => $item->quantity,
                'poLineNumber' => $lineNumber,
            ],
            $session,
            'add_item_to_cart',
            $subOrder
        );
    }

    /**
     * Update the cart.
     */
    private function updateCart(string $mwiCartId, string $orderNumber, SubOrder $subOrder, IntegrationSession $session): void
    {
        $accountId = $this->client->getAccountId($session, 'update_cart', $subOrder);
        $this->client
            ->asJson()
            ->contentType('application/merge-patch+json')
            ->patch(
                "Cart/{$mwiCartId}",
                ['poNumber' => $orderNumber],
                $session,
                'update_cart',
                $subOrder
            );
    }

    /**
     * Validate the cart.
     */
    private function validateCart(string $mwiCartId, SubOrder $suborder, IntegrationSession $session): void
    {
        $response = $this->client->get(
            "Cart/{$mwiCartId}/Validate",
            [],
            $session,
            'validate_cart',
            $suborder
        );

        if (! empty($response->data['validationMessages']) || ! empty($response->data['cartLineValidations'])) {
            // Update the items with validation errors
            collect($response->data['cartLineValidations'])
                ->each(function (array $line) use ($suborder) {
                    $suborder->items()->whereRelation('productOffer', 'external_id', $line['itemId'])
                        ->update([
                            'status' => OrderItemStatus::PlacementFailed,
                            'error_message' => $line['validationMessage'],
                        ]);
                });

            throw new VendorServiceError(
                'MWI response has validation errors',
                context: [
                    'cart_id' => $mwiCartId,
                    'response' => $response->data,
                ],
            );
        }
    }

    /**
     * Recalculate the shipment group.
     */
    private function recalculateShipmentGroup(string $mwiCartId, SubOrder $subOrder, IntegrationSession $session): void
    {
        $this->client->patch(
            'Cart/ShipmentGroup/Recalculate',
            ['cartId' => $mwiCartId],
            $session,
            'recalculate_shipment_group',
            $subOrder
        );
    }

    /**
     * Get the available shipment group.
     */
    private function getAvailableShipmentGroup(string $mwiCartId, SubOrder $subOrder, IntegrationSession $session): array
    {
        $response = $this->client->post(
            'Cart/ShipmentGroup',
            ['cartId' => $mwiCartId],
            $session,
            'get_available_shipment_group',
            $subOrder
        );

        if (is_null($response->data)) {
            throw new VendorServiceError(
                'MWI response is missing shipping information',
                context: [
                    'cart_id' => $mwiCartId,
                    'response' => $response->data,
                ],
            );
        }

        return $response->data;
    }

    /**
     * Submit the cart.
     */
    private function submitCart(string $mwiCartId, SubOrder $subOrder, IntegrationSession $session): void
    {
        $this->client->patch(
            "Cart/{$mwiCartId}/Submit",
            [],
            $session,
            'submit_cart',
            $subOrder
        );
    }

    private function getSalesOrdersByOrderNumber(string $orderNumber, SubOrder $subOrder, IntegrationSession $session): array
    {
        $accountId = $this->client->getAccountId($session, 'get_sales_orders_by_order_number', $subOrder);
        $response = $this->client->post(
            'SalesOrder',
            [
                'accountId' => $accountId,
                'poNumber' => $orderNumber,
            ],
            $session,
            'get_sales_orders_by_order_number',
            $subOrder
        );

        return $response->data;
    }

    private function getInvoiceDetails(int $invoiceId, SubOrder $subOrder, IntegrationSession $session): array
    {
        return $this->client->get(
            "Invoice/{$invoiceId}",
            [],
            $session,
            'get_invoice_details',
            $subOrder
        )->data;
    }

    private function getInvoicesByOrderNumber(mixed $orderNumber, SubOrder $subOrder, IntegrationSession $session): array
    {
        $accountId = $this->client->getAccountId($session, 'get_invoices_by_order_number', $subOrder);
        $response = $this->client->post(
            'Invoice',
            [
                'accountId' => $accountId,
                'poNumber' => $orderNumber,
            ],
            $session,
            'get_invoices_by_order_number',
            $subOrder
        );

        return $response->data;
    }
}
