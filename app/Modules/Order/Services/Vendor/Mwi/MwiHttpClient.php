<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Mwi;

use App\Exceptions\VendorServiceError;
use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Services\Vendor\Http\BaseHttpClient;
use Exception;
use Illuminate\Support\Facades\Http;
use SensitiveParameter;

final class MwiHttpClient extends BaseHttpClient
{
    private ?string $accountId = null;

    public function __construct(
        private readonly string $username,
        #[SensitiveParameter]
        private readonly string $password,
    ) {
        parent::__construct();
        $this->configureClient();
    }

    /**
     * Get the customer number.
     *
     * @throws VendorServiceError
     */
    public function getAccountId(IntegrationSession $session, string $integrationEventAction, $subject): string
    {
        if ($this->accountId !== null) {
            return $this->accountId;
        }

        $response = $this->post('Account', [], $session, $integrationEventAction, $subject);

        $this->accountId = $response->data[0]['accountId'];

        return $this->accountId;
    }

    protected function configureClient(): void
    {
        $this->client = Http::withBasicAuth($this->username, $this->password)
            ->withOptions(
                [
                    'base_uri' => config('highfive.mwi.base_uri'),
                    'headers' => [
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                    ],
                ]
            )
            ->retry(3, function (int $attempt, Exception $exception) {
                return $attempt * 1000;
            });
    }
}
