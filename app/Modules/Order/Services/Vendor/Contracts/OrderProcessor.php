<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Contracts;

use App\Models\SubOrder;
use App\Modules\Integration\Models\IntegrationConnection;

interface OrderProcessor
{
    public function processOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void;

    public function validateOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void;
}
