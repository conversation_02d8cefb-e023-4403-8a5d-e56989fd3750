<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Contracts;

use App\Models\SubOrder;
use App\Modules\Integration\Models\IntegrationConnection;

interface OrderReconciler
{
    /**
     * Reconcile order details from vendor API.
     * This method should fetch order data from the vendor API and update the order items
     * with the latest information including prices, quantities, and tax information.
     *
     * @throws \App\Exceptions\VendorServiceError
     */
    public function reconcileOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void;
}
