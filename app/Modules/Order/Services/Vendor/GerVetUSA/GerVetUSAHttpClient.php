<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\GerVetUSA;

use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Services\Vendor\Http\BaseHttpClient;
use App\Modules\Order\Services\Vendor\Responses\VendorResponse;
use Exception;
use Illuminate\Support\Facades\Http;
use SensitiveParameter;

final class GerVetUSAHttpClient extends BaseHttpClient
{
    public function __construct(
        #[SensitiveParameter]
        private readonly string $apiKey,
    ) {
        parent::__construct();
        $this->configureClient();
    }

    /**
     * Get products from GerVetUSA API.
     */
    public function getProducts(IntegrationSession $session, string $integrationEventAction, $subject): VendorResponse
    {
        return $this->get('/api/products', [], $session, $integrationEventAction, $subject);
    }

    /**
     * Submit an order to GerVetUSA API.
     */
    public function submitOrder(array $orderData, IntegrationSession $session, string $integrationEventAction, $subject): VendorResponse
    {
        return $this->post('/api/orders', $orderData, $session, $integrationEventAction, $subject);
    }

    /**
     * Get order status updates from GerVetUSA API.
     */
    public function getOrderStatus(string $orderId, IntegrationSession $session, string $integrationEventAction, $subject): VendorResponse
    {
        return $this->get('/api/order/status', ['order_id' => $orderId], $session, $integrationEventAction, $subject);
    }

    protected function configureClient(): void
    {
        $this->client = Http::withHeaders([
            'x-api-key' => $this->apiKey,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])
            ->withOptions([
                'base_uri' => config('highfive.gervetusa.base_uri'),
            ])
            ->retry(3, function (int $attempt, Exception $exception) {
                return $attempt * 1000;
            });
    }
}
