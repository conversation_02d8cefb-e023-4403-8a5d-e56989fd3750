<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\GerVetUSA;

use App\Models\SubOrder;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Order\Services\Vendor\Contracts\OrderProcessor;
use App\Modules\Order\Services\Vendor\Contracts\OrderReconciler;
use App\Modules\Order\Services\Vendor\Contracts\OrderSynchronizer;
use App\Modules\Order\Services\Vendor\Contracts\ShipmentSynchronizer;
use SensitiveParameter;

final class GerVetUSAService implements OrderProcessor, OrderReconciler, OrderSynchronizer, ShipmentSynchronizer
{
    private GerVetUSAHttpClient $client;

    /**
     * Create a new GerVetUSA instance.
     */
    public function __construct(
        #[SensitiveParameter]
        private readonly string $apiKey,
    ) {
        $this->client = new GerVetUSAHttpClient($this->apiKey);
    }

    /**
     * Place an order with the vendor.
     */
    public function processOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        // TODO: Implement GerVetUSA order processing
    }

    public function syncOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        // TODO: Implement GerVetUSA order synchronization
    }

    public function syncShipment(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        // TODO: Implement GerVetUSA shipment synchronization
    }

    public function reconcileOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        // TODO: Implement GerVetUSA order reconciliation
    }

    public function validateOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        // TODO: Implement GerVetUSA order validation
    }
}
