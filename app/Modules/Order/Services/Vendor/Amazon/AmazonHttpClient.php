<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Amazon;

use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Services\Vendor\Exceptions\ApiAuthenticationException;
use App\Modules\Order\Services\Vendor\Exceptions\ApiValidationException;
use App\Modules\Order\Services\Vendor\Http\BaseHttpClient;
use App\Modules\Order\Services\Vendor\Responses\VendorResponse;
use Exception;
use Illuminate\Support\Facades\Http;
use SensitiveParameter;

final class AmazonHttpClient extends BaseHttpClient
{
    private ?string $accessToken = null;

    public function __construct(
        #[SensitiveParameter]
        private readonly string $refreshToken,
    ) {
        parent::__construct();
        $this->configureClient();
    }

    /**
     * Place an order with Amazon.
     */
    public function placeOrder(array $orderData, IntegrationSession $session, string $integrationEventAction, $subject): VendorResponse
    {
        return $this->post('/ordering/2022-10-30/orders', $orderData, $session, $integrationEventAction, $subject);
    }

    /**
     * Get a short-lived access token.
     */
    public function getAccessToken(): string
    {
        if ($this->accessToken !== null) {
            return $this->accessToken;
        }

        $response = Http::asForm()
            ->acceptJson()
            ->post('https://api.amazon.com/auth/O2/token', [
                'grant_type' => 'refresh_token',
                'refresh_token' => $this->refreshToken,
                'client_id' => config('highfive.amazon.client_id'),
                'client_secret' => config('highfive.amazon.client_secret'),
            ]);

        if ($response->failed()) {
            throw new ApiAuthenticationException(
                'Failed to obtain Amazon Business API access token',
                [
                    'status' => $response->status(),
                    'reason' => $response->reason(),
                    'body' => $response->json() ?? $response->body(),
                ]
            );
        }

        $data = $response->json();
        $this->accessToken = $data['access_token'];

        return $this->accessToken;
    }

    /**
     * Get order by Amazon order ID.
     */
    public function getOrderByAmazonId(string $id, IntegrationSession $session, string $integrationEventAction, $subject): VendorResponse
    {
        return $this->get("/reports/2021-01-08/orders/{$id}", [
            'includeLineItems' => 'true',
            'includeShipments' => 'true',
            'includeCharges' => 'true',
        ],
            $session,
            $integrationEventAction,
            $subject
        );
    }

    /**
     * Get order by external ID and email (our order number).
     */
    public function getOrderByExternalId(string $externalId, string $email, IntegrationSession $session, string $integrationEventAction, $subject): VendorResponse
    {
        try {
            return $this
                ->headers([
                    'x-amz-user-email' => $email,
                ])
                ->get("/ordering/2022-10-30/orders/{$externalId}",
                    [],
                    $session,
                    $integrationEventAction,
                    $subject
                );
        } catch (ApiValidationException $e) {
            return VendorResponse::error($e->getMessage(), $e->context, $e->errorCode);
        }
    }

    /**
     * Get orders within a date range.
     *
     * @param  string  $startDate  Format: YYYY-MM-DD
     * @param  string  $endDate  Format: YYYY-MM-DD
     */
    public function getOrdersByDate(string $startDate, string $endDate, IntegrationSession $session, string $integrationEventAction, $subject): VendorResponse
    {
        return $this->get('/reports/2021-01-08/orders', [
            'startDate' => $startDate,
            'endDate' => $endDate,
        ],
            $session,
            $integrationEventAction,
            $subject
        );
    }

    protected function configureClient(): void
    {
        $this->client = Http::withOptions(
            [
                'base_uri' => config('highfive.amazon.base_uri'),
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'x-amz-access-token' => $this->getAccessToken(),
                ],
            ]
        )
            ->retry(3, function (int $attempt, Exception $exception) {
                return $attempt * 1000;
            });
    }
}
