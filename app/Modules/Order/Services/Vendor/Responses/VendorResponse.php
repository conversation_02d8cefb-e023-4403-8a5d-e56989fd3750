<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Responses;

final readonly class VendorResponse
{
    public function __construct(
        public bool $success,
        public mixed $data = null,
        public ?string $errorMessage = null,
        public array $context = [],
        public ?string $errorCode = null
    ) {}

    public static function success(mixed $data = null): self
    {
        return new self(true, $data);
    }

    public static function error(
        string $message,
        array $context = [],
        ?string $errorCode = null
    ): self {
        return new self(
            success: false,
            errorMessage: $message,
            context: $context,
            errorCode: $errorCode
        );
    }
}
