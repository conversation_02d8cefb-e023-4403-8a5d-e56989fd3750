<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Exceptions;

use RuntimeException;
use Throwable;

abstract class VendorException extends RuntimeException
{
    public function __construct(
        string $message,
        public readonly string $errorCode,
        public readonly array $context = [],
        ?Throwable $previous = null
    ) {
        parent::__construct($message, 0, $previous);
    }
}
