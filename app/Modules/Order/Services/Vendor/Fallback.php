<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor;

use App\Mail\OrderPlacedForVendor;
use App\Models\SubOrder;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Enums\IntegrationSessionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Order\Services\Vendor\Contracts\OrderProcessor;
use Illuminate\Support\Facades\Mail;
use Throwable;

final class Fallback implements OrderProcessor
{
    public function processOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::PlaceOrders,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);
        try {
            $to = $suborder->vendor->purchase_order_email;
            $mail = Mail::to($to);

            if ($to !== config('highfive.orders.bcc_email')) {
                $mail->bcc(config('highfive.orders.bcc_email'));
            }

            $mail->send(new OrderPlacedForVendor($suborder));

            $session->logSuccessEvent(
                'fallback_order_email_sent',
                $suborder,
                [
                    'to' => $to,
                    'bcc' => config('highfive.orders.bcc_email'),
                    'email' => OrderPlacedForVendor::class,
                ]
            );
            $session->success();
        } catch (Throwable $e) {
            $session->logErrorEvent(
                'fallback_order_email_failed',
                $suborder,
                [
                    'to' => $suborder->vendor->purchase_order_email,
                    'bcc' => config('highfive.orders.bcc_email'),
                    'error' => $e->getMessage(),
                    'email' => OrderPlacedForVendor::class,
                ]
            );
            $session->failed();
            throw $e;
        }
    }

    public function validateOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        // No validation needed for fallback orders (for now)
    }
}
