<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use App\Models\OrderPromotion;
use Spatie\LaravelData\Data;

final class OrderPromotionData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly string $type,
        public readonly ?VendorData $vendor,
        public readonly ?array $triggeringItems,
        public readonly ?array $appliedRules,
        public readonly ?array $appliedBenefits,
    ) {}

    public static function fromModel(OrderPromotion $orderPromotion): self
    {
        $vendor = $orderPromotion->promotion->vendor;
        $vendorData = $vendor ? new VendorData(
            id: $vendor->id,
            name: $vendor->name,
            imageUrl: asset("storage/{$vendor->image_path}"),
        ) : null;

        return self::from([
            'id' => $orderPromotion->id,
            'name' => $orderPromotion->promotion->name,
            'type' => $orderPromotion->promotion->type->value,
            'vendor' => $vendorData,
            'triggeringItems' => $orderPromotion->triggering_items,
            'appliedRules' => $orderPromotion->applied_rules,
            'appliedBenefits' => $orderPromotion->applied_benefits,
        ]);
    }
}
