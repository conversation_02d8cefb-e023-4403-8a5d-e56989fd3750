<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use App\Data\Transformers\MoneyTransformer;
use Brick\Money\Money;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;

#[MapInputName(SnakeCaseMapper::class)]
final class PurchaseHistoryDataPoint extends Data
{
    public function __construct(
        public readonly string $label,
        #[WithTransformer(MoneyTransformer::class)]
        public readonly Money $totalSpent,
        #[WithTransformer(MoneyTransformer::class)]
        public readonly Money $lowestUnitPrice,
        #[WithTransformer(MoneyTransformer::class)]
        public readonly Money $highestUnitPrice,
        public readonly int $totalOrders = 0,
        public readonly int $totalQuantity = 0,
        public readonly int $averageQuantityPerOrder = 0,
    ) {}
}
