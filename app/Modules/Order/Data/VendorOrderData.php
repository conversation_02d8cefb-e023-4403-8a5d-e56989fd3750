<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use App\Data\Casts\MoneyCast;
use App\Data\Transformers\MoneyTransformer;
use App\Models\SubOrder;
use Brick\Money\Money;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;

final class VendorOrderData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly VendorData $vendor,
        #[DataCollectionOf(OrderItemData::class)]
        public readonly Collection $items,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly Money $totalPrice,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly ?Money $totalTaxFee,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly ?Money $shippingFee,
    ) {}

    public static function fromModel(SubOrder $subOrder): self
    {
        return self::from([
            'id' => $subOrder->id,
            'vendor' => VendorData::from($subOrder->vendor),
            'items' => OrderItemData::collect($subOrder->items),
            'totalPrice' => Money::ofMinor($subOrder->total_price, 'USD'),
            'totalTaxFee' => $subOrder->total_tax_fee ? Money::ofMinor($subOrder->total_tax_fee, 'USD') : null,
            'shippingFee' => $subOrder->externalOrders->sum('shipping_fee') ? Money::ofMinor($subOrder->externalOrders->sum('shipping_fee'), 'USD') : null,
        ]);
    }
}
