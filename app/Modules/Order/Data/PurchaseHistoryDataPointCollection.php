<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Illuminate\Support\Collection;

final class PurchaseHistoryDataPointCollection extends Collection
{
    public function getTotalSpent(): BigDecimal
    {
        return array_reduce(
            $this->items,
            fn (Money $carry, PurchaseHistoryDataPoint $item) => $carry->plus($item->totalSpent),
            Money::ofMinor(0, 'USD')
        )->getAmount();
    }

    public function getLowestUnitPrice(): BigDecimal
    {
        if ($this->isEmpty()) {
            return Money::ofMinor(0, 'USD')->getAmount();
        }

        $prices = array_filter(
            array_map(
                fn (PurchaseHistoryDataPoint $item) => $item->lowestUnitPrice,
                array_filter($this->items, fn (PurchaseHistoryDataPoint $item) => $item->totalQuantity > 0)
            ),
            fn (Money $price) => $price->isPositive()
        );

        if (empty($prices)) {
            return Money::ofMinor(0, 'USD')->getAmount();
        }

        return array_reduce(
            $prices,
            fn (Money $carry, Money $price) => $price->isLessThan($carry) ? $price : $carry,
            reset($prices)
        )->getAmount();
    }

    public function getHighestUnitPrice(): BigDecimal
    {
        if ($this->isEmpty()) {
            return Money::ofMinor(0, 'USD')->getAmount();
        }

        $prices = array_filter(
            array_map(
                fn (PurchaseHistoryDataPoint $item) => $item->highestUnitPrice,
                array_filter($this->items, fn (PurchaseHistoryDataPoint $item) => $item->totalQuantity > 0)
            ),
            fn (Money $price) => $price->isPositive()
        );

        if (empty($prices)) {
            return Money::ofMinor(0, 'USD')->getAmount();
        }

        return array_reduce(
            $prices,
            fn (Money $carry, Money $price) => $price->isGreaterThan($carry) ? $price : $carry,
            reset($prices)
        )->getAmount();
    }

    public function getTotalOrders(): int
    {
        return $this->sum('totalOrders');
    }

    public function getTotalQuantity(): int
    {
        return $this->sum('totalQuantity');
    }

    public function getAverageQuantityPerOrder(): int
    {
        $totalOrders = $this->getTotalOrders();

        return $totalOrders > 0 ? (int) ceil($this->getTotalQuantity() / $totalOrders) : 0;
    }
}
