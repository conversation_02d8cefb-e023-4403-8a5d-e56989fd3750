<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;

final class PurchaseHistory extends Data
{
    public function __construct(
        public readonly string $product_offer_id,
        #[DataCollectionOf(PurchaseHistoryDataPoint::class)]
        public readonly PurchaseHistoryDataPointCollection $data
    ) {}

    public function with(): array
    {
        return [
            'summary' => [
                'totalSpent' => $this->data->getTotalSpent(),
                'lowestUnitPrice' => $this->data->getLowestUnitPrice(),
                'highestUnitPrice' => $this->data->getHighestUnitPrice(),
                'totalOrders' => $this->data->getTotalOrders(),
                'averageQuantityPerOrder' => $this->data->getAverageQuantityPerOrder(),
            ],
        ];
    }
}
