<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use App\Enums\ProductStockStatus;
use Carbon\Carbon;
use Spatie\LaravelData\Data;

final class PreviouslyOrderedItem extends Data
{
    public function __construct(
        public readonly string $product_offer_id,
        public readonly string $product_id,
        public readonly string $product_name,
        public readonly string $vendor_id,
        public readonly string $vendor_name,
        public readonly Carbon $last_ordered_at,
        public readonly int $quantity,
        public readonly int $price,
        public readonly string $image_url,
        public readonly int $order_count,
        public readonly ProductStockStatus $stock_status,
        public readonly int $increments
    ) {}
}
