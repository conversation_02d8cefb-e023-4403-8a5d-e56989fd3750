<?php

declare(strict_types=1);

namespace App\Modules\Order\Enums;

use App\Enums\EnumMethods;

enum VendorError: string
{
    use EnumMethods;

    case API_AUTH_ERROR = 'API_AUTH_ERROR';
    case API_VALIDATION_ERROR = 'API_VALIDATION_ERROR';
    case API_ERROR = 'API_ERROR';
    case VENDOR_ERROR = 'VENDOR_ERROR';
    case UNKNOWN_ERROR = 'UNKNOWN_ERROR';
    case ORDER_VALIDATION_ERROR = 'ORDER_VALIDATION_ERROR';

    /**
     * Returns a valid VendorError code or UNKNOWN_ERROR if not valid.
     */
    public static function safeValue($code): string
    {
        $value = $code instanceof self ? $code->value : (string) $code;

        return in_array($value, self::values(), true)
            ? $value
            : self::UNKNOWN_ERROR->value;
    }
}
