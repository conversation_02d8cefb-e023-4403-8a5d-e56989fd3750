<?php

declare(strict_types=1);

namespace App\Modules\Order\Actions;

use App\Enums\OrderItemStatus;
use App\Models\Clinic;
use App\Models\Order;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Order\Enums\ImportOrderItemVendorStatus;
use Exception;
use InvalidArgumentException;

final class ImportOrderItemCsvMapping
{
    public function mapTemplate(string $vendor, array $data): array
    {
        switch ($vendor) {
            case 'ZOET':
                return $this->zoetisTemplate($data);
            default:
                throw new InvalidArgumentException("Unsupported vendor: {$vendor}");
        }
    }

    public function mapHeaders(string $vendor): array
    {
        switch ($vendor) {
            case 'ZOET':
                return [
                    'vendor',
                    'orderNumber',
                    'reference',
                    'status',
                    'datePlaced',
                    'billToId',
                    'billToName',
                    'billToLines',
                    'deliveryId',
                    'deliveryName',
                    'deliveryLines',
                    'requestedDate',
                    'comments',
                    'productName',
                    'productSku',
                    'productPrice',
                    'productOrderType',
                    'productQuantity',
                    'productStatus',
                    'productTotal',
                    'subtotal',
                    'shipping',
                    'tax',
                    'total',
                ];
            default:
                throw new InvalidArgumentException("Unsupported vendor: {$vendor}");
        }
    }

    public function mapStatusToVendor(string $vendor, string $status): string
    {
        switch ($vendor) {
            case 'ZOET':
                return $this->statusZoetis($status);
            default:
                throw new InvalidArgumentException("Unsupported vendor: {$vendor}");
        }

    }

    public function handle(array $record, Vendor $vendor, Clinic $clinic): void
    {
        $mappedData = $this->mapTemplate($vendor->key, $record);

        $order = Order::where('clinic_id', $clinic->id)
            ->where('order_number', $mappedData['order_number'])
            ->first();

        if (is_null($order)) {
            throw new Exception("Could not find order with order number [{$mappedData['order_number']}] for clinic [{$clinic->id}]");
        }

        $productOffer = ProductOffer::where('vendor_sku', $mappedData['vendor_sku'])
            ->where('vendor_id', $vendor->id)
            ->first();
        $item = $order->items()->where('product_offer_id', $productOffer->id)->first();

        if (is_null($item)) {
            throw new Exception("Order item with vendor SKU [{$mappedData['vendor_sku']}] not found in order [{$order->id}]");
        }

        $item->update([
            'price' => $mappedData['price'],
            'quantity' => $mappedData['quantity'],
            'status' => $mappedData['status'],
        ]);
    }

    private static function statusZoetis(string $status)
    {
        switch ($status) {
            case ImportOrderItemVendorStatus::ZOETIS_ORDERED->value:
                return OrderItemStatus::Accepted->value;
            case ImportOrderItemVendorStatus::ZOETIS_SHIPPED->value:
                return OrderItemStatus::Shipped->value;
            case ImportOrderItemVendorStatus::ZOETIS_DELIVERED->value:
                return OrderItemStatus::Delivered->value;
            default:
                throw new InvalidArgumentException("Unsupported [ZOETIS] order item status: {$status}");
        }
    }

    private function zoetisTemplate(array $data): array
    {
        // Helper function to clean monetary values
        $cleanMonetaryValue = function ($value) {
            if (empty($value)) {
                return '0';
            }
            // Remove $ and convert comma-separated thousands to proper decimal
            $cleaned = str_replace(['$', ','], ['', ''], $value);

            return $cleaned;
        };

        return [
            'vendor' => 'ZOET',
            'order_number' => $data['reference'] ?? 'empty_value',
            'vendor_sku' => $data['productSku'] ?? '',
            'price' => (int) ((float) $cleanMonetaryValue($data['productPrice'] ?? '0') * 100),
            'quantity' => (int) ($data['productQuantity'] ?? 0),
            'total_price' => (int) ((float) $cleanMonetaryValue($data['productTotal'] ?? '0') * 100),
            'status' => $this->mapStatusToVendor('ZOET', $data['productStatus'] ?? ''),
        ];
    }
}
