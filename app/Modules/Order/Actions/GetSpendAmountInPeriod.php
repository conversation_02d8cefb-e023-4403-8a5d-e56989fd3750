<?php

declare(strict_types=1);

namespace App\Modules\Order\Actions;

use App\Models\OrderItem;
use Brick\Money\Money;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

final readonly class GetSpendAmountInPeriod
{
    public function handle(string $clinicId, array $productOfferIds, Carbon $startDate, Carbon $endDate): Money
    {
        $total = OrderItem::query()
            ->whereHas('order', fn (Builder $query) => $query->where('clinic_id', $clinicId)->whereNull('import_order_history_task_id'))
            ->whereHas('productOffer', fn (Builder $query) => $query->whereIn('id', $productOfferIds))
            ->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->sum('total_price');

        return Money::ofMinor($total, 'USD');
    }
}
