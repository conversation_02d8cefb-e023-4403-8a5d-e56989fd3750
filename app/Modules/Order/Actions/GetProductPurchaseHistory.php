<?php

declare(strict_types=1);

namespace App\Modules\Order\Actions;

use App\Models\OrderItem;
use App\Modules\Order\Data\PurchaseHistory;
use App\Modules\Order\Data\PurchaseHistoryDataPoint;
use Brick\Money\Money;
use Illuminate\Database\Eloquent\Collection as DatabaseCollection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

final class GetProductPurchaseHistory
{
    public function __invoke(string $clinicId, string $productOfferId): PurchaseHistory
    {
        [$startDateRange, $endDateRange] = $this->getDateRange();

        $orderItems = $this->getOrderItems($clinicId, $productOfferId, [$startDateRange, $endDateRange]);

        $months = $this->createEmptyMonthsCollection([$startDateRange, $endDateRange]);

        $data = $this->createPurchaseHistoryData($orderItems, $months);

        return PurchaseHistory::from([
            'product_offer_id' => $productOfferId,
            'data' => $data,
        ]);
    }

    private function getDateRange(): array
    {
        $endDateRange = Carbon::now()->endOfMonth();
        $startDateRange = $endDateRange->clone()->subMonthNoOverflow(11)->startOfMonth();

        return [$startDateRange, $endDateRange];
    }

    private function getOrderItems(string $clinicId, string $productOfferId, array $dateRange): DatabaseCollection
    {
        return Cache::remember(
            "order:product-purchase-history:{$clinicId}:{$productOfferId}",
            Carbon::now()->addDay(),
            function () use ($clinicId, $productOfferId, $dateRange) {
                return OrderItem::query()
                    ->whereHas('order', fn ($query) => $query->where('clinic_id', $clinicId))
                    ->where('product_offer_id', $productOfferId)
                    ->whereBetween('created_at', $dateRange)
                    ->where('quantity', '>', 0)
                    ->get();
            });
    }

    private function createEmptyMonthsCollection(array $dateRange): Collection
    {
        [$startDateRange, $endDateRange] = $dateRange;

        $months = collect();
        $current = $startDateRange->copy();

        while ($current <= $endDateRange) {
            $months->push($current->format('Y-m'));
            $current->addMonth();
        }

        return $months;
    }

    private function createPurchaseHistoryData(DatabaseCollection $orderItems, Collection $months): Collection
    {
        $grouped = $orderItems->groupBy(fn ($item) => $item->created_at->format('Y-m'));

        return $months->map(function ($month) use ($grouped) {
            $items = $grouped->get($month, collect());

            if ($items->isEmpty()) {
                return PurchaseHistoryDataPoint::from([
                    'label' => $month,
                    'total_spent' => Money::ofMinor(0, 'USD'),
                    'lowest_unit_price' => Money::ofMinor(0, 'USD'),
                    'highest_unit_price' => Money::ofMinor(0, 'USD'),
                ]);
            }

            return PurchaseHistoryDataPoint::from([
                'label' => $month,
                'total_spent' => Money::ofMinor($items->sum('total_price'), 'USD'),
                'lowest_unit_price' => Money::ofMinor($items->min('price'), 'USD'),
                'highest_unit_price' => Money::ofMinor($items->max('price'), 'USD'),
                'total_orders' => $items->count(),
                'total_quantity' => $items->sum('quantity'),
                'average_quantity_per_order' => $items->count() > 0 ? (int) ceil($items->sum('quantity') / $items->count()) : 0,
            ]);
        });
    }
}
