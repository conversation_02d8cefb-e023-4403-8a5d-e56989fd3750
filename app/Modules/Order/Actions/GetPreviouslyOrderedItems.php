<?php

declare(strict_types=1);

namespace App\Modules\Order\Actions;

use App\Enums\ProductStockStatus;
use App\Models\OrderItem;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Order\Data\PreviouslyOrderedItem;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

final class GetPreviouslyOrderedItems
{
    /**
     * Get the previously ordered items for a clinic based on search query.
     *
     * The items are ordered by:
     * 1. Latest purchase date (first priority)
     * 2. Number of times ordered (second priority if multiple items have the same latest purchase date)
     * 3. Limited to max 3 items
     * 4. Filtered by the required search query
     * 5. Only includes products from vendors with active connections to the clinic
     */
    public function handle(string $clinicId, string $searchQuery, int $limit = 3): Collection
    {

        $query = OrderItem::query()
            ->select([
                'product_offers.id as product_offer_id',
                'products.id as product_id',
                'products.name as product_name',
                'products.image_url',
                'vendors.id as vendor_id',
                'vendors.name as vendor_name',
                'product_offers.stock_status',
                'product_offers.increments',
                DB::raw('MAX(order_items.created_at) as last_ordered_at'),
                DB::raw('SUM(order_items.quantity) as total_quantity'),
                DB::raw('COUNT(DISTINCT orders.id) as order_count'),
                'order_items.price',
            ])
            ->join('orders', 'orders.id', '=', 'order_items.order_id')
            ->join('product_offers', 'product_offers.id', '=', 'order_items.product_offer_id')
            ->join('products', 'products.id', '=', 'product_offers.product_id')
            ->join('vendors', 'vendors.id', '=', 'product_offers.vendor_id')
            ->join('integration_connections', function ($join) use ($clinicId) {
                $join->on('integration_connections.vendor_id', '=', 'vendors.id')
                    ->where('integration_connections.clinic_id', $clinicId)
                    ->where('integration_connections.status', IntegrationConnectionStatus::Connected->value);
            })
            ->where('orders.clinic_id', $clinicId);

        // Apply search filter - query is required
        $searchTerms = explode(' ', $searchQuery);
        foreach ($searchTerms as $term) {
            $term = trim($term);
            if (! empty($term)) {
                $query->where(function ($q) use ($term) {
                    $q->where('products.name', 'ilike', "%{$term}%")
                        ->orWhere('product_offers.name', 'ilike', "%{$term}%");
                });
            }
        }

        $results = $query
            ->groupBy([
                'product_offers.id',
                'products.id',
                'products.name',
                'products.image_url',
                'vendors.id',
                'vendors.name',
                'product_offers.stock_status',
                'product_offers.increments',
                'order_items.price',
            ])
            ->orderByRaw('MAX(order_items.created_at) DESC')
            ->orderByRaw('COUNT(DISTINCT orders.id) DESC')
            ->limit($limit)
            ->get();

        return $results->map(function ($item) {
            return new PreviouslyOrderedItem(
                $item->product_offer_id,
                $item->product_id,
                $item->product_name,
                $item->vendor_id,
                $item->vendor_name,
                Carbon::parse($item->last_ordered_at),
                $item->total_quantity,
                $item->price,
                $item->image_url ?? '',
                $item->order_count,
                ProductStockStatus::from($item->stock_status),
                $item->increments
            );
        });
    }
}
