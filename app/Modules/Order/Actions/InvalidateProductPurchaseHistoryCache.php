<?php

declare(strict_types=1);

namespace App\Modules\Order\Actions;

use App\Models\Order;
use Illuminate\Support\Facades\Cache;

final readonly class InvalidateProductPurchaseHistoryCache
{
    public function handle(Order $order): void
    {
        $clinicId = $order->clinic_id;

        $productOfferIds = $order->items()
            ->select('product_offer_id')
            ->distinct()
            ->pluck('product_offer_id');

        foreach ($productOfferIds as $productOfferId) {
            Cache::forget("order:product-purchase-history:{$clinicId}:{$productOfferId}");
        }
    }
}
