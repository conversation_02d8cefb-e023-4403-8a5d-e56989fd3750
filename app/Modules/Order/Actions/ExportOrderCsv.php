<?php

declare(strict_types=1);

namespace App\Modules\Order\Actions;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\SubOrder;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;

final class ExportOrderCsv
{
    public function handle(string $orderId)
    {
        $order = Order::query()->findOrFail($orderId);

        $columns = [
            'date',
            'order_number',
            'product_name',
            'product_sku',
            'vendor',
            'vendor_order_number',
            'price',
            'quantity',
            'total_price',
        ];

        $csvFileName = "{$orderId}-checklist.csv";
        $csvFile = tmpfile();
        fputcsv($csvFile, $columns);

        $order->suborders()->each(function (SubOrder $subOrder) use ($csvFile) {
            $subOrder->items->each(function (OrderItem $orderItem) use ($csvFile, $subOrder) {
                fputcsv($csvFile, [
                    $orderItem->order->created_at->format('Y-m-d'),
                    $orderItem->order->order_number,
                    $orderItem->productOffer->name,
                    $orderItem->productOffer->vendor_sku,
                    $orderItem->vendor->name,
                    $subOrder->external_id ?? 'N/A',
                    round($orderItem->price / 100, 2),
                    $orderItem->quantity,
                    round($orderItem->total_price / 100, 2),
                ]);
            });
        });

        Storage::disk('local')->put($csvFileName, $csvFile);
        fclose($csvFile);

        return Response::download(Storage::path($csvFileName), $csvFileName)->deleteFileAfterSend(true);
    }
}
