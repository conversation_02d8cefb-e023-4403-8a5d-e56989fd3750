<?php

declare(strict_types=1);

namespace App\Modules\Order\Actions;

use App\Models\OrderItem;
use Illuminate\Support\Collection;

final readonly class FetchProductOfferIds
{
    /**
     * @return \Illuminate\Database\Eloquent\Collection<int, string>
     */
    public function handle(string $orderId): Collection
    {
        return OrderItem::query()
            ->where('order_id', $orderId)
            ->pluck('product_offer_id')
            ->unique();
    }
}
