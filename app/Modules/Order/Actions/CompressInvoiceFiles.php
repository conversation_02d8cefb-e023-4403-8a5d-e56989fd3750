<?php

declare(strict_types=1);

namespace App\Modules\Order\Actions;

use App\Models\Order;
use App\Modules\Order\Exceptions\InvoicesNotAvailableException;
use App\Modules\Order\Exceptions\MissingInvoiceFileException;
use App\Modules\Order\Services\Vendor\Contracts\InvoiceSynchronizer;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Support\Facades\Storage;
use STS\ZipStream\Facades\Zip;

final class CompressInvoiceFiles
{
    /**
     * @throws InvoicesNotAvailableException
     * @throws MissingInvoiceFileException
     */
    public function handle(string $orderId): Responsable
    {
        $order = Order::query()->findOrFail($orderId);

        $invoices = $order->suborders->pluck('externalOrders')->collapse()
            ->whereNotNull('invoice_file_path');

        if ($invoices->isEmpty()) {
            throw new InvoicesNotAvailableException("No invoices available for order [{$orderId}]");
        }

        if (! $invoices->every(fn ($invoice) => Storage::disk(InvoiceSynchronizer::INVOICE_STORAGE_DISK)->exists($invoice->invoice_file_path))) {
            throw new MissingInvoiceFileException("At least one invoice file is missing for order [{$orderId}]");
        }

        return Zip::create("{$orderId}-invoices.zip", $invoices->pluck('invoice_file_path')
            ->map(fn ($filePath) => Storage::disk(InvoiceSynchronizer::INVOICE_STORAGE_DISK)->path($filePath))
            ->toArray()
        );
    }
}
