<?php

declare(strict_types=1);

namespace App\Modules\Order\Actions;

use App\Models\OrderItem;
use App\Modules\Dashboard\Data\MetricData;
use App\Modules\Order\Queries\TotalSpendOrderItemQueryBuilder;
use Brick\Money\Money;

final class GetTotalSpendByClinic
{
    public function handle(string $clinicId): MetricData
    {
        $totalSpend = TotalSpendOrderItemQueryBuilder::for(
            OrderItem::query()
        )->byClinic($clinicId)->first()->total_spend;

        $totalSpend = Money::ofMinor($totalSpend, 'USD')->formatTo('en_US');

        return MetricData::from([
            'title' => 'Total Spend',
            'value' => $totalSpend,
            'description' => 'Through HFV',
            'helpText' => 'Total spend of all orders within Highfive, excluding taxes & fees',
        ]);
    }
}
