<?php

declare(strict_types=1);

use App\Modules\Order\Http\Controllers\DownloadChecklistController;
use App\Modules\Order\Http\Controllers\DownloadInvoicesController;
use App\Modules\Order\Http\Controllers\OrderController;
use Illuminate\Support\Facades\Route;

Route::middleware(['api', 'auth:sanctum', 'can:clinic.orders.view'])->prefix('api')->group(function () {
    Route::middleware(['signed'])->group(function () {
        Route::get('orders/{order}/download-invoices', DownloadInvoicesController::class)->name('order.invoice.download');
        Route::get('orders/{order}/download-checklist', DownloadChecklistController::class)->name('order.checklist.download');
    });
    Route::middleware('header:highfive-clinic')->group(function () {
        Route::get('orders', [OrderController::class, 'index']);
        Route::get('orders/{order}', [OrderController::class, 'show']);
    });
});
