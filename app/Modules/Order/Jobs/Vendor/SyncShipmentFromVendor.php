<?php

declare(strict_types=1);

namespace App\Modules\Order\Jobs\Vendor;

use App\Jobs\BaseVendorJob;
use App\Models\SubOrder;
use App\Modules\Order\Services\Vendor\Contracts\ShipmentSynchronizer;
use App\Modules\Order\Services\Vendor\Exceptions\ApiAuthenticationException;
use App\Modules\Order\Services\Vendor\Exceptions\ApiException;
use App\Modules\Order\Services\Vendor\Factory;
use Illuminate\Support\Facades\Log;

final class SyncShipmentFromVendor extends BaseVendorJob
{
    public function __construct(private SubOrder $suborder)
    {
        parent::__construct($suborder->order->clinic_id, $suborder->vendor_id);
    }

    public function handle(): void
    {

        try {
            $vendorConnection = $this->getVendorConnection();
            $service = Factory::make($vendorConnection);
            if (! $service instanceof ShipmentSynchronizer) {
                Log::warning('Vendor service does not support shipment synchronization');

                return;
            }
            $service->syncShipment($this->suborder, $vendorConnection);
        } catch (ApiException|ApiAuthenticationException $e) {
            throw $e;
        }
    }
}
