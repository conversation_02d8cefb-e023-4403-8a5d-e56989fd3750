<?php

declare(strict_types=1);

namespace App\Modules\Order\Jobs\Import;

use App\Enums\AddressType;
use App\Enums\ProductStockStatus;
use App\Models\Product;
use App\Models\Vendor;
use App\Modules\Order\Models\ImportOrderHistoryTask;
use App\Support\SequenceNumber;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use League\Csv\Reader;
use League\Csv\Statement;
use Throwable;

final class ProcessImportedOrderHistoryCsvFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 600;

    private Collection $vendors;

    public function __construct(
        private readonly ImportOrderHistoryTask $task
    ) {}

    public function handle(): void
    {
        ini_set('memory_limit', '2048M');
        try {
            Log::info('Starting import order history task processing', [
                'task_id' => $this->task->id,
                'file_name' => $this->task->file_path,
            ]);

            $this->task->markAsProcessing();

            if (! Storage::disk('local')->exists($this->task->file_path)) {
                throw new Exception('CSV file not found: '.$this->task->file_path);
            }

            $csvContent = Storage::disk('local')->get($this->task->file_path);

            $reader = Reader::createFromString($csvContent);
            $toSkip = Statement::create()->where(
                fn (array $record) => collect($this->required())->every(fn (string $field) => $record[$field] === '')
            )->process($reader, $this->headers());

            $toSkipCount = iterator_count($toSkip);

            $toProcess = Statement::create()->where(
                fn (array $record) => collect($this->required())->every(fn (string $field) => $record[$field] !== '')
            )->process($reader, $this->headers());

            $toProcessCount = iterator_count($toProcess);

            Log::info('Import order history task processing counts', [
                'task_id' => $this->task->id,
                'to_skip_count' => $toSkipCount,
                'to_process_count' => $toProcessCount,
            ]);

            $this->task->update(['total_rows_count' => $toProcessCount - 1]);

            $processedRows = 0;
            $errors = [];

            $this->validateHeader($toProcess->getHeader());

            $this->vendors = Vendor::get();

            foreach ($toProcess as $index => $record) {
                if ($record['Date'] === 'Date') {
                    continue;
                }

                try {

                    $this->processOrderHistoryRow($record, $index + 1);

                } catch (Exception $e) {
                    $rowNumber = $index + 1;
                    $userFriendlyError = $this->createUserFriendlyErrorMessage($e, $rowNumber);

                    $errors[] = [
                        'row' => $rowNumber,
                        'error' => $userFriendlyError,
                        'original_error' => $e->getMessage(),
                        'data' => $record ?? null,
                    ];

                    Log::warning('Failed to process row in import order history task', [
                        'task_id' => $this->task->id,
                        'row' => $rowNumber,
                        'error' => $e->getMessage(),
                        'user_friendly_error' => $userFriendlyError,
                    ]);

                    $this->task->refresh();
                    $this->task->update([
                        'status_reason' => $this->task->status_reason.'<br>'.$userFriendlyError,
                    ]);
                }

                $processedRows++;

                if ($processedRows % 100 === 0) {
                    $this->task->update([
                        'processed_rows_count' => $processedRows,
                    ]);
                }
            }

            $this->task->update([
                'processed_rows_count' => $processedRows,
            ]);

            $this->task->markAsCompleted();

            Log::info('Import order history task completed successfully', [
                'task_id' => $this->task->id,
                'errors' => $errors,
            ]);

        } catch (Exception $e) {
            Log::error('Import order history task failed', [
                'task_id' => $this->task->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            $this->task->markAsCompleted();
        }
    }

    public function failed(Throwable $exception): void
    {
        Log::error('Import order history task job failed', [
            'task_id' => $this->task->id,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }

    private function validateHeader(array $headers): void
    {
        $requiredHeaders = $this->required();

        $invalidHeaders = '';
        foreach ($requiredHeaders as $header) {
            if (! in_array($header, $headers, true)) {
                $invalidHeaders .= "Required header '{$header}' is missing in the CSV file.".PHP_EOL;
            }
        }

        if ($invalidHeaders !== '') {
            throw new Exception('Invalid CSV headers: '.$invalidHeaders);
        }
    }

    private function required(): array
    {
        return [
            'Date',
            'Name',
            'Order #',
            'Supplier',
            'Quantity',
            'Total Price',
            "Supplier's SKU",
        ];
    }

    private function getFieldFriendlyNames(): array
    {
        return [
            'Date' => 'Date',
            'Name' => 'Product Name',
            'Order #' => 'Order Number',
            'Supplier' => 'Supplier',
            'Quantity' => 'Quantity',
            'Total Price' => 'Total Price',
            "Supplier's SKU" => 'Supplier SKU',
        ];
    }

    private function createUserFriendlyErrorMessage(Exception $e, ?int $rowNumber = null): string
    {
        $message = $e->getMessage();
        $friendlyNames = $this->getFieldFriendlyNames();

        foreach ($this->required() as $field) {
            if (str_contains($message, $field)) {
                $friendlyField = $friendlyNames[$field] ?? $field;

                return $rowNumber
                    ? "Could not process line {$rowNumber} - Required header '{$friendlyField}' is empty"
                    : "Required header '{$friendlyField}' is empty";
            }
        }

        if (str_contains($message, 'Could not find vendor')) {
            return $rowNumber
                ? "Could not process line {$rowNumber} - Vendor not found | ".$message
                : 'Vendor not found | '.$message;
        }

        if (str_contains($message, 'CSV file not found')) {
            return 'CSV file not found';
        }

        if (str_contains($message, 'invalid') || str_contains($message, 'Invalid')) {
            return $rowNumber
                ? "Could not process line {$rowNumber} - Invalid data"
                : 'Invalid data in file';
        }

        return $rowNumber
            ? "Could not process line {$rowNumber} - Processing error"
            : 'File processing error';
    }

    private function headers(): array
    {
        return [
            'Vetcove ID',
            'Date',
            'Hospital',
            'Name',
            'Order #',
            'PO Number',
            'Supplier',
            'Manufacturer',
            'Manufacturer Number',
            'Primary Category',
            'Secondary Category',
            'Vetcove Item ID',
            'Cost per Dose',
            'Units',
            'Unit Price',
            'Unit Measurement',
            'List Price',
            'Quantity',
            'Total Price',
            'Item Status',
            "Supplier's SKU",
        ];
    }

    private function mapExternalHeadersToOurInternal(array $record): array
    {
        return [
            'date' => $record['Date'],
            'vendor' => $record['Supplier'],
            'order_number' => $record['Order #'],
            'product_name' => $record['Name'],
            'manufacturer' => $record['Manufacturer'],
            'manufacturer_number' => $record['Manufacturer Number'],
            'vendor_sku' => $record["Supplier's SKU"],
            'list_price' => $record['List Price'] ? (float) str_replace('$', '', $record['List Price']) : null,
            'quantity' => (int) $record['Quantity'],
            'total_price' => $record['Total Price'] ? (float) $record['Total Price'] : null,
        ];
    }

    private function processOrderHistoryRow(array $record): void
    {
        $record = $this->mapExternalHeadersToOurInternal($record);
        $clinic = $this->task->clinic;

        Product::withoutSyncingToSearch(function () use ($clinic, $record) {
            if (! $vendor = $this->vendors->where('name', $record['vendor'])->first()) {
                throw new Exception("Could not find vendor with name [{$record['vendor']}]");
            }

            $record['total_price'] = (int) ((float) ($record['total_price']) * 100);

            if ($record['quantity'] > 0) {
                $record['list_price'] = (int) ($record['total_price'] / $record['quantity']);
            } else {
                $record['list_price'] = 0;
            }

            $product = $vendor->productOffers()->firstOrCreate([
                'vendor_sku' => $record['vendor_sku'],
            ], [
                'deactivated_at' => now(),
                'name' => $record['product_name'],
                'price' => $record['list_price'],
                'increments' => 1,
                'stock_status' => ProductStockStatus::OutOfStock,
                'external_id' => '',
                'external_url' => '',
            ]);

            $order = $clinic->orders()->whereHas('suborders', function (Builder $query) use ($vendor, $record) {
                $query->where('vendor_id', $vendor->id)->where('external_id', $record['order_number']);
            })->first();

            if (is_null($order)) {
                $order = DB::transaction(function () use ($clinic, $vendor, $record) {
                    $order = $clinic->orders()->createQuietly([
                        'order_number' => SequenceNumber::next('IOH'),
                        'payment_method' => 'INVOICE',
                        'created_at' => $record['date'],
                        'updated_at' => $record['date'],
                        'import_order_history_task_id' => $this->task->id,
                    ]);

                    $order->shippingAddress()->updateOrCreate([], [
                        'type' => AddressType::Shipping,
                        'street' => $clinic->shippingAddress->street,
                        'city' => $clinic->shippingAddress->city,
                        'state' => $clinic->shippingAddress->state,
                        'postal_code' => $clinic->shippingAddress->postal_code,
                    ]);

                    $order->billingAddress()->updateOrCreate([], [
                        'type' => AddressType::Billing,
                        'street' => $clinic->billingAddress->street,
                        'city' => $clinic->billingAddress->city,
                        'state' => $clinic->billingAddress->state,
                        'postal_code' => $clinic->billingAddress->postal_code,
                    ]);

                    $order->suborders()->create([
                        'vendor_id' => $vendor->id,
                        'external_id' => $record['order_number'],
                        'external_status' => '',
                        'created_at' => $record['date'],
                        'updated_at' => $record['date'],
                    ]);

                    return $order;
                });
            }

            $order->items()->updateOrCreate([
                'product_offer_id' => $product->id,
            ], [
                'price' => $record['list_price'],
                'quantity' => $record['quantity'],
                'status' => 'DELIVERED',
                'external_id' => '',
                'external_status' => '',
                'created_at' => $record['date'],
                'updated_at' => $record['date'],
            ]);
        });
    }
}
