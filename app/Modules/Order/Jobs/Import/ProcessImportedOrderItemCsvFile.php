<?php

declare(strict_types=1);

namespace App\Modules\Order\Jobs\Import;

use App\Models\Vendor;
use App\Modules\Order\Actions\ImportOrderItemCsvMapping;
use App\Modules\Order\Enums\ImportOrderItemCsvStatus;
use App\Modules\Order\Models\ImportOrderItemCsvTask;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

final class ProcessImportedOrderItemCsvFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300;

    private Collection $vendors;

    public function __construct(
        private readonly ImportOrderItemCsvTask $task
    ) {}

    public function handle(): void
    {

        try {
            Log::info('Starting import order item csv task processing', [
                'task_id' => $this->task->id,
                'file_name' => $this->task->file_path,
            ]);

            $this->task->markAsProcessing();

            if (! Storage::disk('local')->exists($this->task->file_path)) {
                throw new Exception('CSV file not found: '.$this->task->file_path);
            }

            $csvContent = Storage::disk('local')->get($this->task->file_path);

            $csvContent = $this->cleanCsvContent($csvContent);

            $lines = explode("\n", $csvContent);
            $headerLine = array_shift($lines);
            $headers = str_getcsv($headerLine);

            Log::info('CSV processing debug', [
                'task_id' => $this->task->id,
                'original_lines_count' => mb_substr_count($csvContent, "\n"),
                'cleaned_lines_count' => count($lines),
                'first_few_lines' => array_slice($lines, 0, 3),
            ]);

            Log::info('CSV Headers detected', [
                'task_id' => $this->task->id,
                'headers' => $headers,
                'header_count' => count($headers),
            ]);

            $vendorIndex = array_search('vendor', $headers);
            if ($vendorIndex === false) {
                $this->task->update([
                    'status' => ImportOrderItemCsvStatus::Failed,
                    'status_reason' => 'Vendor header not found in CSV file, check the template please.',
                ]);

                return;
            }

            if (empty($lines)) {
                throw new Exception('No data rows found in CSV file');
            }

            $firstLine = $lines[0];
            $firstRowData = $this->parseComplexCsvLine($firstLine, $headers);

            if (! isset($firstRowData['vendor'])) {
                throw new Exception('Vendor field not found in CSV data row');
            }

            $vendorCode = trim($firstRowData['vendor']);
            if (empty($vendorCode)) {
                throw new Exception('Vendor field is empty in first data row');
            }

            Log::info('Detected vendor in CSV', [
                'task_id' => $this->task->id,
                'vendor' => $vendorCode,
            ]);

            $vendor = Vendor::where('key', $vendorCode)->first();
            if (! $vendor) {
                throw new Exception("Vendor with slug [{$vendorCode}] not found in database");
            }

            $action = new ImportOrderItemCsvMapping;
            $headers = $action->mapHeaders($vendorCode);

            $requiredFields = ['vendor', 'reference', 'productSku', 'productPrice', 'productQuantity', 'productTotal', 'productStatus'];

            $validRecords = [];
            foreach ($lines as $line) {
                if (trim($line) === '') {
                    continue;
                }

                $record = $this->parseComplexCsvLine($line, $headers);

                $hasAllRequiredFields = collect($requiredFields)->every(fn (string $field) => isset($record[$field]) && trim($record[$field]) !== '');

                if ($hasAllRequiredFields) {
                    $validRecords[] = $record;
                }
            }

            $toProcessCount = count($validRecords);

            Log::info('Import order item task processing counts', [
                'task_id' => $this->task->id,
                'to_process_count' => $toProcessCount,
            ]);

            $this->task->update(['total_rows_count' => $toProcessCount]);

            $processedRows = 0;
            $successfulRows = 0;
            $failedRows = 0;
            $errors = [];

            foreach ($validRecords as $index => $record) {
                try {
                    $action->handle($record, $vendor, $this->task->clinic);
                    $successfulRows++;
                } catch (Exception $e) {
                    $failedRows++;
                    $rowNumber = $index + 1;

                    $errors[] = [
                        'row' => $rowNumber,
                        'error' => $e->getMessage(),
                        'original_error' => $e->getMessage(),
                        'data' => $record ?? null,
                    ];

                    Log::warning('Failed to process row in import order item csv', [
                        'task_id' => $this->task->id,
                        'row' => $rowNumber,
                        'error' => $e->getMessage(),
                    ]);

                    $this->task->refresh();
                    $this->task->update([
                        'status_reason' => $this->task->status_reason.'<br>'.$e->getMessage(),
                    ]);
                }

                $processedRows++;

                if ($processedRows % 100 === 0) {
                    $this->task->update([
                        'processed_rows_count' => $processedRows,
                    ]);
                }
            }

            $this->task->update([
                'processed_rows_count' => $processedRows,
            ]);

            $this->task->markAsCompleted();

            Log::info('Import order item task completed successfully', [
                'task_id' => $this->task->id,
                'processed_rows' => $processedRows,
                'successful_rows' => $successfulRows,
                'failed_rows' => $failedRows,
                'errors' => $errors,
            ]);

        } catch (Exception $e) {
            Log::error('Import order item task failed', [
                'task_id' => $this->task->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $this->task->markAsFailed($e->getMessage());

            throw $e;
        }
    }

    private function parseComplexCsvLine(string $line, array $headers): array
    {
        $fields = str_getcsv($line);

        $record = [];
        foreach ($headers as $index => $header) {
            $record[$header] = $fields[$index] ?? null;
        }

        return $record;
    }

    private function cleanCsvContent(string $csvContent): string
    {
        $lines = explode("\n", $csvContent);
        $cleanedLines = [];

        foreach ($lines as $line) {
            if (trim($line) === '') {
                continue;
            }
            $cleanedLine = $line;
            $cleanedLine = preg_replace('/\[.*?\]/', 'REMOVED', $cleanedLine);
            $cleanedLine = str_replace(["\u{A0}", "\x{A0}", chr(160), "\t", "\r"], '', $cleanedLine);
            $cleanedLine = str_replace(['[', ']'], '', $cleanedLine);
            $cleanedLine = preg_replace('/,+/', ',', $cleanedLine);
            $cleanedLines[] = $cleanedLine;
        }

        return implode("\n", $cleanedLines);
    }
}
