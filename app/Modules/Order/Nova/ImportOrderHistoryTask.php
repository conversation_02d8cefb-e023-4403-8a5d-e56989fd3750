<?php

declare(strict_types=1);

namespace App\Modules\Order\Nova;

use App\Modules\Order\Enums\ImportOrderHistoryTaskStatus;
use App\Nova\Clinic;
use App\Nova\Order;
use App\Nova\Resource;
use <PERSON>vel\Nova\Fields\Badge;
use <PERSON>vel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\File;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

final class ImportOrderHistoryTask extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Modules\Order\Models\ImportOrderHistoryTask>
     */
    public static $model = \App\Modules\Order\Models\ImportOrderHistoryTask::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'file_path';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'file_path',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make('ID', 'id')
                ->sortable(),

            BelongsTo::make('Clinic', 'clinic', Clinic::class)
                ->searchable()
                ->sortable()
                ->readonly(fn (NovaRequest $request) => $request->isUpdateOrUpdateAttachedRequest()),

            File::make('CSV File', 'file_path')
                ->disk('local')
                ->path('import-order-history')
                ->acceptedTypes('.csv')
                ->rules('required')
                ->readonly(fn (NovaRequest $request) => $request->isUpdateOrUpdateAttachedRequest())
                ->hideFromIndex(),

            Badge::make('Status', 'status')
                ->map([
                    ImportOrderHistoryTaskStatus::Pending->value => 'info',
                    ImportOrderHistoryTaskStatus::Processing->value => 'warning',
                    ImportOrderHistoryTaskStatus::Completed->value => 'success',
                    ImportOrderHistoryTaskStatus::Failed->value => 'danger',
                ])
                ->sortable(),

            Textarea::make('Status Reason', 'status_reason')
                ->hideFromIndex()
                ->readonly()
                ->alwaysShow()
                ->resolveUsing(fn ($value) => str_replace('<br>', "\n", $value ?? '')),

            DateTime::make('Started At', 'started_at')
                ->hideFromIndex()
                ->readonly(),

            DateTime::make('Finished At', 'finished_at')
                ->hideFromIndex()
                ->readonly(),

            Number::make('Total Rows', 'total_rows_count')
                ->hideFromIndex()
                ->readonly(),

            Number::make('Processed Rows', 'processed_rows_count')
                ->hideFromIndex()
                ->readonly(),

            HasMany::make('Orders', 'orders', Order::class),
        ];
    }
}
