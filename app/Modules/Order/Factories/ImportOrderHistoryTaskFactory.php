<?php

declare(strict_types=1);

namespace App\Modules\Order\Factories;

use App\Models\Clinic;
use App\Modules\Order\Enums\ImportOrderHistoryTaskStatus;
use App\Modules\Order\Models\ImportOrderHistoryTask;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Modules\Order\Models\ImportOrderHistoryTask>
 */
final class ImportOrderHistoryTaskFactory extends Factory
{
    protected $model = ImportOrderHistoryTask::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'clinic_id' => Clinic::factory(),
            'file_path' => 'import-order-history/'.$this->faker->uuid().'.csv',
            'status' => ImportOrderHistoryTaskStatus::Pending,
            'status_reason' => null,
            'started_at' => null,
            'finished_at' => null,
            'total_rows_count' => null,
            'processed_rows_count' => 0,
        ];
    }
}
