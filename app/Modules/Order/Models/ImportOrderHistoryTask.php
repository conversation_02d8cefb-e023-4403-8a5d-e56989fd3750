<?php

declare(strict_types=1);

namespace App\Modules\Order\Models;

use App\Models\Clinic;
use App\Models\Order;
use App\Modules\Order\Enums\ImportOrderHistoryTaskStatus;
use App\Modules\Order\Factories\ImportOrderHistoryTaskFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class ImportOrderHistoryTask extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = [];

    protected $attributes = [
        'status' => ImportOrderHistoryTaskStatus::Pending,
    ];

    protected $casts = [
        'status' => ImportOrderHistoryTaskStatus::class,
        'started_at' => 'datetime',
        'finished_at' => 'datetime',
        'total_rows_count' => 'integer',
        'processsed_rows_count' => 'integer',
    ];

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'import_order_history_task_id');
    }

    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    public function isPending(): bool
    {
        return $this->status === ImportOrderHistoryTaskStatus::Pending;
    }

    public function isProcessing(): bool
    {
        return $this->status === ImportOrderHistoryTaskStatus::Processing;
    }

    public function isCompleted(): bool
    {
        return $this->status === ImportOrderHistoryTaskStatus::Completed;
    }

    public function isFailed(): bool
    {
        return $this->status === ImportOrderHistoryTaskStatus::Failed;
    }

    public function markAsProcessing(): void
    {
        $this->update([
            'status' => ImportOrderHistoryTaskStatus::Processing,
            'started_at' => now(),
        ]);
    }

    public function markAsCompleted(): void
    {
        $this->update([
            'status' => ImportOrderHistoryTaskStatus::Completed,
            'finished_at' => now(),
        ]);
    }

    public function markAsFailed(string $reason): void
    {
        $this->update([
            'status' => ImportOrderHistoryTaskStatus::Failed,
            'status_reason' => $reason,
            'finished_at' => now(),
        ]);
    }

    public function getProgressPercentageAttribute(): float
    {
        if (! $this->total_rows || $this->total_rows === 0) {
            return 0;
        }

        return round(($this->processed_rows / $this->total_rows) * 100, 2);
    }

    public function getSuccessRateAttribute(): float
    {
        if (! $this->processed_rows || $this->processed_rows === 0) {
            return 0;
        }

        return round(($this->successful_rows / $this->processed_rows) * 100, 2);
    }

    protected static function newFactory(): ImportOrderHistoryTaskFactory
    {
        return ImportOrderHistoryTaskFactory::new();
    }
}
