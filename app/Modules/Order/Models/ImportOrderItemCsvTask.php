<?php

declare(strict_types=1);

namespace App\Modules\Order\Models;

use App\Models\Clinic;
use App\Modules\Order\Enums\ImportOrderItemCsvStatus;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class ImportOrderItemCsvTask extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = [];

    protected $attributes = [
        'status' => ImportOrderItemCsvStatus::Pending,
    ];

    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    public function isPending(): bool
    {
        return $this->status === ImportOrderItemCsvStatus::Pending;
    }

    public function isProcessing(): bool
    {
        return $this->status === ImportOrderItemCsvStatus::Processing;
    }

    public function isCompleted(): bool
    {
        return $this->status === ImportOrderItemCsvStatus::Completed;
    }

    public function isFailed(): bool
    {
        return $this->status === ImportOrderItemCsvStatus::Failed;
    }

    public function markAsProcessing(): void
    {
        $this->update([
            'status' => ImportOrderItemCsvStatus::Processing,
            'started_at' => now(),
        ]);
    }

    public function markAsCompleted(): void
    {
        $this->update([
            'status' => ImportOrderItemCsvStatus::Completed,
            'finished_at' => now(),
        ]);
    }

    public function markAsFailed(string $reason): void
    {
        $this->update([
            'status' => ImportOrderItemCsvStatus::Failed,
            'status_reason' => $reason,
            'finished_at' => now(),
        ]);
    }

    public function getProgressPercentageAttribute(): float
    {
        if (! $this->total_rows || $this->total_rows === 0) {
            return 0;
        }

        return round(($this->processed_rows / $this->total_rows) * 100, 2);
    }

    protected function casts(): array
    {
        return [
            'status' => ImportOrderItemCsvStatus::class,
            'started_at' => 'datetime',
            'finished_at' => 'datetime',
            'total_rows_count' => 'integer',
            'processsed_rows_count' => 'integer',
        ];
    }
}
