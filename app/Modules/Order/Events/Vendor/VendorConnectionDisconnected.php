<?php

declare(strict_types=1);

namespace App\Modules\Order\Events\Vendor;

use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

final class VendorConnectionDisconnected
{
    use Dispatchable, SerializesModels;

    public IntegrationConnection $connection;

    public string $message;

    public function __construct(IntegrationConnection $connection, string $message)
    {
        $this->connection = $connection;
        $this->message = $message;
    }
}
