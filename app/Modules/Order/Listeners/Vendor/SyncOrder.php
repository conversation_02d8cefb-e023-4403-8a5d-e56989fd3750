<?php

declare(strict_types=1);

namespace App\Modules\Order\Listeners\Vendor;

use App\Modules\Order\Events\Vendor\OrderPlaced;
use App\Modules\Order\Jobs\Vendor\SyncInvoiceFromVendor;
use App\Modules\Order\Jobs\Vendor\SyncOrderFromVendor;

final class SyncOrder
{
    public function handle(OrderPlaced $event): void
    {
        SyncOrderFromVendor::dispatch($event->suborder)
            ->delay(now()->addMinute());

        SyncInvoiceFromVendor::dispatch($event->suborder)
            ->delay(now()->addMinute());
    }
}
