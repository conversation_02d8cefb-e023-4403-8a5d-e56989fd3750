<?php

declare(strict_types=1);

namespace App\Modules\Order\Listeners\Vendor;

use App\Modules\Order\Events\Vendor\VendorConnectionDisconnected;

final class MarkConnectionAsDisconnected
{
    public function handle(VendorConnectionDisconnected $event): void
    {
        $event->connection->markAsDisconnected(message: '**We couldn’t authenticate with your vendor account.** Please verify that your credentials are correct and that you’re able to access the vendor platform directly using them.');
    }
}
