<?php

declare(strict_types=1);

namespace App\Modules\Order\Queries;

use App\Enums\OrderItemStatus;
use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedSort;
use <PERSON><PERSON>\QueryBuilder\QueryBuilder;

final class OrdersQuery extends QueryBuilder
{
    public function __construct(Builder $query)
    {
        $query->withCount([
            'items',
        ])
            ->addSelect(['vendors_count' => function ($query) {
                $query->selectRaw('COUNT(DISTINCT product_offers.vendor_id)')
                    ->from('order_items')
                    ->join('product_offers', 'order_items.product_offer_id', '=', 'product_offers.id')
                    ->whereColumn('order_items.order_id', 'orders.id');
            }]);

        parent::__construct($query);

        $this->allowedFilters([
            AllowedFilter::callback('search', function (Builder $query, $value) {
                return $query->where(function ($q) use ($value) {

                    // sqlite workaround
                    $likeOperator = $q->getConnection()->getDriverName() === 'pgsql' ? 'iLIKE' : 'LIKE';

                    $q->where('orders.order_number', $likeOperator, "%{$value}%")
                        ->orWhereHas('items.productOffer', function ($q) use ($value, $likeOperator) {
                            $q->where('name', $likeOperator, "%{$value}%");
                        })
                        ->orWhereHas('items.productOffer.vendor', function ($q) use ($value, $likeOperator) {
                            $q->where('name', $likeOperator, "%{$value}%");
                        });
                });
            }),

            AllowedFilter::callback('backordered', function (Builder $query, $value) {
                if (filter_var($value, FILTER_VALIDATE_BOOLEAN)) {
                    return $query->whereHas('items', function (Builder $q) {
                        $q->where('status', OrderItemStatus::Backordered);
                    });
                }
                return $query;
            }),

            AllowedFilter::scope('date_from'),
            AllowedFilter::scope('date_to'),
        ]);

        $this->allowedSorts([
            AllowedSort::field('order_number'),
            AllowedSort::field('date', 'created_at'),
        ]);

        $this->defaultSort(
            AllowedSort::field('-date', 'created_at'),
        );
    }
}
