<?php

declare(strict_types=1);

namespace App\Modules\Order\Queries;

use App\Enums\OrderItemStatus;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Spatie\QueryBuilder\AllowedFilter;
use Spa<PERSON>\QueryBuilder\QueryBuilder;

final class TotalSpendOrderItemQueryBuilder extends QueryBuilder
{
    public function __construct(Builder $query)
    {
        parent::__construct($query);

        $this->select([
            DB::raw('COALESCE(SUM(order_items.total_price),0) as total_spend'),
        ])
            ->join('orders', 'orders.id', '=', 'order_items.order_id')
            ->whereNull('orders.import_order_history_task_id')
            ->whereNotIn('order_items.status', [
                OrderItemStatus::Pending,
                OrderItemStatus::PlacementFailed,
                OrderItemStatus::Rejected,
                OrderItemStatus::Cancelled,
                OrderItemStatus::Returned,
            ]);

        $this->allowedFilters([
            AllowedFilter::callback('date_from', function (Builder $query, $value) {
                $query->whereDate('orders.created_at', '>=', $value);
            }),
            AllowedFilter::callback('date_to', function (Builder $query, $value) {
                $query->whereDate('orders.created_at', '<=', $value);
            }),
        ]);

    }

    public function byClinic(string $clinicId): self
    {
        return $this->where('orders.clinic_id', $clinicId);
    }
}
