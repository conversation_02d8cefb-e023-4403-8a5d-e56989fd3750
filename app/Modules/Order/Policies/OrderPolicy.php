<?php

declare(strict_types=1);

namespace App\Modules\Order\Policies;

use App\Models\Order;
use App\Models\User;

final class OrderPolicy
{
    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, Order $order): bool
    {
        return $user->can('viewNova') || $user->belongsToAccount($order->clinic->clinic_account_id);
    }

    public function update(User $user, Order $order): bool
    {
        return $user->can('viewNova') || $user->belongsToAccount($order->clinic->clinic_account_id);
    }
}
