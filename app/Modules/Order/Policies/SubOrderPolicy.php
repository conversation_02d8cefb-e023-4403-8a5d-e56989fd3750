<?php

declare(strict_types=1);

namespace App\Modules\Order\Policies;

use App\Models\SubOrder;
use App\Models\User;

final class SubOrderPolicy
{
    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, SubOrder $suborder): bool
    {
        return $user->can('viewNova') || $user->belongsToAccount($suborder->order->clinic->clinic_account_id);
    }

    public function create(User $user): bool
    {
        return $user->can('viewNova');
    }

    public function update(User $user, SubOrder $suborder): bool
    {
        return $user->can('viewNova') || $user->belongsToAccount($suborder->order->clinic->clinic_account_id);
    }

    public function delete(User $user, SubOrder $suborder): bool
    {
        return $user->can('viewNova') || $user->belongsToAccount($suborder->order->clinic->clinic_account_id);
    }

    public function restore(User $user, SubOrder $suborder): bool
    {
        return $user->can('viewNova') || $user->belongsToAccount($suborder->order->clinic->clinic_account_id);
    }

    public function forceDelete(User $user, SubOrder $suborder): bool
    {
        return $user->can('viewNova') || $user->belongsToAccount($suborder->order->clinic->clinic_account_id);
    }
}
