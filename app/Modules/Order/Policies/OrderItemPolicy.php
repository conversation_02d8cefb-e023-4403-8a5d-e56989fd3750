<?php

declare(strict_types=1);

namespace App\Modules\Order\Policies;

use App\Models\OrderItem;
use App\Models\User;

final class OrderItemPolicy
{
    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, OrderItem $item): bool
    {
        return $user->can('viewNova') || $user->belongsToAccount($item->order->clinic->clinic_account_id);
    }

    public function update(User $user, OrderItem $item): bool
    {
        return $user->can('viewNova') || $user->belongsToAccount($item->order->clinic->clinic_account_id);
    }
}
