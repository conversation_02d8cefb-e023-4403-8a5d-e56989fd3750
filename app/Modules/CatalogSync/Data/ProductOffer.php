<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Data;

use App\Enums\ProductStockStatus;
use <PERSON><PERSON>\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Casts\EnumCast;
use Spatie\LaravelData\Data;

final class ProductOffer extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $sku,
        public readonly string $name,
        public readonly ?string $description,
        public readonly ?string $category,
        public readonly ?string $image,
        public readonly int $increments,
        public readonly ?string $brand,
        public readonly ?string $manufacturer,
        public readonly ?string $manufacturerSku,
        #[WithCast(EnumCast::class, ProductStockStatus::class)]
        public readonly ProductStockStatus $stockStatus,
        public readonly ProductPricing $pricing,
        public readonly ProductFlags $flags,
        public readonly array $attributes = [],
        public readonly ?string $nationalDrugCode = null,
        public readonly ?string $size = null,
        public readonly ?string $unitOfMeasure = null,
        public readonly ?string $rawCategory1 = null,
        public readonly ?string $rawCategory2 = null,
        public readonly ?string $rawCategory3 = null,
        public readonly ?string $rawCategory4 = null,
    ) {}
}
