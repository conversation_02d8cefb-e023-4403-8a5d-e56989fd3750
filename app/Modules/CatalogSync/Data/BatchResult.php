<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Data;

use App\Modules\CatalogSync\Enums\ResultBatchStatus;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Casts\EnumCast;
use Spatie\LaravelData\Data;

final class BatchResult extends Data
{
    public function __construct(
        public readonly string $taskId,
        #[WithCast(EnumCast::class, ResultBatchStatus::class)]
        public readonly ResultBatchStatus $status,
        public readonly ?string $statusReason,
        public readonly array $products,
    ) {}

    public function failed(): bool
    {
        return $this->status === ResultBatchStatus::Error;
    }
}
