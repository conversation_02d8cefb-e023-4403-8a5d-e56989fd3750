<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Models;

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Database\Factories\CatalogSyncTaskFactory;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;

final class CatalogSyncTask extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $guarded = [];

    protected $attributes = [
        'status' => CatalogSyncTaskStatus::Pending,
    ];

    protected $casts = [
        'status' => CatalogSyncTaskStatus::class,
        'scheduled_at' => 'datetime',
    ];

    public static function newFactory(): CatalogSyncTaskFactory
    {
        return CatalogSyncTaskFactory::new();
    }

    public function connection(): BelongsTo
    {
        return $this->belongsTo(IntegrationConnection::class, 'integration_connection_id');
    }

    public function clinic(): HasOneThrough
    {
        return $this->hasOneThrough(Clinic::class, IntegrationConnection::class, 'id', 'id', 'integration_connection_id', 'clinic_id');
    }

    public function vendor(): HasOneThrough
    {
        return $this->hasOneThrough(Vendor::class, IntegrationConnection::class, 'id', 'id', 'integration_connection_id', 'vendor_id');
    }

    public function batches(): HasMany
    {
        return $this->hasMany(CatalogSyncBatch::class);
    }
}
