<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Services;

use App\Modules\CatalogSync\Contracts\FetchClient;
use App\Modules\CatalogSync\Data\BatchResultMessage;
use App\Modules\CatalogSync\Data\FetchResultsQueueConfig;
use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;
use App\Modules\CatalogSync\Exceptions\DeleteBatchResultMessageException;
use App\Modules\CatalogSync\Exceptions\GetBatchResultMessagesException;
use App\Modules\CatalogSync\Exceptions\StartCatalogSyncException;
use Aws\Sqs\Exception\SqsException;
use Aws\Sqs\SqsClient;
use Illuminate\Support\Facades\Http;
use Throwable;

final class Fetch implements FetchClient
{
    private readonly SqsClient $sqs;

    public function __construct(
        private readonly string $endpoint,
        private readonly FetchResultsQueueConfig $queue,
    ) {
        $this->sqs = new SqsClient([
            'region' => $this->queue->region,
            'version' => '2012-11-05',
        ]);
    }

    /**
     * @throws StartCatalogSyncException
     */
    public function startCatalogSync(string $taskId, string $vendorId, array $credentials): void
    {
        $response = Http::retry([500, 1000, 5000])->post("{$this->endpoint}/api/scrape", [
            'taskId' => $taskId,
            'vendorId' => $vendorId,
            'credentials' => $credentials,
        ]);

        if ($response->failed()) {
            throw new StartCatalogSyncException($response->body());
        }
    }

    public function getCatalogSyncTaskStatus(string $taskId): CatalogSyncTaskStatus
    {
        $response = Http::retry([500, 1000, 5000])->get("{$this->endpoint}/api/taskStatus/{$taskId}");

        if ($response->failed()) {
            return CatalogSyncTaskStatus::Failed;
        }

        return CatalogSyncTaskStatus::from($response->json('status'));
    }

    /**
     * @return array<int, BatchResultMessage>
     *
     * @throws \App\Modules\CatalogSync\Exceptions\GetBatchResultsException
     */
    public function getBatchResultMessages(): array
    {
        try {
            $results = $this->sqs->receiveMessage([
                'QueueUrl' => $this->queue->url,
                'MaxNumberOfMessages' => 10,
                'WaitTimeSeconds' => 20,
            ]);

            return array_map(
                fn (array $message) => BatchResultMessage::from([
                    'id' => $message['ReceiptHandle'],
                    'body' => json_decode($message['Body'], true, 512, JSON_THROW_ON_ERROR),
                ]),
                $results->get('Messages') ?? [],
            );
        } catch (Throwable $e) {
            throw new GetBatchResultMessagesException($e->getMessage());
        }

    }

    /**
     * @throws DeleteBatchResultMessageException
     */
    public function deleteBatchResultMessage(string $messageId): void
    {
        try {
            $this->sqs->deleteMessage([
                'QueueUrl' => $this->queue->url,
                'ReceiptHandle' => $messageId,
            ]);
        } catch (SqsException $e) {
            throw new DeleteBatchResultMessageException($e->getMessage());
        }
    }
}
