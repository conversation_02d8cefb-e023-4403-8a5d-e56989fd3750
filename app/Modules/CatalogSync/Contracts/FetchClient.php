<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Contracts;

use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;

interface FetchClient
{
    public function startCatalogSync(string $taskId, string $vendorId, array $credentials): void;

    public function getCatalogSyncTaskStatus(string $taskId): CatalogSyncTaskStatus;

    public function getBatchResultMessages(): array;

    public function deleteBatchResultMessage(string $messageId): void;
}
