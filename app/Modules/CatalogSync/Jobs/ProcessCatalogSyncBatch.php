<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Jobs;

use App\Modules\CatalogSync\Actions\SyncProduct;
use App\Modules\CatalogSync\Contracts\FetchClient;
use App\Modules\CatalogSync\Data\ProductOffer;
use App\Modules\CatalogSync\Enums\CatalogSyncBatchStatus;
use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;
use App\Modules\CatalogSync\Models\CatalogSyncBatch;
use App\Modules\Integration\Actions\MarkIntegrationConnectionAsDisconnected;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\Skip;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

final class ProcessCatalogSyncBatch implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300;

    public $tries = 5;

    public function __construct(
        private readonly CatalogSyncBatch $batch,
    ) {}

    public function middleware(): array
    {
        return [
            Skip::when($this->batch->success()),
        ];
    }

    public function handle(
        FetchClient $fetch,
        SyncProduct $syncProduct,
        MarkIntegrationConnectionAsDisconnected $markIntegrationConnectionAsDisconnected,
    ): void {
        $this->batch->update([
            'status' => CatalogSyncBatchStatus::Processing,
            'status_reason' => null,
            'started_at' => now(),
        ]);

        /** @var \App\Models\Clinic $clinic */
        $clinic = $this->batch->task->clinic;

        /** @var \App\Models\Vendor $vendor */
        $vendor = $this->batch->task->vendor;

        foreach ($this->batch->message['products'] as $product) {
            try {
                $syncProduct->handle($clinic, $vendor, ProductOffer::from($product));
            } catch (Throwable $th) {
                Log::error('Failed to sync product', [
                    'taskId' => $this->batch->task->id,
                    'batchId' => $this->batch->id,
                    'product' => $product,
                    'error' => $th->getMessage(),
                ]);

                continue;
            }
        }

        $status = $fetch->getCatalogSyncTaskStatus($this->batch->task->id);

        if ($status === CatalogSyncTaskStatus::Failed) {
            $this->batch->task->update(['status' => CatalogSyncTaskStatus::Failed]);
            $markIntegrationConnectionAsDisconnected->handle(
                $this->batch->task->integration_connection_id,
                '**Authentication failed.** We couldn’t log in to your vendor account. Please verify that your credentials are correct and that you’re able to access the vendor platform directly using them.',
            );
        }

        if ($status === CatalogSyncTaskStatus::Succeeded) {
            $this->batch->task->update(['status' => CatalogSyncTaskStatus::Succeeded]);
            $this->batch->task->connection->removeAlert();
        }

        $this->batch->update([
            'status' => CatalogSyncBatchStatus::Completed,
            'finished_at' => now(),
        ]);
    }

    public function failed(Throwable $exception): void
    {
        $this->batch->update([
            'status' => CatalogSyncBatchStatus::Failed,
            'status_reason' => $exception->getMessage(),
            'finished_at' => now(),
        ]);
    }
}
