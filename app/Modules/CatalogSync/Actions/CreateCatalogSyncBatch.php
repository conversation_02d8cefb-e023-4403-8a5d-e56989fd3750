<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Actions;

use App\Modules\CatalogSync\Data\BatchResult;
use App\Modules\CatalogSync\Enums\CatalogSyncBatchStatus;
use App\Modules\CatalogSync\Jobs\ProcessCatalogSyncBatch;
use App\Modules\CatalogSync\Models\CatalogSyncTask;
use App\Modules\Integration\Actions\MarkIntegrationConnectionAsDisconnected;

final class CreateCatalogSyncBatch
{
    public function __construct(private readonly MarkIntegrationConnectionAsDisconnected $markIntegrationConnectionAsDisconnected) {}

    public function handle(BatchResult $result): void
    {
        $task = CatalogSyncTask::query()
            ->findOrFail($result->taskId);

        $batch = $task->batches()->create([
            'status' => $result->failed() ? CatalogSyncBatchStatus::Failed : CatalogSyncBatchStatus::Pending,
            'status_reason' => $result->failed() ? $result->statusReason : null,
            'message' => $result,
        ]);

        if ($result->failed()) {
            $this->markIntegrationConnectionAsDisconnected->handle(
                $task->integration_connection_id,
                '**Authentication failed.** We couldn’t log in to your vendor account. Please verify that your credentials are correct and that you’re able to access the vendor platform directly using them.',
            );
        }

        ProcessCatalogSyncBatch::dispatchIf($batch->pending(), $batch);
    }
}
