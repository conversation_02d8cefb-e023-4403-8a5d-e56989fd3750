<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Actions;

use App\Modules\CatalogSync\Models\CatalogSyncTask;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Jobs\UpdateCatalogSyncTaskStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Support\Carbon;

final class CreateCatalogSyncTask
{
    public function __construct(private StartCatalogSync $action) {}

    public function handle(string $clinicId, string $vendorId): void
    {
        $connection = IntegrationConnection::query()
            ->with(['clinic', 'vendor'])
            ->where('clinic_id', $clinicId)
            ->where('vendor_id', $vendorId)
            ->firstOrFail();

        if (! $connection->vendor->can(IntegrationPoint::SyncProductCatalog)) {
            return;
        }

        $task = CatalogSyncTask::create([
            'integration_connection_id' => $connection->id,
            'scheduled_at' => Carbon::now(),
        ]);

        $this->action->handle($task);

        UpdateCatalogSyncTaskStatus::dispatch($task);
    }
}
