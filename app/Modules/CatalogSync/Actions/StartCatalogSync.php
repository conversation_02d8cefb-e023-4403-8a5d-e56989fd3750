<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Actions;

use App\Modules\CatalogSync\Contracts\FetchClient;
use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;
use App\Modules\CatalogSync\Exceptions\StartCatalogSyncException;
use App\Modules\CatalogSync\Models\CatalogSyncTask;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Database\Eloquent\ModelNotFoundException;

final class StartCatalogSync
{
    public function __construct(private readonly FetchClient $fetch) {}

    public function handle(CatalogSyncTask $task): void
    {
        try {
            $connection = IntegrationConnection::query()
                ->whereIn('status', [IntegrationConnectionStatus::Connecting, IntegrationConnectionStatus::Connected])
                ->findOrFail($task->integration_connection_id);
        } catch (ModelNotFoundException $e) {
            $task->update([
                'status' => CatalogSyncTaskStatus::Failed,
                'status_reason' => 'The clinic does not have an active connection with the vendor',
            ]);

            return;
        }

        try {
            $this->fetch->startCatalogSync($task->id, $connection->vendor->slug, $connection->credentials);
        } catch (StartCatalogSyncException $e) {
            $task->update([
                'status' => CatalogSyncTaskStatus::Failed,
                'status_reason' => $e->getMessage(),
            ]);

            return;
        }

        $task->update(['status' => CatalogSyncTaskStatus::Running]);
    }
}
