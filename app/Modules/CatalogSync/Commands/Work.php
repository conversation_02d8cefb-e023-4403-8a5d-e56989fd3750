<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Commands;

use App\Modules\CatalogSync\Actions\CreateCatalogSyncBatch;
use App\Modules\CatalogSync\Contracts\FetchClient;
use App\Modules\CatalogSync\Exceptions\GetBatchResultMessagesException;
use Illuminate\Console\Command;
use Throwable;

final class Work extends Command
{
    protected $signature = 'catalog-sync:work';

    protected $description = 'Start processing the catalog sync results as a daemon';

    public function handle(FetchClient $fetch, CreateCatalogSyncBatch $action): void
    {
        $this->info('Starting catalog sync worker');

        while (true) {
            try {
                $messages = $fetch->getBatchResultMessages();
            } catch (GetBatchResultMessagesException $e) {
                $this->error("Failed to fetch batch result messages: {$e->getMessage()}");

                sleep(1);

                continue;
            }

            if (empty($messages)) {
                $this->line('No results messages. Waiting...');

                sleep(1);

                continue;
            }

            foreach ($messages as $message) {
                $result = $message->body;

                $this->line("Processing catalog sync batch [{$result->taskId}]");

                try {
                    $action->handle($result);
                } catch (Throwable $e) {
                    $this->error("Failed to process catalog sync batch [{$result->taskId}]: {$e->getMessage()}");

                    continue;
                }

                $fetch->deleteBatchResultMessage($message->id);
            }
        }
    }
}
