<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Commands;

use App\Modules\CatalogSync\Actions\CreateCatalogSyncTask;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

final class Start extends Command
{
    protected $signature = 'catalog-sync:start {--vendor=} {--clinic=}';

    protected $description = 'Start the catalog sync process';

    public function handle(CreateCatalogSyncTask $action)
    {
        $vendorId = $this->option('vendor');
        $clinicId = $this->option('clinic');

        $this->info('Starting the catalog sync process');

        $page = Carbon::now()->dayOfWeek() + 1;

        $query = IntegrationConnection::query()
            ->whereHas('vendor', function (Builder $query) {
                $query->whereJsonContains('integration_points', IntegrationPoint::SyncProductCatalog);
            })
            ->whereIn('status', [IntegrationConnectionStatus::Connecting, IntegrationConnectionStatus::Connected])
            ->whereNotNull('credentials');

        if ($vendorId) {
            $query->where('vendor_id', $vendorId);
        }

        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }

        $count = $query->count();

        $this->line("There are {$count} clinic-vendors connections");

        $connections = $vendorId || $clinicId ? $query->get() : $query->paginate($count / 3, page: $page);

        foreach ($connections as $connection) {
            $action->handle($connection->clinic_id, $connection->vendor_id);
        }

        $this->info("The catalog sync process has started for {$connections->count()} clinic-vendors connections");
    }
}
