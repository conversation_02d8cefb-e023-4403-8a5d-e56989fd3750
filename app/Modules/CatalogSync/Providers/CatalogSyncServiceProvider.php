<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Providers;

use App\Modules\CatalogSync\Commands\Start;
use App\Modules\CatalogSync\Commands\Work;
use App\Modules\CatalogSync\Contracts\FetchClient;
use App\Modules\CatalogSync\Data\FetchResultsQueueConfig;
use App\Modules\CatalogSync\Services\Fetch;
use Illuminate\Support\ServiceProvider;

final class CatalogSyncServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->commands([
            Start::class,
            Work::class,
        ]);
    }

    public function register(): void
    {
        $this->app->bind(FetchClient::class, fn () => new Fetch(
            config('highfive.catalog_sync.service_endpoint'),
            FetchResultsQueueConfig::from([
                'region' => config('highfive.catalog_sync.results_queue_region'),
                'url' => config('highfive.catalog_sync.results_queue_url'),
            ]),
        ));
    }
}
