<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Listeners;

use App\Modules\CatalogSync\Actions\CreateCatalogSyncTask;
use App\Modules\Integration\Events\ConnectingIntegration;
use Illuminate\Contracts\Queue\ShouldQueue;

final class RunCatalogSync implements ShouldQueue
{
    public function __construct(private CreateCatalogSyncTask $action) {}

    public function handle(ConnectingIntegration $event): void
    {
        $this->action->handle($event->clinicId, $event->vendorId);
    }
}
