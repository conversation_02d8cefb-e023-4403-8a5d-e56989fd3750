<?php

declare(strict_types=1);

namespace App\Modules\User\Actions;

use App\Models\Clinic;
use App\Modules\Account\Enums\ClinicAccountRole;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

final readonly class StartImpersonateUserByClinic
{
    public function handle(Clinic $clinic): void
    {
        DB::transaction(function () use ($clinic) {
            $systemUser = $clinic->account->users()->firstOrCreate(['email' => "hello+system-user-{$clinic->account->id}@highfive.vet"], [
                'name' => "System User for Account {$clinic->account->id}",
                'password' => Hash::make(Str::password(32, true, true, true, false)),
                'account_id' => $clinic->account->id,
                'is_system_user' => true,
            ]);
            $systemUser->assignRole(ClinicAccountRole::Owner);

            Auth::user()->impersonate($systemUser);
        });
    }
}
