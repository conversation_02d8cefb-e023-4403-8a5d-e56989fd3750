<?php

declare(strict_types=1);

namespace App\Modules\User\Http\Controllers;

use App\Modules\User\Actions\StopImpersonateUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class StopImpersonateUserController
{
    public function __construct(
        private readonly StopImpersonateUser $stopImpersonateUser,
    ) {}

    public function __invoke(Request $request): JsonResponse
    {
        $this->stopImpersonateUser->handle();

        return response()->json([
            'message' => 'Impersonation stopped',
            'redirectUri' => route('nova.pages.index', ['resource' => 'clinics']),
        ]);
    }
}
