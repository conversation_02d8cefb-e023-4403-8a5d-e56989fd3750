<?php

declare(strict_types=1);

namespace App\Modules\User\Enums;

use App\Enums\EnumMethods;

enum UserRole: string
{
    use EnumMethods;

    case Owner = 'user:owner';
    case Executive = 'user:executive';
    case RegionalManager = 'user:regional-manager';
    case ClinicManager = 'user:clinic-manager';
    case InventoryManager = 'user:inventory-manager';
    case Purchaser = 'user:purchaser';
    case BookKeeper = 'user:bookkeeper';
}
