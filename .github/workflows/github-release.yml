name: GitHub Release

on:
  workflow_dispatch:

jobs:
  release:
    runs-on: ubuntu-latest

    permissions:
      contents: write

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          tags: true

      - name: Check if HEAD already has a tag
        id: tagcheck
        run: |
          tags=$(git tag --points-at HEAD)
          if [[ -n "$tags" ]]; then
            echo "HEAD already has tag(s): $tags"
            echo "skip=true" >> $GITHUB_OUTPUT
          else
            echo "skip=false" >> $GITHUB_OUTPUT
          fi

      - name: Stop if HEAD is already tagged
        if: steps.tagcheck.outputs.skip == 'true'
        run: |
          echo "🔖 HEAD is already tagged; skipping release."

      - name: Set up date variables
        if: steps.tagcheck.outputs.skip == 'false'
        id: date
        run: |
          today=$(date +'%Y%m.%d')
          echo "today=$today" >> $GITHUB_OUTPUT

      - name: Get latest tag for today
        if: steps.tagcheck.outputs.skip == 'false'
        id: latest
        run: |
          prefix="v${{ steps.date.outputs.today }}"
          tags=$(git tag -l "${prefix}.*" | sort -V)

          if [[ -z "$tags" ]]; then
            n=0
          else
            last_tag=$(echo "$tags" | tail -n 1)
            last_n=$(echo "$last_tag" | awk -F. '{print $3}')
            n=$((last_n + 1))
          fi

          new_tag="${prefix}.${n}"
          echo "new_tag=$new_tag" >> $GITHUB_OUTPUT

      - name: Create and push tag
        if: steps.tagcheck.outputs.skip == 'false'
        run: |
          git config user.name "github-actions"
          git config user.email "<EMAIL>"
          git tag ${{ steps.latest.outputs.new_tag }}
          git push origin ${{ steps.latest.outputs.new_tag }}

      - name: Create GitHub release
        if: steps.tagcheck.outputs.skip == 'false'
        uses: actions/create-release@v1
        with:
          tag_name: ${{ steps.latest.outputs.new_tag }}
          release_name: ${{ steps.latest.outputs.new_tag }}
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
