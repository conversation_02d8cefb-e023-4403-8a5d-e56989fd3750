<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_offers', function (Blueprint $table) {
            $table->string('raw_category_1')->nullable();
            $table->string('raw_category_2')->nullable();
            $table->string('raw_category_3')->nullable();
            $table->string('raw_category_4')->nullable();
        });
    }
};
