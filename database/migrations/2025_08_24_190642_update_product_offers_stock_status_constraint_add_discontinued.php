<?php

declare(strict_types=1);

use App\Enums\ProductStockStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the old constraint
        DB::statement('ALTER TABLE public.product_offers DROP CONSTRAINT IF EXISTS products_stock_status_check');

        // Get all enum values including the new DISCONTINUED
        $enumValues = ProductStockStatus::values();

        // Build the constraint array dynamically
        $constraintArray = array_map(function ($value) {
            return "('{$value}'::character varying)::text";
        }, $enumValues);

        $constraintArrayString = implode(', ', $constraintArray);

        // Add the new constraint with all enum values
        DB::statement("ALTER TABLE public.product_offers ADD CONSTRAINT products_stock_status_check CHECK (((stock_status)::text = ANY (ARRAY[{$constraintArrayString}])))");
    }
};
