<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('reassign_swap_set_task_events', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('reassign_swap_set_task_id')->constrained('reassign_swap_set_tasks');
            $table->string('type');
            $table->json('meta');
            $table->timestamps();
        });
    }
};
