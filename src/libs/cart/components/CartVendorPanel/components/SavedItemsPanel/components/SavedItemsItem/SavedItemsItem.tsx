import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useRemoveSavedItem } from '@/libs/cart/hooks/useRemoveSavedItem';
import { Icon } from '@/libs/icons/Icon';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { Button } from '@/libs/ui/Button/Button';
import { SavedItemType } from '@/types';
import { getPriceString } from '@/utils';
import { Tooltip } from '@mantine/core';

interface SavedItemsItemProps {
  savedItem: SavedItemType;
}

export const SavedItemsItem = ({ savedItem }: SavedItemsItemProps) => {
  const { mutate: handleDeleteFromSavedItems } = useRemoveSavedItem();
  const { addToCart } = useCartStore();

  const handleMoveToCart = (savedItem: SavedItemType) => {
    addToCart({
      offers: [
        {
          productOfferId: savedItem.productOffer.id,
          quantity: savedItem.quantity,
        },
      ],
      onError: (message: string) => {
        console.error('Failed to add item to cart:', message);
      },
    });

    handleDeleteFromSavedItems(savedItem.id);
  };

  const { originalPrice, salePrice } = getProductOfferComputedData(
    savedItem.productOffer,
  );

  return (
    <div className="mb-2 flex items-center justify-between border-b border-black/[0.04] pb-2 [&:last-child]:m-0 [&:last-child]:border-none [&:last-child]:p-0">
      <div className="flex items-center gap-2">
        <div className="flex h-5 w-7 items-center justify-center rounded-sm bg-[#E5FCFD] text-[10px] font-medium">
          {savedItem.quantity}
        </div>
        <div className="relative h-12 w-12 rounded-md border-1 border-black/[0.04]">
          <img
            src={savedItem.productOffer.product.imageUrl || ''}
            alt={savedItem.productOffer.product.name}
            className="absolute inset-0 h-full w-full object-cover"
          />
        </div>
        <div>
          <h5 className="text-sm font-medium">
            {savedItem.productOffer.product.name}
          </h5>
          <p className="text-xs">
            {originalPrice > salePrice ? (
              <>
                <span className="mr-1 text-[#666666] line-through">
                  {getPriceString(originalPrice)}
                </span>
                <span className="font-medium text-[#333]">
                  {getPriceString(salePrice)}
                </span>
              </>
            ) : (
              <span className="font-medium text-[#333]">
                {getPriceString(salePrice)}
              </span>
            )}
          </p>
        </div>
      </div>
      <div className="flex items-center gap-4">
        <Tooltip label="Remove Item from saved items list">
          <Button
            variant="unstyled"
            onClick={() => handleDeleteFromSavedItems(savedItem.id)}
            arial-label="Remove Item from saved items list"
          >
            <Icon name="trash" color="#667085" size="1rem" aria-hidden />
          </Button>
        </Tooltip>
        <div className="h-4 w-px bg-[#D0D5DD]" />
        <Tooltip label="Move Item to cart">
          <Button
            className="flex items-center gap-1"
            arial-label="Move Item to cart"
            onClick={() => handleMoveToCart(savedItem)}
          >
            <Icon name="cart" color="#667085" size="1.25rem" />
            <Icon name="arrowUp" color="#667085" size="0.875rem" />
          </Button>
        </Tooltip>
      </div>
    </div>
  );
};
