import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import type { CartItemType, CartVendorType } from '@/libs/cart/types';
import { getPriceString } from '@/utils';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import CutoffIcon from '@/assets/images/cart/clock.svg?react';
import { getSubtotalItemsText } from '@/libs/cart/utils/getSubtotalItemsText';
import styles from './CartVendorPanel.module.css';
import clsx from 'clsx';
import { CartVendorProductItem } from '@/apps/shop/pages/Cart/components/CartVendorProductItem/CartVendorProductItem';
import { SavedItemsPanel } from './components/SavedItemsPanel/SavedItemsPanel';
import { FEATURE_FLAGS } from '@/constants';
import { CartVendorPromoItem } from '@/apps/shop/pages/Cart/components/CartVendorPromoItem/CartVendorPromoItem';
import { SavedItemType } from '@/types';
import { Button } from '@/libs/ui/Button/Button';
import { getPromotionData } from '@/libs/promotions/utils/getPromotionData';

type CartVendorPanelProps = {
  vendor: CartVendorType & {
    savedItems: SavedItemType[];
  };
};

export const CartVendorPanel = (props: CartVendorPanelProps) => {
  const {
    vendor: {
      imageUrl,
      name,
      shippingFee,
      subtotal,
      items,
      amountToFreeShipping,
      cutoffTime,
      savedItems,
    },
  } = props;

  const isShippingFee = shippingFee ? +shippingFee === 0 : false;

  const { promotions, nonPromotionItems } = getPromotionData(items);

  const { gpoItems, otherItems } = nonPromotionItems.reduce<{
    gpoItems: CartItemType[];
    otherItems: CartItemType[];
    // gpoSavings: number;
    // vendorSavings: number;
  }>(
    (acc, item: CartItemType) => {
      const offer = item.product.offers.find(
        ({ id }) => id === item.productOfferId,
      )!;
      const { isRecommended } = offer;
      // const { gpoSavings, vendorSavings } = getProductOfferComputedData(offer);

      if (isRecommended) {
        acc.gpoItems.push(item);
      } else {
        acc.otherItems.push(item);
      }

      // acc.gpoSavings += gpoSavings;
      // acc.vendorSavings += vendorSavings;

      return acc;
    },
    {
      gpoItems: [],
      otherItems: [],
      // gpoSavings: 0,
      // vendorSavings: 0,
    },
  );

  return (
    <CollapsiblePanel
      header={
        <div className="flex items-center pr-12">
          <img
            src={imageUrl || defaultProductImgUrl}
            alt={name}
            className="mr-4 h-[42px] object-cover"
            title={name}
            onError={(e) => {
              e.currentTarget.src = defaultProductImgUrl;
            }}
          />

          <div className="flex items-center gap-3">
            <div className="flex items-end leading-none">
              <span className="mr-2 font-medium">
                {subtotal ? getPriceString(subtotal) : ''}
              </span>
              {/* <span
                  className="font-medium text-xs text-gray-400 line-through"
                >
                  {getPriceString(subtotal)}
                </span> */}
            </div>

            {subtotal && <div className="divider-v mx-0" />}

            {items?.length > 0 && (
              <div>
                <span className="text-sm font-normal text-gray-500">
                  {getSubtotalItemsText(items.length)}
                </span>
              </div>
            )}

            {amountToFreeShipping &&
            shippingFee &&
            (+amountToFreeShipping > 0 || +shippingFee === 0) ? (
              <>
                <div className="divider-v mx-0" />
                <div
                  className={clsx(styles.infoCell, {
                    [styles.warning]: !isShippingFee,
                  })}
                >
                  {isShippingFee ? (
                    <span className="rounded-md bg-[#CEEDC2] px-2 py-1 text-sm font-normal text-[#225A0F]">
                      Shipping free
                    </span>
                  ) : (
                    <span className="text-xs">
                      Add{' '}
                      <span className="text-sm font-medium">
                        {amountToFreeShipping}
                      </span>{' '}
                      and get{' '}
                      <span className="text-sm font-medium">Free Shipping</span>
                    </span>
                  )}
                </div>
              </>
            ) : null}

            {cutoffTime && (
              <>
                <div className="divider-v mx-0" />
                <div className={styles.infoCell}>
                  <div className="flex items-center">
                    <CutoffIcon />
                    <span className="ml-1 text-xs">{cutoffTime}</span>
                  </div>
                </div>
              </>
            )}

            {FEATURE_FLAGS.SAVED_ITEMS && items && items.length > 0 && (
              <>
                <div className="divider-v mx-0" />

                <Button
                  variant="unstyled"
                  onClick={() => {
                    // TODO: Missing BE implementation
                  }}
                >
                  <span className="text-xs font-medium text-[#447bfd]">
                    Save for later
                  </span>
                </Button>
              </>
            )}
          </div>
        </div>
      }
      content={
        <>
          <div className="flex flex-col gap-4 divide-y divide-gray-200 p-4">
            {[...gpoItems, ...otherItems].map((cartItem) => (
              <CartVendorProductItem
                key={cartItem.productOfferId}
                data={cartItem}
              />
            ))}
            {promotions.buy_x_get_y && (
              <CartVendorPromoItem promoItem={promotions.buy_x_get_y} />
            )}
          </div>
          {savedItems.length > 0 && (
            <div className="mb-4 p-4">
              <SavedItemsPanel
                savedItems={savedItems}
                startOpen={
                  savedItems.length > 0 && (!items || items.length === 0)
                }
              />
            </div>
          )}
        </>
      }
      startOpen
    />
  );
};
