.vendorSectionRoot {
  --item-layout: 80px minmax(200px, 1fr) minmax(405px, 1fr);
  --item-column-gap: 1rem;
  --item-min-width: 600px;
  --item-padding: 1rem 0;

  display: flex;
  flex-direction: column;
  border-radius: 0.5rem;
  border: 1px solid var(--mantine-color-dark-4);
  overflow: hidden;

  &[data-active='true'] {
    .vendorLogo {
      border-radius: 0.5rem 0 0 0;
    }

    :global(.mantine-Accordion-chevron) {
      svg {
        transform: rotate(180deg);
      }
    }
  }

  &:not(:last-child) {
    margin-bottom: 1rem;
  }

  :global(.mantine-Accordion-control) {
    border: none;
    padding: 0;
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row-reverse;
    background-color: transparent;
  }

  :global(.mantine-Accordion-label) {
    width: 100%;
  }

  .vendorHeader {
    display: flex;
    width: 100%;
    height: 46px;
    border-radius: 0.5rem 0.5rem 0 0;
    background-color: rgba(0, 0, 0, 2%);
  }

  .vendorLogo {
    border-radius: 0.5rem 0 0 0.5rem;
    height: 100%;
    transition: border-radius 0.3s;
    margin-right: 0.75rem;
    max-width: 95px;
    width: 100%;
    flex: auto;
  }

  .vendorInfo {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    column-gap: 1rem;
  }

  .infoCell {
    display: flex;
    gap: 0.625rem;
    padding: 0 0.625rem;
  }

  .warning {
    svg {
      circle,
      path {
        stroke: var(--mantine-color-orange-2);
      }
    }
  }
}
