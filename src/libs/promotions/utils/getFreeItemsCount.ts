import { BenefitType, PromoType } from '@/types/common';
import { getMinimumQuantity } from './getMinimumQuantity';

export const getFreeItemsCount = ({
  requirements,
  quantity,
  freeBenefit,
}: {
  requirements: PromoType['requirements'];
  quantity: number;
  freeBenefit: BenefitType;
}) => {
  const minimumQuantity = getMinimumQuantity(requirements);
  const triggers = Math.floor(quantity / minimumQuantity);
  return triggers * freeBenefit?.quantity;
};
