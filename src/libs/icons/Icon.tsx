import React from 'react';
import AlertIcon from './assets/alert.svg?react';
import ArrowUpIcon from './assets/arrow-up.svg?react';
import BackorderIcon from './assets/backorder.svg?react';
import BankNoteIcon from './assets/bank-note.svg?react';
import CartIcon from './assets/cart.svg?react';
import CartSummaryIcon from './assets/cart-summary.svg?react';
import ClockIcon from './assets/clock.svg?react';
import DownloadIcon from './assets/download.svg?react';
import DollarIcon from './assets/dollar.svg?react';
import ExitIcon from './assets/exit.svg?react';
import FavoriteIcon from './assets/favorite.svg?react';
import FavoriteSolidIcon from './assets/favorite-solid.svg?react';
import HomeIcon from './assets/home.svg?react';
import MagnifierIcon from './assets/magnifier.svg?react';
import MinusIcon from './assets/minus.svg?react';
import MoreOptionsIcon from './assets/more-options.svg?react';
import MultiplyIcon from './assets/multiply.svg?react';
import PlusIcon from './assets/plus.svg?react';
import SaveFlagIcon from './assets/save-flag.svg?react';
import SettingsIcon from './assets/settings.svg?react';
import SortIcon from './assets/sort.svg?react';
import TrashIcon from './assets/trash.svg?react';

const iconComponents = {
  alert: AlertIcon,
  arrowUp: ArrowUpIcon,
  backorder: BackorderIcon,
  bankNote: BankNoteIcon,
  cart: CartIcon,
  cartSummary: CartSummaryIcon,
  clock: ClockIcon,
  download: DownloadIcon,
  dollar: DollarIcon,
  favorite: FavoriteIcon,
  favoriteSolid: FavoriteSolidIcon,
  exit: ExitIcon,
  home: HomeIcon,
  magnifier: MagnifierIcon,
  minus: MinusIcon,
  moreOptions: MoreOptionsIcon,
  multiply: MultiplyIcon,
  plus: PlusIcon,
  saveFlag: SaveFlagIcon,
  settings: SettingsIcon,
  sort: SortIcon,
  trash: TrashIcon,
} as const;

type IconName = keyof typeof iconComponents;

interface IconProps extends React.SVGProps<SVGSVGElement> {
  name: IconName;
  size?: string | number;
  color?: string;
  className?: string;
  'aria-label'?: string;
}

export const Icon: React.FC<IconProps> = ({
  name,
  size = '1rem',
  color = 'currentColor',
  className,
  ...restProps
}) => {
  const IconComponent = iconComponents[name];

  if (!IconComponent) {
    console.warn(
      `Icon "${name}" not found. Available icons:`,
      Object.keys(iconComponents),
    );
    return null;
  }

  return (
    <IconComponent
      width={size}
      height={size}
      color={color}
      className={className}
      {...restProps}
    />
  );
};
