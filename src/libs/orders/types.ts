import type { OfferType } from '@/types';

export type ShippingStatusType =
  | 'PENDING'
  | 'PLACEMENT_FAILED'
  | 'ACCEPTED'
  | 'BACKORDERED'
  | 'SHIPPED'
  | 'REJECTED'
  | 'RETURNED'
  | 'DELIVERED'
  | 'CANCELLED'
  | 'PROCESSING'
  | 'PARTIALLY_SHIPPED'
  | 'PARTIALLY_DELIVERED'
  | 'NON_TRACKABLE';

export type OrderHistoryItemType = {
  backorderedItemsCount?: number;
  date: string;
  id: string;
  itemsCount: number;
  orderNumber: string;
  status: ShippingStatusType;
  totalPrice: string;
  vendorsCount: number;
};

export type OrderHistoryDetailItemType = {
  id: string;
  product: {
    id: string;
    name: string;
    imageUrl: string;
    offers: OfferType[];
  };
  productOfferId: string;
  quantity: number;
  status: ShippingStatusType;
  totalPrice: string;
  unitPrice: string;
  taxFee: string | null;
  orderNumber: string;
};

export type OrderHistoryPromotionTriggeringItem = {
  productOfferId: string;
  productId: string;
  quantity: number;
};

export type OrderHistoryPromotionRuleCondition = {
  id: string;
  type: string;
  description: string;
  config: Record<string, string>;
};

export type OrderHistoryPromotionRuleAction = {
  id: string;
  type: string;
  description: string;
  config: Record<string, number>;
};

export type OrderHistoryPromotionAppliedRule = {
  ruleId: string;
  priority: number;
  conditions: OrderHistoryPromotionRuleCondition[];
  actions: OrderHistoryPromotionRuleAction[];
};

export type OrderHistoryPromotionAppliedBenefit = {
  type: 'give_free_product';
  quantity: string;
  productOffer: {
    clinicPrice: string | null;
    id: string;
    imageUrl: string | null;
    name: string;
    price: number | null;
    unitOfMeasure: string | null;
    size: string | null;
  };
  productOfferId: string | null;
  freeOffer?: OfferType;
  description?: string;
  message?: string | null;
};

export type OrderHistoryPromotion = {
  id: string;
  name: string;
  type: 'buy_x_get_y';
  triggeringItems: OrderHistoryPromotionTriggeringItem[];
  appliedRules: OrderHistoryPromotionAppliedRule[];
  appliedBenefits: OrderHistoryPromotionAppliedBenefit[];
  vendor: {
    id: string;
    name: string;
    imageUrl: string;
  };
};

export type OrderHistoryPromotionData = {
  subtotalPaidItems: number;
  subtotalAllItems: number;
  paidItemsQty: number;
  freeItemsQty: number;
  freeOffer: OfferType | null;
  promotion: OrderHistoryPromotion;
  items: OrderHistoryDetailItemType[];
  imageUrl: string;
  manufacturer: string;
};

export type PromotionGroup = {
  promotion: OrderHistoryPromotion;
  items: OrderHistoryDetailItemType[];
  freeItemsQty: number;
  paidItemsQty: number;
  subtotalPaidItems: number;
  subtotalAllItems: number;
};

export type VendorPromotionGroups = {
  buy_x_get_y?: PromotionGroup[] | null;
  untriggeredItems: OrderHistoryDetailItemType[];
};

export type OrderHistoryDetailVendorOrderType = {
  id: string;
  items: OrderHistoryDetailItemType[];
  totalPrice: string;
  totalTaxFee: string | null;
  shippingFee: string | null;
  vendor: {
    id: string;
    name: string;
    imageUrl: string;
  };
  promotionData?: OrderHistoryPromotionData;
};

export type OrderHistoryDetailsType = {
  date: string;
  id: string;
  vendorOrders: OrderHistoryDetailVendorOrderType[];
  orderNumber: string;
  status: ShippingStatusType;
  totalPrice: string;
  downloadChecklistUrl: string;
  downloadInvoicesUrl: string | null;
  promotions?: OrderHistoryPromotion[];
};
