import { ProductType } from '@/types';
import { Divider, Image, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { getPriceString } from '@/utils';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useState, type FormEventHandler } from 'react';
import {
  AddToCartInput,
  type AddToCartInputProps,
} from '../AddToCartInput/AddToCartInput';
import { AddToCartButton } from '../AddToCartForm/components/AddToCartButton/AddToCartButton';
import styles from './ProductSuggestedItem.module.css';

type ProductSubstitutesItemProps = {
  product: ProductType;
};

export const ProductSuggestedItem = ({
  product,
}: ProductSubstitutesItemProps) => {
  const {
    id: productOfferId,
    price,
    vendor,
    vendorSku,
    increments,
  } = product.offers[0];
  const { addToCart, offersMapData } = useCartStore();
  const [amount, setAmount] = useState(increments ?? 1);

  const { isLoading = false, quantity = 0 } =
    offersMapData[productOfferId] ?? {};
  const handleQuantityUpdate: AddToCartInputProps['onUpdate'] = ({
    amount: newAmount,
  }) => {
    setAmount(newAmount);
  };

  const handleAddToCartClick: FormEventHandler<HTMLFormElement> = (event) => {
    event.preventDefault();
    addToCart({
      offers: [
        {
          productOfferId,
          quantity: quantity + amount,
        },
      ],
      onError: () => {},
    });
  };

  return (
    <Flex
      py="1.5rem"
      px="1rem"
      justify="space-between"
      className={styles.container}
    >
      <div>
        <Text className={styles.title}>{product.name}</Text>
        <Flex mt="4" align="center" gap="0.5rem">
          <Image src={vendor?.imageUrl} alt={vendor?.name} w="50" />
          <Text>SKU: {vendorSku}</Text>
          <Divider orientation="vertical" />
        </Flex>
      </div>
      <Flex gap="10px" align="center">
        <Text>{getPriceString(price)}</Text>
        <form onSubmit={handleAddToCartClick}>
          <Flex gap="12px" flex="grow" align="center">
            <div style={{ maxWidth: '120px' }}>
              <AddToCartInput
                originalAmount={increments}
                minIncrement={increments}
                onUpdate={handleQuantityUpdate}
              />
            </div>
            <AddToCartButton
              quantityInCart={quantity}
              isLoading={isLoading}
              fullWidth
              onlyIcon
            />
          </Flex>
        </form>
      </Flex>
    </Flex>
  );
};
