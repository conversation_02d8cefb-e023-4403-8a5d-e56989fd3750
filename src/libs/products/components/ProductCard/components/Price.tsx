import { PromoType } from '@/types/common';
import { getPriceString } from '@/utils/stringUtils';
import { Dollar } from '../../Dollar/Dollar';
import { NetPriceRebateIcon } from '../../NetPriceRebateIcon/NetPriceRebateIcon';
import { PROMO_TYPE } from '@/constants';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { OfferType } from '@/types';

type PriceType = {
  offer: OfferType;
  promotions?: PromoType[];
};

export const Price = ({ offer, promotions }: PriceType) => {
  const { hasRebate, originalPrice, salePrice } =
    getProductOfferComputedData(offer);

  return (
    <div className="mb-1 flex items-center gap-2">
      {originalPrice > salePrice ? (
        <div className="flex items-end gap-2">
          <span className="text-2xl font-medium">
            {getPriceString(salePrice)}
          </span>

          <span className="text-sm font-medium text-gray-500 line-through">
            {getPriceString(originalPrice)}
          </span>
        </div>
      ) : (
        <span className="text-2xl font-medium">
          {getPriceString(salePrice)}
        </span>
      )}
      {hasRebate && <NetPriceRebateIcon />}
      {!!promotions?.length && (
        <Dollar
          toolTipLabel={`Promotion Type: ${PROMO_TYPE[promotions[0].type]}`}
        />
      )}
    </div>
  );
};
