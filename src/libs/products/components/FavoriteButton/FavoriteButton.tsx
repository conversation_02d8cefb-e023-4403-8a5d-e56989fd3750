import { Button } from '@/components';
import { useProductStore } from '@/apps/shop/stores/useProductStore/useProductStore';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { useEffect, useState } from 'react';
import { Icon } from '@/libs/icons/Icon';
type FavoriteButtonProps = {
  productId: string;
  isFavorite: boolean;
};

export const FavoriteButton = ({
  productId,
  isFavorite: isFavoriteProp,
}: FavoriteButtonProps) => {
  const [isFavorite, setIsFavorite] = useState(isFavoriteProp);
  const { addToFavorite, removeToFavorite } = useProductStore();

  const { apiRequest: handleToggleFavorite, isLoading: isFavoriteLoading } =
    useAsyncRequest({
      apiFunc: async () => {
        if (isFavorite) {
          const itWasRemoved = await removeToFavorite(productId);
          setIsFavorite(!itWasRemoved);
        } else {
          const itWasAdded = await addToFavorite(productId);
          setIsFavorite(itWasAdded);
        }
      },
    });

  useEffect(() => {
    setIsFavorite(isFavoriteProp);
  }, [isFavoriteProp]);

  return (
    <Button
      loading={isFavoriteLoading}
      onClick={handleToggleFavorite}
      variant="transparent"
      size="sm"
      p="2"
      iconOnly
    >
      {isFavorite ? (
        <Icon name="favoriteSolid" color="red" />
      ) : (
        <Icon name="favorite" color="black" />
      )}
    </Button>
  );
};
