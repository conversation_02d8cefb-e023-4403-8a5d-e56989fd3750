import { useCallback, useState, useEffect } from 'react';
import { fetchApi } from '@/libs/utils/api';

export interface MoniteEntityUserTokenResponse {
  accessToken: string;
  tokenType: string;
  expiresIn: number;
  entityId: string;
  entityUserId: string;
}

export interface UseMoniteTokenProps {
  clinicId: string;
}

export interface UseMoniteTokenReturn {
  token: MoniteEntityUserTokenResponse | undefined;
  isLoading: boolean;
  hasError: boolean;
  fetchToken: () => Promise<void>;
  error: string | null;
}

export const useMoniteToken = ({
  clinicId,
}: UseMoniteTokenProps): UseMoniteTokenReturn => {
  const [token, setToken] = useState<MoniteEntityUserTokenResponse | undefined>(
    undefined,
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasError, setHasError] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchToken = useCallback(async () => {
    if (!clinicId) return;

    setIsLoading(true);
    setHasError(false);
    setError(null);

    try {
      const result = await fetchApi<MoniteEntityUserTokenResponse>(
        '/monite/entity-user-token',
        {
          method: 'POST',
          withApi: true,
          authStrategy: 'cookie',
          options: {
            headers: {
              'Highfive-Clinic': clinicId,
            },
          },
        },
      );
      setToken(result);
    } catch (err: unknown) {
      setHasError(true);
      if (err && typeof err === 'object' && 'data' in err) {
        const apiError = err as { data?: { message?: string } };
        setError(apiError.data?.message || 'Failed to fetch Monite token');
      } else {
        setError('Failed to fetch Monite token');
      }
    } finally {
      setIsLoading(false);
    }
  }, [clinicId]);

  // Auto-fetch token when component mounts
  useEffect(() => {
    fetchToken();
  }, [fetchToken]);

  return {
    token,
    isLoading,
    hasError,
    fetchToken,
    error,
  };
};
