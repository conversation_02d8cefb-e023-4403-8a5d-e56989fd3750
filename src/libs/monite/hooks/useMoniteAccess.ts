import { useIsMoniteEnabled } from './useMoniteFeatureFlag';

/**
 * Hook to check if the current clinic has access to Monite features
 * This is a convenience hook that can be used throughout the app to conditionally
 * show/hide Monite-related UI elements like navigation items, buttons, etc.
 */
export const useMoniteAccess = (clinicId?: string) => {
  const { isMoniteEnabled, isLoading, error } = useIsMoniteEnabled(clinicId);

  return {
    // Main flag to check if Monite features should be shown
    hasMoniteAccess: isMoniteEnabled,

    // Loading state - useful for showing loading indicators
    isCheckingAccess: isLoading,

    // Error state - useful for error handling
    accessCheckError: error,

    // Utility function to check if invoices should be shown
    shouldShowInvoices: isMoniteEnabled && !isLoading && !error,

    // Utility function to check if any Monite features should be shown
    shouldShowMoniteFeatures: isMoniteEnabled && !isLoading && !error,
  };
};

/**
 * Hook specifically for navigation components to determine if
 * Monite-related menu items should be visible
 */
export const useMoniteNavigation = (clinicId?: string) => {
  const { hasMoniteAccess, isCheckingAccess } = useMoniteAccess(clinicId);

  return {
    // Show invoices menu item
    showInvoicesMenuItem: hasMoniteAccess,

    // Show any loading indicators in navigation
    isLoadingAccess: isCheckingAccess,

    // Check if navigation should be updated
    shouldUpdateNavigation: !isCheckingAccess,
  };
};
