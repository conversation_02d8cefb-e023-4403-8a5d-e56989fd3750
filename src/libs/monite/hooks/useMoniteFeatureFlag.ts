import { useQuery } from '@tanstack/react-query';
import { fetchApi } from '@/libs/utils/api';

export interface MoniteFeatureFlagResponse {
  clinic_id: string;
  clinic_name: string;
  monite_enabled: boolean;
  // Support camelCase transformation (common in frontend APIs)
  clinicId?: string;
  clinicName?: string;
  moniteEnabled?: boolean;
}

interface UseMoniteFeatureFlagOptions {
  clinicId?: string;
  enabled?: boolean;
}

export const useMoniteFeatureFlag = ({
  clinicId,
  enabled = true,
}: UseMoniteFeatureFlagOptions = {}) => {
  return useQuery({
    queryKey: ['monite-feature-flag', clinicId],
    queryFn: async (): Promise<MoniteFeatureFlagResponse> => {
      if (!clinicId) {
        throw new Error('Clinic ID is required to check Monite feature flag');
      }

      const response = await fetchApi<MoniteFeatureFlagResponse>(
        '/feature-flags/check-monite',
        {
          method: 'POST',
          withApi: true,
          authStrategy: 'cookie',
        },
      );

      return response;
    },
    enabled: enabled && !!clinicId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

// Utility hook that combines feature flag check with easy access
export const useIsMoniteEnabled = (clinicId?: string) => {
  const { data, isLoading, error } = useMoniteFeatureFlag({ clinicId });

  // Handle both snake_case (backend) and camelCase (frontend transformation)
  const isEnabled = data?.monite_enabled ?? data?.moniteEnabled ?? false;

  return {
    isMoniteEnabled: isEnabled,
    isLoading,
    error,
    clinicId: data?.clinic_id || data?.clinicId,
    clinicName: data?.clinic_name || data?.clinicName,
  };
};
