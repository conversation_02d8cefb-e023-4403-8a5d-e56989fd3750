import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Popover } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import { Input, InputProps } from '@/libs/form/Input';
import { Icon } from '@/libs/icons/Icon';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';

import { AddToCartButton } from '@/libs/ui/AddToCartButton/AddToCartButton';
import { Loader } from '@/libs/ui/Loader/Loader';

export interface AddToCartProps {
  productOfferId: string;
  minIncrement: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const AddToCart = ({
  productOfferId,
  minIncrement,
  size = 'md',
  className = '',
}: AddToCartProps) => {
  const { addToCart, offersMapData } = useCartStore();
  const [error, setError] = useState('');
  const [showIncrementInfo, setShowIncrementInfo] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const { isLoading = false, quantity = 0 } =
    offersMapData[productOfferId] ?? {};
  const hasItemsInCart = quantity > 0;

  const showTrashIcon = quantity <= minIncrement;

  const handleAddToCart = useCallback(
    (quantity: number) => {
      addToCart({
        offers: [
          {
            productOfferId,
            quantity,
          },
        ],
        onError: (message) => {
          setError(message);
        },
      });
    },
    [productOfferId, addToCart],
  );

  const handleUpdateValue = useCallback(() => {
    if (!inputRef.current?.value) {
      return;
    }

    setShowIncrementInfo(false);
    let newValue = +inputRef.current.value;

    if (newValue <= 0) {
      inputRef.current.value = quantity.toString();
      return;
    }

    if (newValue % minIncrement !== 0) {
      newValue = (Math.floor(newValue / minIncrement) || 1) * minIncrement;
      inputRef.current.value = newValue.toString();
    }

    setError('');
    handleAddToCart(newValue);
  }, [inputRef, quantity, minIncrement, handleAddToCart]);

  const handleInitialAddToCart = useCallback(() => {
    const initialQuantity = Math.max(1, minIncrement);
    handleAddToCart(initialQuantity);
  }, [minIncrement, handleAddToCart]);

  const handleInputKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.code === 'Enter') {
        handleUpdateValue();
      }
    },
    [handleUpdateValue],
  );

  const handleControlClick = useCallback(
    (increment: number) => () => {
      if (!inputRef.current) {
        return;
      }

      inputRef.current.value = (+inputRef.current.value + increment).toString();
      handleUpdateValue();
    },
    [handleUpdateValue],
  );

  const handleRemove = useCallback(() => {
    handleAddToCart(0);
  }, [handleAddToCart]);

  const inputSize: InputProps['size'] =
    size === 'lg' ? 'lg' : size === 'sm' ? 'sm' : 'md';

  useEffect(() => {
    if (inputRef.current && hasItemsInCart) {
      inputRef.current.value = quantity.toString();
    }
  }, [quantity, hasItemsInCart]);

  if (!hasItemsInCart) {
    return (
      <div className={className}>
        <AddToCartButton
          isLoading={isLoading}
          onClick={handleInitialAddToCart}
          height={size === 'sm' ? 'sm' : 'normal'}
        />
      </div>
    );
  }

  return (
    <div className={className}>
      <Popover opened={showIncrementInfo}>
        <Popover.Target>
          <div className="relative flex items-center">
            <Button
              aria-label={
                showTrashIcon ? 'Remove item from cart' : 'Decrease quantity'
              }
              type="button"
              variant="unstyled"
              className="absolute top-1/2 left-2 z-10 flex h-8 -translate-y-1/2 items-center"
              onClick={
                showTrashIcon
                  ? handleRemove
                  : handleControlClick(minIncrement * -1)
              }
              disabled={isLoading}
            >
              <Icon
                name={showTrashIcon ? 'trash' : 'minus'}
                aria-hidden={true}
              />
            </Button>
            {isLoading ? (
              <div className="relative flex h-9 w-full items-center justify-center rounded border border-gray-300 bg-gray-50">
                <div className="flex animate-spin items-center justify-center">
                  <Loader color="#FFF" size="xs" />
                </div>
              </div>
            ) : (
              <Input
                ref={inputRef}
                defaultValue={quantity}
                key={quantity}
                align="center"
                onKeyDown={handleInputKeyDown}
                onBlur={handleUpdateValue}
                onFocus={() => setShowIncrementInfo(minIncrement > 1)}
                error={error}
                type="number"
                min="0"
                step={minIncrement}
                size={inputSize}
                className="px-2.5"
              />
            )}

            <Button
              aria-label="Increase quantity"
              type="button"
              variant="unstyled"
              className="absolute top-1/2 right-2 z-10 flex h-8 -translate-y-1/2 items-center"
              onClick={handleControlClick(minIncrement)}
              disabled={isLoading}
            >
              <Icon name="plus" aria-hidden={true} />
            </Button>
          </div>
        </Popover.Target>

        <Popover.Dropdown>
          This product requires increments of {minIncrement}
        </Popover.Dropdown>
      </Popover>
    </div>
  );
};
