import { PropsWithChildren, ReactNode } from 'react';
import * as RadixTooltip from '@radix-ui/react-tooltip';
import { mergeClasses } from '@/utils/tailwind';

export interface TooltipProps {
  label: string | ReactNode;
  children: ReactNode;
  className?: string;
  // Radix positioning props
  side?: 'top' | 'right' | 'bottom' | 'left';
  align?: 'start' | 'center' | 'end';
  sideOffset?: number;
  alignOffset?: number;
  // Provider props
  delayDuration?: number;
  skipDelayDuration?: number;
  disableHoverableContent?: boolean;
}

export interface WithTooltipProps extends PropsWithChildren {
  label: string | ReactNode;
  className?: string;
  side?: 'top' | 'right' | 'bottom' | 'left';
  align?: 'start' | 'center' | 'end';
  sideOffset?: number;
  alignOffset?: number;
}

export const Tooltip = (props: TooltipProps) => {
  const {
    children,
    label,
    className,
    side = 'bottom',
    align = 'center',
    sideOffset = 5,
    alignOffset,
    delayDuration = 300,
    skipDelayDuration = 300,
    disableHoverableContent = false,
    ...rest
  } = props;

  if (!label) {
    return <>{children}</>;
  }

  return (
    <RadixTooltip.Provider
      delayDuration={delayDuration}
      skipDelayDuration={skipDelayDuration}
      disableHoverableContent={disableHoverableContent}
    >
      <RadixTooltip.Root>
        <RadixTooltip.Trigger asChild>
          <div className="inline-flex items-center justify-center">
            {children}
          </div>
        </RadixTooltip.Trigger>
        <RadixTooltip.Portal>
          <RadixTooltip.Content
            side={side}
            align={align}
            sideOffset={sideOffset}
            alignOffset={alignOffset}
            className={mergeClasses(
              'animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-[400] max-w-[200px] rounded bg-slate-800 px-3 py-1 text-sm break-words text-white opacity-90',
              className,
            )}
            {...rest}
          >
            {label}
          </RadixTooltip.Content>
        </RadixTooltip.Portal>
      </RadixTooltip.Root>
    </RadixTooltip.Provider>
  );
};

export const withTooltip = (
  children: ReactNode,
  tooltip?: WithTooltipProps,
) => {
  const { label, ...rest } = tooltip || {};

  return label ? (
    <Tooltip label={label} {...rest}>
      {children}
    </Tooltip>
  ) : (
    children
  );
};
