# Modal Component

A Radix UI-based Modal component that provides a drop-in replacement for the previous Mantine-based Modal while maintaining full backwards compatibility.

## Migration Summary

This Modal component has been migrated from Mantine UI to Radix UI primitives while maintaining:
- ✅ **Full backwards compatibility** - All existing code continues to work without changes
- ✅ **Same API** - All props and interfaces remain identical
- ✅ **Same behavior** - Modal state management via `useModalStore` works exactly the same
- ✅ **Same styling** - Visual appearance matches the original (3rem padding, white background, centered)
- ✅ **Enhanced accessibility** - Improved screen reader support via Radix UI primitives

## Usage

The Modal component works exactly the same as before:

```tsx
import { Modal } from '@/components'; // Still works!
// or
import { Modal } from '@/libs/ui/Modal'; // New location

function MyComponent() {
  return (
    <Modal name="MY_MODAL" withCloseButton title="My Modal">
      <div>Modal content here</div>
    </Modal>
  );
}
```

## Props

All original Mantine Modal props are supported:

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `name` | `string` | **required** | Unique identifier for the modal (used with modal store) |
| `size` | `string \| number` | `'md'` | Modal size - supports Mantine sizes (xs, sm, md, lg, xl, auto) or custom values |
| `centered` | `boolean` | `true` | Whether to center the modal |
| `withCloseButton` | `boolean` | `false` | Show close button in top-right corner |
| `closeOnClickOutside` | `boolean` | `true` | Close modal when clicking outside |
| `closeOnEscape` | `boolean` | `true` | Close modal when pressing Escape |
| `onClose` | `() => void` | - | Custom close handler (falls back to modal store) |
| `title` | `ReactNode` | - | Modal title |
| `customClasses` | `object` | `{}` | Custom CSS classes for different parts |
| `className` | `string` | - | Additional CSS class for the modal content |
| `overlayProps` | `object` | `{}` | Props for the modal overlay |

## Modal Store Integration

The component integrates seamlessly with the existing `useModalStore`:

```tsx
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { MODAL_NAME } from '@/constants';

function MyComponent() {
  const { openModal } = useModalStore();
  
  const handleOpenModal = () => {
    openModal({ name: MODAL_NAME.MY_MODAL, someData: 'value' });
  };
  
  return (
    <>
      <button onClick={handleOpenModal}>Open Modal</button>
      <Modal name={MODAL_NAME.MY_MODAL}>
        Modal content
      </Modal>
    </>
  );
}
```

## Styling

The component uses Tailwind CSS classes and maintains the original styling:
- **Body padding**: 3rem (equivalent to `p-12`)
- **Background**: White
- **Positioning**: Centered by default
- **Animations**: Smooth fade and zoom transitions

### Custom Classes

You can still use `customClasses` for backwards compatibility:

```tsx
<Modal 
  name="MY_MODAL"
  customClasses={{
    body: 'custom-body-class',
    header: 'custom-header-class',
    overlay: 'custom-overlay-class'
  }}
>
  Content
</Modal>
```

## Accessibility

The new implementation provides enhanced accessibility features:
- Proper ARIA labels and descriptions
- Screen reader support
- Focus management
- Keyboard navigation

## Migration Notes

- **No breaking changes** - All existing code continues to work
- **Import paths unchanged** - `import { Modal } from '@/components'` still works
- **Props interface identical** - No need to update prop usage
- **Behavior preserved** - Modal store integration works exactly the same
- **Styling maintained** - Visual appearance is identical

## Technical Details

- **Built with**: `@radix-ui/react-dialog`
- **Styling**: Tailwind CSS
- **State management**: Existing `useModalStore` (Zustand)
- **Accessibility**: Full WCAG compliance via Radix primitives
- **Bundle size**: Smaller than Mantine equivalent

## Testing

The component includes comprehensive tests covering:
- Modal opening/closing based on store state
- Close button functionality
- Accessibility features
- Props handling

Run tests with:
```bash
npm test -- src/libs/ui/Modal/Modal.test.tsx
```
