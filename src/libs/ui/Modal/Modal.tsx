import { PropsWithChildren, ReactNode } from 'react';
import * as RadixDialog from '@radix-ui/react-dialog';
import { mergeClasses } from '@/utils/tailwind';
import { useModalStore } from '@/apps/shop/stores/useModalStore';

// Mantine Modal size mapping to Tailwind classes
const MODAL_SIZE_CLASSES = {
  xs: 'max-w-xs',
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  auto: 'max-w-fit',
  '70%': 'max-w-[70%]',
  '100%': 'max-w-full',
} as const;

// Define the interface that matches the original Mantine Modal props
export interface ModalProps extends PropsWithChildren {
  // Core Modal props
  name: string;

  // Mantine Modal props we need to support
  size?: keyof typeof MODAL_SIZE_CLASSES | string | number;
  centered?: boolean;
  withCloseButton?: boolean;
  closeOnClickOutside?: boolean;
  closeOnEscape?: boolean;
  trapFocus?: boolean;
  returnFocus?: boolean;

  // Event handlers
  onClose?: () => void;

  // Styling props
  className?: string;
  overlayProps?: {
    className?: string;
    opacity?: number;
    color?: string;
    backgroundOpacity?: number;
  };

  // Custom classes for backwards compatibility
  customClasses?: Partial<{
    root: string;
    inner: string;
    content: string;
    header: string;
    body: string;
    title: string;
    close: string;
    overlay: string;
  }>;

  // Additional Mantine props that might be used
  title?: ReactNode;
  padding?: string | number;
  radius?: string | number;
  shadow?: string;
  fullScreen?: boolean;
  lockScroll?: boolean;
  withinPortal?: boolean;
  portalProps?: {
    target?: HTMLElement | string;
  };
  transitionProps?: {
    duration?: number;
    transition?: string;
  };

  // Any other props that might be passed through
  [key: string]: any;
}

export const Modal = ({
  children,
  name,
  size = 'md',
  centered = true,
  withCloseButton = false,
  closeOnClickOutside = true,
  closeOnEscape = true,
  trapFocus = true,
  returnFocus = true,
  onClose,
  className,
  overlayProps = {},
  customClasses = {},
  title,
  padding,
  fullScreen = false,
  lockScroll = true,
  withinPortal = true,
  portalProps,
  transitionProps,
  ...rest
}: ModalProps) => {
  const { modalOption, closeModal } = useModalStore();

  // Determine if modal should be open based on the name matching logic from original
  const isOpen = Boolean(modalOption.name) && modalOption.name.includes(name);

  const handleClose = () => {
    if (onClose) {
      onClose();
      return;
    }
    closeModal();
  };

  // Handle size prop - support both predefined sizes and custom values
  const getSizeClass = () => {
    if (typeof size === 'string' && size in MODAL_SIZE_CLASSES) {
      return MODAL_SIZE_CLASSES[size as keyof typeof MODAL_SIZE_CLASSES];
    }
    if (typeof size === 'string') {
      return `max-w-[${size}]`;
    }
    if (typeof size === 'number') {
      return `max-w-[${size}px]`;
    }
    return MODAL_SIZE_CLASSES.md;
  };

  // Build overlay classes
  const overlayClasses = mergeClasses(
    // Base overlay styles
    'fixed inset-0 z-50 bg-black/50',
    // Animation classes
    'data-[state=open]:animate-in data-[state=closed]:animate-out',
    'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
    // Custom overlay classes
    customClasses.overlay,
    overlayProps.className
  );

  // Build content classes
  const contentClasses = mergeClasses(
    // Base content styles
    'fixed z-50 grid w-full gap-4 rounded-lg border bg-white shadow-lg',
    // Size classes
    getSizeClass(),
    // Positioning - centered by default like original
    centered && 'left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]',
    // Full screen support
    fullScreen && 'h-screen w-screen max-w-none rounded-none',
    // Animation classes
    'duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out',
    'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
    'data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
    'data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%]',
    'data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]',
    // Custom content classes
    customClasses.content,
    className
  );

  // Build body classes - replicate the original 3rem padding
  const bodyClasses = mergeClasses(
    'p-12', // 3rem padding like original
    'bg-white',
    customClasses.body
  );

  // Build header classes if title is provided
  const headerClasses = mergeClasses(
    'flex flex-col space-y-1.5 text-center sm:text-left',
    customClasses.header
  );

  // Build close button classes
  const closeButtonClasses = mergeClasses(
    'absolute right-4 top-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-slate-950 focus:ring-offset-2 disabled:pointer-events-none',
    customClasses.close
  );

  const DialogComponent = withinPortal ? RadixDialog.Portal : 'div';
  const dialogProps = withinPortal && portalProps?.target
    ? { container: typeof portalProps.target === 'string'
        ? document.querySelector(portalProps.target)
        : portalProps.target
      }
    : {};

  return (
    <RadixDialog.Root
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          handleClose();
        }
      }}
    >
      <DialogComponent {...(withinPortal ? dialogProps : {})}>
        <RadixDialog.Overlay className={overlayClasses} />
        <RadixDialog.Content
          className={contentClasses}
          onEscapeKeyDown={closeOnEscape ? undefined : (e) => e.preventDefault()}
          onPointerDownOutside={closeOnClickOutside ? undefined : (e) => e.preventDefault()}
          {...rest}
        >
          {title ? (
            <div className={headerClasses}>
              <RadixDialog.Title className={mergeClasses('text-lg font-semibold leading-none tracking-tight', customClasses.title)}>
                {title}
              </RadixDialog.Title>
            </div>
          ) : (
            <RadixDialog.Title className="sr-only">
              Modal
            </RadixDialog.Title>
          )}

          {withCloseButton && (
            <RadixDialog.Close className={closeButtonClasses}>
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
              <span className="sr-only">Close</span>
            </RadixDialog.Close>
          )}

          <RadixDialog.Description className="sr-only">
            Modal content
          </RadixDialog.Description>

          <div className={bodyClasses}>
            {children}
          </div>
        </RadixDialog.Content>
      </DialogComponent>
    </RadixDialog.Root>
  );
};
