import type {
  OrderHistoryDetailItemType,
  OrderHistoryPromotion,
} from '@/libs/orders/types';
import { OrderStatus } from '../../../OrderStatus/OrderStatus';
import { FEATURE_FLAGS, PROMO_TYPE } from '@/constants';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';
import { Dollar } from '@/libs/products/components/Dollar/Dollar';
import { ProductImage } from '@/libs/products/components/ProductImage/ProductImage';
import { OrderHistoryItemContent } from '../OrderHistoryItemContent/OrderHistoryItemContent';

export const OrderHistoryBuyGetPromotionItem = ({
  promotion,
  items,
}: {
  promotion: OrderHistoryPromotion;
  items: OrderHistoryDetailItemType[];
}) => {
  if (!promotion || items.length === 0) return null;

  return (
    <div className="rounded-sm border border-gray-200/70 bg-gray-50">
      <div className="flex gap-3 rounded-t-sm bg-gray-100 p-4">
        <Dollar
          toolTipLabel={`Promotion Type: ${PROMO_TYPE[promotion.type]}`}
        />
        <span className="text-sm font-medium">
          Promotion <span className="mx-1 text-gray-400/80">•</span>{' '}
          <span className="text-xs">{PROMO_TYPE[promotion.type]}</span>:{' '}
          <span className="text-xs text-gray-500">{promotion.name}</span>
        </span>
      </div>

      <div className="grid gap-4 divide-y divide-gray-200/80 p-4 pt-6">
        {items.map((item) => (
          <div
            key={item.id}
            className="grid w-full grid-cols-[110px_1fr] gap-1 pb-4"
          >
            <ProductImage
              product={{ ...item.product, productOfferId: item.productOfferId }}
              className="h-24 w-full"
            />
            <div className="grid w-full grid-cols-[1fr_150px] gap-5">
              <OrderHistoryItemContent item={item} />
              <div className="text-right">
                <p className="mb-1 text-xs font-medium text-gray-500/70">
                  Status
                </p>
                {item.status && (
                  <OrderStatus status={item.status} align="right" />
                )}
                {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
                  <Button className="mt-2 max-h-8 max-w-20">
                    <Icon name="cartSummary" className="mr-1.25" />
                    <Icon name="plus" size={'0.8rem'} />
                  </Button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
