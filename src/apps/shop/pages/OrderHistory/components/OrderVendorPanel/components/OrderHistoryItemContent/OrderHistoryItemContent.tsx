import { Link } from 'react-router-dom';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { getPriceString } from '@/utils';
import type { OrderHistoryDetailItemType } from '@/libs/orders/types';
import { Tooltip } from '@/libs/ui/Tooltip';
import { t } from 'i18next';
import { getMeasure } from '../../../../utils/getMeasure';

interface OrderHistoryItemContentProps {
  item: OrderHistoryDetailItemType;
}

export const OrderHistoryItemContent = ({
  item,
}: OrderHistoryItemContentProps) => {
  const isFreeItem = item.totalPrice === '0';
  const measure = getMeasure({ item });

  return (
    <div className="ml-4 flex h-full flex-col justify-between">
      <div>
        <p className="mb-0.5 text-xs font-medium text-gray-600/70">
          Order ID: {item.orderNumber}
        </p>
        {item.product.offers.length > 0 ? (
          <Link
            to={getProductUrl(item.product.id, item.productOfferId)}
            className="cursor-pointer font-medium text-gray-800 no-underline hover:underline"
          >
            {item.product.name}
          </Link>
        ) : (
          <Tooltip label={t('client.orderHistoryItem.notAvailable')}>
            <span
              tabIndex={0}
              aria-disabled="true"
              className="font-medium text-gray-800"
            >
              {item.product.name}
            </span>
          </Tooltip>
        )}
        {!!measure && <p className="text-xs text-gray-600">UOM: {measure}</p>}
      </div>
      <div className="mt-2 flex items-center text-xs">
        <p className="text-gray-600">
          Quantity:{' '}
          <span className="font-bold text-gray-800">{item.quantity}</span>
        </p>
        <div className="divider-v mx-4" />
        <p className="text-gray-600">
          Price:{' '}
          {isFreeItem ? (
            <span className="font-bold text-green-700">FREE</span>
          ) : (
            <span className="font-bold text-gray-800">
              {getPriceString(item.unitPrice)}
            </span>
          )}
        </p>
        <div className="divider-v mx-4" />
        <p className="text-gray-600">
          Net Total:{' '}
          {isFreeItem ? (
            <span className="font-bold text-green-700">FREE</span>
          ) : (
            <span className="font-bold text-gray-800">
              {getPriceString(item.totalPrice)}
            </span>
          )}
        </p>
      </div>
    </div>
  );
};
