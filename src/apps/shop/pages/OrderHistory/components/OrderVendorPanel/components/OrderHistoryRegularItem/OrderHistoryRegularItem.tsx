import { FEATURE_FLAGS } from '@/constants';
import { OrderStatus } from '../../../OrderStatus/OrderStatus';
import { Button } from '@/libs/ui/Button/Button';
import PlusIcon from '@/assets/images/plus.svg?react';
import type { OrderHistoryDetailItemType } from '@/libs/orders/types';
import { OrderHistoryItemContent } from '../OrderHistoryItemContent/OrderHistoryItemContent';
import { Icon } from '@/libs/icons/Icon';
import { ProductImage } from '@/libs/products/components/ProductImage/ProductImage';

export const OrderHistoryRegularItem = ({
  item,
}: {
  item: OrderHistoryDetailItemType;
}) => {
  return (
    <div className="grid w-full grid-cols-[110px_1fr] gap-1 pb-4">
      <ProductImage
        product={{ ...item.product, productOfferId: item.productOfferId }}
        className="h-24 w-full"
      />
      <div className="grid w-full grid-cols-[1fr_150px] gap-5">
        <OrderHistoryItemContent item={item} />
        <div className="text-right">
          <p className="mb-1 text-xs font-medium text-gray-500/70">Status</p>
          {item.status && <OrderStatus status={item.status} align="right" />}
          {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
            <Button className="mt-2">
              <Icon name="cartSummary" size={'1.3rem'} />
              <PlusIcon />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
