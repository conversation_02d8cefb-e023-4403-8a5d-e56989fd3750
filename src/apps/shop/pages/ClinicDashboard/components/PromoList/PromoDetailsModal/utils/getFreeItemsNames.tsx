import React from 'react';
import { BenefitType } from '@/types/common';
import { LocalCartItem } from '../PromoDetailsModal';

export const getFreeItemsNames = ({
  cartItems,
  freeBenefit,
  freeItemsCount,
}: {
  cartItems: LocalCartItem[];
  freeBenefit: BenefitType;
  freeItemsCount: number;
}) => {
  if (!freeBenefit.freeProductOffer) {
    return (
      <>
        {cartItems.map((item, index) => (
          <div
            key={index}
          >{`${item.freeItemsCount} Free: ${item.offer.name}`}</div>
        ))}
      </>
    );
  }

  return (
    <span>{`${freeItemsCount} Free: ${freeBenefit.freeProductOffer.name}`}</span>
  );
};
