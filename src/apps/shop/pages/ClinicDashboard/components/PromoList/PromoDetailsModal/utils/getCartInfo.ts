import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { LocalCartItem } from '../PromoDetailsModal';

export const getCartInfo = (cartItems: LocalCartItem[]) => {
  return cartItems.reduce(
    (info, item) => {
      const { salePrice } = getProductOfferComputedData(item.offer);
      if (!salePrice) return info;
      return {
        subtotalPaidItems: info.subtotalPaidItems + salePrice * item.quantity,
        subtotalFreeItems:
          info.subtotalFreeItems +
          item.freeOfferSalePrice * item.freeItemsCount,
        paidItemsCount: info.paidItemsCount + item.quantity,
        freeItemsCount: info.freeItemsCount + item.freeItemsCount,
      };
    },
    {
      subtotalPaidItems: 0,
      subtotalFreeItems: 0,
      paidItemsCount: 0,
      freeItemsCount: 0,
    },
  );
};
