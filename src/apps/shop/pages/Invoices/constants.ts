export const INVOICES_CONSTANTS = {
  // Monite API configuration - from environment variables
  MONITE_API_URL:
    import.meta.env.VITE_MONITE_API_URL || 'https://api.sandbox.monite.com/v1',
  MONITE_ENVIRONMENT: import.meta.env.VITE_MONITE_ENVIRONMENT || 'sandbox',
  MONITE_API_VERSION: import.meta.env.VITE_MONITE_API_VERSION || '2023-09-01',

  // Theme configuration aligned with Highfive app design system
  MONITE_THEME: {
    borderRadius: 8, // Matches app's border-radius: 0.5rem
    spacing: 16, // Matches app's spacing patterns
    colors: {
      // Primary: Yellow theme from app's primary buttons
      primary: '#F1C40F', // var(--mantine-color-yellow-5) equivalent
      // Secondary: Light blue theme from app's secondary buttons
      secondary: '#74B9FF', // var(--mantine-color-light-blue-5) equivalent
      // Neutral: Dark colors from app's text
      neutral: '#6C7B7F', // var(--mantine-color-dark-5) equivalent
      // Info: Light blue from app's info elements
      info: '#0984E3', // var(--mantine-color-light-blue-8) equivalent
      // Success: Green from app's success states
      success: '#00B894', // var(--mantine-color-green-11) equivalent
      // Warning: Yellow from app's warning states
      warning: '#FDCB6E', // var(--mantine-color-yellow-4) equivalent
      // Error: Red from app's error states
      error: '#E17055', // var(--mantine-color-red-2) equivalent
      // Background: White from app's backgrounds
      background: '#FFFFFF', // var(--mantine-color-white) equivalent
      // Text: Dark from app's text colors
      text: '#2D3436', // var(--mantine-color-dark-8) equivalent
    },
    typography: {
      // Font family matching app's system fonts
      fontFamily:
        '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif',
      fontSize: 16, // Base font size matching app
      h1: {
        fontSize: 32, // 2rem - matches app's main titles
        fontWeight: 700, // Bold weight from app
        lineHeight: '40px',
      },
      h2: {
        fontSize: 24, // 1.5rem - matches app's section headings
        fontWeight: 700, // Bold weight from app
        lineHeight: '32px',
      },
      h3: {
        fontSize: 20, // 1.25rem - matches app's subsection headings
        fontWeight: 600, // Semi-bold weight from app
        lineHeight: '28px',
      },
      body1: {
        fontSize: 16, // 1rem - matches app's body text
        fontWeight: 400, // Normal weight from app
        lineHeight: '24px',
      },
      body2: {
        fontSize: 14, // 0.875rem - matches app's smaller text
        fontWeight: 400, // Normal weight from app
        lineHeight: '20px',
      },
    },
  },

  // Localization aligned with Highfive app terminology
  MONITE_LOCALE: {
    code: 'en-US',
    messages: {
      // Main navigation and sections
      Payables: 'Invoices',
      Counterparts: 'Vendors',
      Sales: 'Sales',
      Invoices: 'Invoices',

      // Form labels and actions
      'Counterpart Name': 'Vendor Name',
      'Create New': 'Create New',
      'Add New': 'Add New',
      Edit: 'Edit',
      Delete: 'Delete',
      Save: 'Save',
      Cancel: 'Cancel',

      // Status and states
      Draft: 'Draft',
      Sent: 'Sent',
      Paid: 'Paid',
      Overdue: 'Overdue',
      Cancelled: 'Cancelled',

      // Common terms
      Amount: 'Amount',
      Date: 'Date',
      'Due Date': 'Due Date',
      Status: 'Status',
      Description: 'Description',
      Notes: 'Notes',
    },
  },

  // Component-specific settings
  COMPONENT_SETTINGS: {
    payables: {
      pageSizeOptions: [15, 50, 100],
    },
  },
} as const;
