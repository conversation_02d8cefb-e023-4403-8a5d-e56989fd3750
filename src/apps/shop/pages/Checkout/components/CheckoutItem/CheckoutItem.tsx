import { CartItemType } from '@/libs/cart/types';
import { getPriceString } from '@/utils';
import { getMeasure } from '../../../OrderHistory/utils/getMeasure';

export const CheckoutItem = ({ item }: { item: CartItemType }) => {
  const { quantity, product, subtotal, price } = item;
  const measure = getMeasure({ item });

  return (
    <div className="grid grid-cols-[1fr_auto] gap-10 p-3">
      <div className="flex flex-col gap-2">
        <span className="font-medium text-gray-900">{product?.name}</span>
        {!!measure && (
          <span className="text-xs text-gray-500">
            UOM:
            <span className="ml-0.5 font-medium text-[#344054]">{measure}</span>
          </span>
        )}
      </div>

      <div className="flex items-center">
        <span className="text-gray-500">
          Quantity:{' '}
          <span className="font-medium text-gray-900">{quantity}</span>
        </span>
        <div className="divider-v"></div>

        <span className="text-gray-500">
          Unit:{' '}
          <span className="font-medium text-gray-900">
            {getPriceString(price)}
          </span>
        </span>
        <div className="divider-v"></div>

        <span className="text-gray-500">
          Net Total:{' '}
          <span className="font-medium text-gray-900">
            {getPriceString(subtotal)}
          </span>
        </span>
      </div>
    </div>
  );
};
