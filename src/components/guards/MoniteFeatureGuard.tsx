import { ReactNode } from 'react';
import { useIsMoniteEnabled } from '@/libs/monite/hooks/useMoniteFeatureFlag';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { Loader } from '@/libs/ui/Loader/Loader';
import { MoniteFeatureNotAvailable } from '@/components/errors/MoniteFeatureNotAvailable';

interface MoniteFeatureGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Route guard component that checks if Monite features are enabled for the current clinic.
 * If not enabled, shows a 404-style "Feature Not Available" page.
 * If checking, shows a loading state.
 */
export const MoniteFeatureGuard = ({
  children,
  fallback,
}: MoniteFeatureGuardProps) => {
  const activeClinic = useAccountStore((state) => state.activeClinic);

  const { isMoniteEnabled, isLoading, error } = useIsMoniteEnabled(
    activeClinic?.id,
  );

  // Show loading while checking feature flag
  if (isLoading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <Loader size="3rem" />
          <p className="mt-4 text-gray-600">Checking access...</p>
        </div>
      </div>
    );
  }

  // Show 404-style page if feature is not enabled or there's an error
  if (!isMoniteEnabled || error) {
    return fallback || <MoniteFeatureNotAvailable />;
  }

  // Feature is enabled, render the protected content
  return <>{children}</>;
};
