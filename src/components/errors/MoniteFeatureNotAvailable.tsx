import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/atoms/Button/Button';

/**
 * 404-style component shown when a user tries to access Monite features
 * that are not enabled for their clinic.
 */
export const MoniteFeatureNotAvailable = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1); // Go back to previous page
  };

  const handleGoHome = () => {
    navigate('/'); // Go to dashboard/home
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="max-w-md text-center">
        <div className="mb-8">
          {/* 404-style icon */}
          <div className="mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-gray-200">
            <svg
              className="h-12 w-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>

          <h2 className="mb-4 text-xl font-semibold text-gray-600">
            {t('errors.featureNotAvailable.subtitle', 'Feature Not Available')}
          </h2>

          <p className="mb-8 text-gray-600">
            {t(
              'errors.featureNotAvailable.message',
              'The feature you are trying to access is not currently available for your clinic. Please contact your administrator for more information.',
            )}
          </p>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
          <Button
            onClick={handleGoBack}
            variant="secondary"
            className="w-full sm:w-auto"
          >
            {t('errors.featureNotAvailable.goBack', 'Go Back')}
          </Button>

          <Button
            onClick={handleGoHome}
            variant="primary"
            className="w-full sm:w-auto"
          >
            {t('errors.featureNotAvailable.goHome', 'Go to Dashboard')}
          </Button>
        </div>

        <div className="mt-8 text-sm text-gray-500">
          <p>
            {t(
              'errors.featureNotAvailable.contactInfo',
              'If you believe this is an error, please contact your system administrator.',
            )}
          </p>
        </div>
      </div>
    </div>
  );
};
